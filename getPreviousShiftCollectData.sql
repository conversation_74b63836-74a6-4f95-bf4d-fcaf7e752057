-- PostgreSQL 存储过程：获取上个班次的采集点数据
-- 参数：
-- p_orgcode: 登录人机构代码（父级机构）
-- p_datetime: 查询日期时间（可选，默认当前时间）
-- p_where_unit: 额外的核算对象id过滤条件（可选）

CREATE OR REPLACE FUNCTION get_previous_shift_collect_data(
    p_orgcode VARCHAR(200),
    p_datetime VARCHAR(19) DEFAULT NULL,
    p_where_unit TEXT DEFAULT NULL
)
RETURNS TABLE (
    collect_point_id VARCHAR,
    ipt_id VARCHAR,
    collect_point_text VARCHAR,
    collect_point_val VARCHAR,
    input_comp_type VARCHAR,
    input_options VARCHAR,
    input_time VARCHAR,
    job_input_time VARCHAR,
    shift_class_id VARCHAR,
    shift_class_name VARCHAR,
    shift_start_time VARCHAR,
    shift_end_time VARCHAR,
    shift_date VARCHAR
) AS $$
DECLARE
    current_shift_record RECORD;
    previous_shift_record RECORD;
    sql_query TEXT;
    query_datetime VARCHAR(19);
    query_date VARCHAR(10);
    query_time VARCHAR(8);
BEGIN
    -- 处理输入参数
    IF p_datetime IS NULL OR LENGTH(TRIM(p_datetime)) = 0 THEN
        query_datetime := TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS');
    ELSE
        query_datetime := p_datetime;
    END IF;

    query_date := SUBSTRING(query_datetime FROM 1 FOR 10);
    query_time := SUBSTRING(query_datetime FROM 12 FOR 8);

    -- 第一步：获取当前班次信息
    -- 查找当前时间点的班次，p_orgcode是班次数据中objid的父级机构
    SELECT
        orgcode, modelid, dbrq, objid, objname, objtype,
        shiftclassid, shiftclassname, sbsj, xbsj, tjrq, gzsj, progid
    INTO current_shift_record
    FROM shift_data
    WHERE orgcode = p_orgcode
      AND (
          -- 同一天的班次，当前时间在上下班时间范围内
          (dbrq = query_date AND query_time BETWEEN sbsj AND xbsj)
          OR
          -- 跨日班次：前一天的班次，下班时间小于上班时间，且当前时间大于等于上班时间
          (dbrq = TO_CHAR(TO_DATE(query_date, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND xbsj < sbsj
           AND query_time >= sbsj)
          OR
          -- 跨日班次：当天的班次，下班时间小于上班时间，且当前时间小于等于下班时间
          (dbrq = query_date
           AND xbsj < sbsj
           AND query_time <= xbsj)
      )
    ORDER BY dbrq DESC, sbsj DESC
    LIMIT 1;

    -- 如果没有找到当前班次，返回空结果
    IF current_shift_record IS NULL THEN
        RAISE NOTICE '未找到当前班次: orgcode=%, datetime=%', p_orgcode, query_datetime;
        RETURN;
    END IF;

    -- 第二步：获取上个班次信息
    -- 上个班次的下班时间应该等于当前班次的上班时间
    SELECT
        orgcode, modelid, dbrq, objid, objname, objtype,
        shiftclassid, shiftclassname, sbsj, xbsj, tjrq, gzsj, progid
    INTO previous_shift_record
    FROM shift_data
    WHERE orgcode = p_orgcode
      AND (
          -- 情况1：同一天的上个班次，下班时间等于当前班次上班时间
          (dbrq = current_shift_record.dbrq AND xbsj = current_shift_record.sbsj)
          OR
          -- 情况2：前一天的班次，下班时间等于当前班次上班时间（跨日情况）
          (dbrq = TO_CHAR(TO_DATE(current_shift_record.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND xbsj = current_shift_record.sbsj)
          OR
          -- 情况3：如果找不到精确匹配，则找同一天最接近的前一个班次
          (dbrq = current_shift_record.dbrq AND sbsj < current_shift_record.sbsj)
          OR
          -- 情况4：如果当天没有前一个班次，则找前一天的最后一个班次
          (dbrq = TO_CHAR(TO_DATE(current_shift_record.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD')
           AND NOT EXISTS (
               SELECT 1 FROM shift_data sd2
               WHERE sd2.orgcode = p_orgcode
                 AND sd2.dbrq = current_shift_record.dbrq
                 AND sd2.sbsj < current_shift_record.sbsj
           ))
      )
    ORDER BY
        CASE WHEN xbsj = current_shift_record.sbsj THEN 1 ELSE 2 END,  -- 优先精确匹配
        dbrq DESC,
        sbsj DESC
    LIMIT 1;

    -- 如果没有找到上个班次，返回空结果
    IF previous_shift_record IS NULL THEN
        RAISE NOTICE '未找到上个班次: orgcode=%', p_orgcode;
        RETURN;
    END IF;

    -- 第三步：构建查询采集点数据的SQL
    sql_query := 'SELECT
        mx.collect_point_id::VARCHAR,
        mx.ipt_id::VARCHAR,
        mx.collect_point_text::VARCHAR,
        mx.collect_point_val::VARCHAR,
        mx.input_comp_type::VARCHAR,
        mx.input_options::VARCHAR,
        mx.input_time::VARCHAR,
        mx.job_input_time::VARCHAR,
        $1::VARCHAR as shift_class_id,
        $2::VARCHAR as shift_class_name,
        $3::VARCHAR as shift_start_time,
        $4::VARCHAR as shift_end_time,
        $5::VARCHAR as shift_date
    FROM acctobj_inputmx mx
    WHERE mx.tmused = 1
      AND EXISTS (
          SELECT ai.id
          FROM acctobj_input ai
          WHERE ai.tmused = 1 ';

    -- 根据是否有额外的单位条件来构建不同的SQL
    IF p_where_unit IS NOT NULL AND LENGTH(TRIM(p_where_unit)) > 0 THEN
        sql_query := sql_query ||
            'AND ai.acctobj_id ' || p_where_unit || ' ' ||
            'AND ai.bcdm = $1 ' ||
            'AND ai.sbsj = $3 ' ||
            'AND ai.team_id = $6 ' ||
            'AND ai.id = mx.ipt_id
          )
    ORDER BY mx.collect_point_id';

        -- 执行查询并返回结果
        RETURN QUERY EXECUTE sql_query
        USING previous_shift_record.shiftclassid,
              previous_shift_record.shiftclassname,
              previous_shift_record.sbsj,
              previous_shift_record.xbsj,
              previous_shift_record.dbrq,
              previous_shift_record.objid;
    ELSE
        sql_query := sql_query ||
            'AND ai.acctobj_id = $6 ' ||
            'AND ai.bcdm = $1 ' ||
            'AND ai.sbsj = $3 ' ||
            'AND ai.team_id = $6 ' ||
            'AND ai.id = mx.ipt_id
          )
    ORDER BY mx.collect_point_id';

        -- 执行查询并返回结果
        RETURN QUERY EXECUTE sql_query
        USING previous_shift_record.shiftclassid,
              previous_shift_record.shiftclassname,
              previous_shift_record.sbsj,
              previous_shift_record.xbsj,
              previous_shift_record.dbrq,
              previous_shift_record.objid;
    END IF;

END;
$$ LANGUAGE plpgsql;

-- 使用示例：
-- 1. 基本调用（使用当前时间）
-- SELECT * FROM get_previous_shift_collect_data('ORG001');

-- 2. 指定时间调用
-- SELECT * FROM get_previous_shift_collect_data('ORG001', '2024-01-15 14:30:00');

-- 3. 带额外单位条件的调用
-- SELECT * FROM get_previous_shift_collect_data('ORG001', '2024-01-15 14:30:00', 'IN (''UNIT001'', ''UNIT002'')');

-- 简化版存储过程（只返回采集点数据，不包含班次信息）
CREATE OR REPLACE FUNCTION get_previous_shift_collect_data_simple(
    p_orgcode VARCHAR(200),
    p_datetime VARCHAR(19) DEFAULT NULL,
    p_where_unit TEXT DEFAULT NULL
)
RETURNS TABLE (
    collect_point_id VARCHAR,
    ipt_id VARCHAR,
    collect_point_text VARCHAR,
    collect_point_val VARCHAR,
    input_comp_type VARCHAR,
    input_options VARCHAR,
    input_time VARCHAR,
    job_input_time VARCHAR
) AS $$
DECLARE
    sql_query TEXT;
    query_datetime VARCHAR(19);
    query_date VARCHAR(10);
    query_time VARCHAR(8);
BEGIN
    -- 处理输入参数
    IF p_datetime IS NULL OR LENGTH(TRIM(p_datetime)) = 0 THEN
        query_datetime := TO_CHAR(CURRENT_TIMESTAMP, 'YYYY-MM-DD HH24:MI:SS');
    ELSE
        query_datetime := p_datetime;
    END IF;

    query_date := SUBSTRING(query_datetime FROM 1 FOR 10);
    query_time := SUBSTRING(query_datetime FROM 12 FOR 8);

    -- 构建基础查询
    sql_query := '
        SELECT
            mx.collect_point_id::VARCHAR,
            mx.ipt_id::VARCHAR,
            mx.collect_point_text::VARCHAR,
            mx.collect_point_val::VARCHAR,
            mx.input_comp_type::VARCHAR,
            mx.input_options::VARCHAR,
            mx.input_time::VARCHAR,
            mx.job_input_time::VARCHAR
        FROM acctobj_inputmx mx
        WHERE mx.tmused = 1
          AND EXISTS (
              SELECT ai.id
              FROM acctobj_input ai
              INNER JOIN (
                  -- 获取上个班次信息的子查询
                  SELECT prev_shift.*
                  FROM (
                      -- 先获取当前班次
                      SELECT
                          curr.shiftclassid as curr_shiftclassid,
                          curr.sbsj as curr_sbsj,
                          curr.objid as curr_objid,
                          curr.dbrq as curr_dbrq
                      FROM shift_data curr
                      WHERE curr.orgcode = $1
                        AND (
                            -- 同一天的班次，当前时间在上下班时间范围内
                            (curr.dbrq = $2 AND $3 BETWEEN curr.sbsj AND curr.xbsj)
                            OR
                            -- 跨日班次处理
                            (curr.dbrq = TO_CHAR(TO_DATE($2, ''YYYY-MM-DD'') - INTERVAL ''1 day'', ''YYYY-MM-DD'')
                             AND curr.xbsj < curr.sbsj
                             AND $3 >= curr.sbsj)
                            OR
                            (curr.dbrq = $2
                             AND curr.xbsj < curr.sbsj
                             AND $3 <= curr.xbsj)
                        )
                      ORDER BY curr.dbrq DESC, curr.sbsj DESC
                      LIMIT 1
                  ) current_shift,
                  LATERAL (
                      -- 基于当前班次查找上个班次
                      SELECT
                          s.shiftclassid,
                          s.sbsj,
                          s.objid
                      FROM shift_data s
                      WHERE s.orgcode = $1
                        AND (
                            -- 下班时间等于当前班次上班时间
                            s.xbsj = current_shift.curr_sbsj
                            OR
                            -- 如果找不到精确匹配，找最接近的前一个班次
                            (s.dbrq = current_shift.curr_dbrq AND s.sbsj < current_shift.curr_sbsj)
                            OR
                            (s.dbrq = TO_CHAR(TO_DATE(current_shift.curr_dbrq, ''YYYY-MM-DD'') - INTERVAL ''1 day'', ''YYYY-MM-DD''))
                        )
                      ORDER BY
                          CASE WHEN s.xbsj = current_shift.curr_sbsj THEN 1 ELSE 2 END,
                          s.dbrq DESC,
                          s.sbsj DESC
                      LIMIT 1
                  ) prev_shift
              ) ps ON 1=1
              WHERE ai.tmused = 1';

    -- 根据是否有额外的单位条件来构建不同的SQL
    IF p_where_unit IS NOT NULL AND LENGTH(TRIM(p_where_unit)) > 0 THEN
        sql_query := sql_query || '
                AND ai.acctobj_id ' || p_where_unit || '
                AND ai.bcdm = ps.shiftclassid
                AND ai.sbsj = ps.sbsj
                AND ai.team_id = ps.objid
                AND ai.id = mx.ipt_id
          )
        ORDER BY mx.collect_point_id';

        -- 执行查询
        RETURN QUERY EXECUTE sql_query USING p_orgcode, query_date, query_time;
    ELSE
        sql_query := sql_query || '
                AND ai.acctobj_id = ps.objid
                AND ai.bcdm = ps.shiftclassid
                AND ai.sbsj = ps.sbsj
                AND ai.team_id = ps.objid
                AND ai.id = mx.ipt_id
          )
        ORDER BY mx.collect_point_id';

        -- 执行查询
        RETURN QUERY EXECUTE sql_query USING p_orgcode, query_date, query_time;
    END IF;

END;
$$ LANGUAGE plpgsql;

-- 使用示例：
-- 1. 简化版基本调用（使用当前时间）
-- SELECT * FROM get_previous_shift_collect_data_simple('ORG001');

-- 2. 简化版指定时间调用
-- SELECT * FROM get_previous_shift_collect_data_simple('ORG001', '2024-01-15 14:30:00');

-- 3. 简化版带额外单位条件的调用
-- SELECT * FROM get_previous_shift_collect_data_simple('ORG001', '2024-01-15 14:30:00', 'IN (''UNIT001'', ''UNIT002'')');
