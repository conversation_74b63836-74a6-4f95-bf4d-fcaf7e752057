package com.yunhesoft.accountTools.controller;


import com.mongodb.lang.Nullable;
import com.yunhesoft.accountTools.entity.dto.AccountConfigQueryDto;
import com.yunhesoft.accountTools.entity.dto.AccountConfigSaveDto;
import com.yunhesoft.accountTools.entity.dto.CheckDeletePermissionDto;
import com.yunhesoft.accountTools.entity.dto.RtdbDataDto;
import com.yunhesoft.accountTools.entity.po.DigitalLedger;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerExtendRow;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerModule;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerTime;
import com.yunhesoft.accountTools.entity.vo.ComboVo;
import com.yunhesoft.accountTools.service.IAccountConfigService;
import com.yunhesoft.accountTools.service.impl.AccountConfigServiceImpl;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.tds.entity.po.TdataSource;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.entity.po.TdsoutPara;
import com.yunhesoft.system.tds.service.IDataSourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/accountTools/accountConfig")
@Api(tags = "表单台账设置")
public class AccountConfigController extends BaseRestController {


    @Autowired
    private IAccountConfigService configService;

    @Autowired
    private IDataSourceService tdsServ;

    @Autowired
    private IShiftService shiftService;
    //-------------------------------- 表单台账设置 ↓ ----------------------------------

//    @ApiOperation("查询数据源tds数据(未使用)")
//    @RequestMapping(
//        value = {"getTdsData"},
//        method = {RequestMethod.POST}
//    )
//    public Res<?> getTdsData(@RequestBody TdsQueryDto param) {
//
//        JSONArray tdsData = tdsServ.getTDSData(param);
//        JSONArray data = tdsData.getJSONObject(0).getJSONArray("data");
//        Map<String, String> stringStringMap = AccountConfigServiceImpl.parseInParams(param.getInParaAlias());
//        if((data == null || data.isEmpty()) && StringUtils.isNotEmpty(stringStringMap.get("ledgerInitType"))) {
//            if ("time".equals(stringStringMap.get("ledgerInitType"))) {
//                JSONArray initDatas = configService.queryLedgerTimeData(param);
////              conf = accountServ.getAccountConf(getDSAlias());//配置信息
//                data.addAll(initDatas);
//            }else{
//                // 扩展行
//            }
//        }
//        return Res.OK(tdsData);
//    }

    /**
     * 保存存储台账时间批次初始化信息
     *
     * @return
     */
    @RequestMapping(value = "/queryRtdbDatas", method = RequestMethod.POST)
    @ApiOperation("存储台账时间批次初始化信息")
    public Res<?> queryRtdbDatas(@RequestBody RtdbDataDto rtdbDataDto) {
        Map<String, Map<String, String>> tagValues = configService.getTagValues(rtdbDataDto);
        return Res.OK(tagValues);
    }

    /**
     * 保存存储台账时间批次初始化信息
     *
     * @return
     */
    @RequestMapping(value = "/insertLedgerTime", method = RequestMethod.POST)
    @ApiOperation("存储台账时间批次初始化信息")
    public Res<?> insertLedgerTime(@RequestBody List<DigitalLedgerTime> lists) {
        return configService.insertLedgerTime(lists);
    }

    /**
     * 查询台账时间批次初始化信息
     *
     * @return
     */
    @RequestMapping(value = "/queryLedgerTime", method = RequestMethod.POST)
    @ApiOperation("查询台账时间批次初始化信息")
    public Res<?> queryLedgerTime(@RequestParam String ledgerId) {
        return Res.OK(configService.queryLedgerTime(ledgerId));
    }

    /**
     * 检查删除按钮权限
     *
     * @param queryDto
     * @return
     */
    @RequestMapping(value = "/checkDeletePermission", method = RequestMethod.POST)
    @ApiOperation("检查删除按钮权限")
    public Res<?> checkDeletePermission(@RequestBody CheckDeletePermissionDto checkDeletePermissionDto) {
        return Res.OK(configService.checkDeletePermission(checkDeletePermissionDto));
    }

    /**
     * 获取表单台账设置数据
     *
     * @param queryDto
     * @return
     */
    @RequestMapping(value = "/getFormAccountConfigObj", method = RequestMethod.POST)
    @ApiOperation("获取表单台账设置数据")
    public Res<?> getFormAccountConfigObj(@RequestBody AccountConfigQueryDto queryDto) {
        Res<DigitalLedger> res = new Res<DigitalLedger>();
        DigitalLedger obj = configService.getFormAccountConfigObj(queryDto);
        res.setResult(obj);
        return res;
    }

    @RequestMapping(value = "/getFormAccountConfigList", method = RequestMethod.POST)
    @ApiOperation("获取表单台账设置数据")
    public Res<?> getFormAccountConfigList(@RequestBody AccountConfigQueryDto queryDto) {
        List<DigitalLedger> dataList = configService.getDataList(queryDto);
        if (StringUtils.isEmpty(dataList)) {
            return Res.OK(Collections.emptyList());
        } else {
            return Res.OK(dataList);
        }
    }

    /**
     * 保存表单台账设置数据
     *
     * @param saveDto
     * @return
     */
    @RequestMapping(value = "/saveFormAccountConfigData", method = RequestMethod.POST)
    @ApiOperation("保存表单台账设置数据")
    public Res<?> saveFormAccountConfigData(@RequestBody AccountConfigSaveDto saveDto) {
        return Res.OK(configService.saveFormAccountConfigData(saveDto));
    }

    /**
     * 获取表单台账模型下拉框数据
     *
     * @param queryDto
     * @return
     */
    @RequestMapping(value = "/getTaizModelCombList", method = RequestMethod.POST)
    @ApiOperation("获取表单台账模型下拉框数据")
    public Res<?> getTaizModelCombList(@RequestBody AccountConfigQueryDto queryDto) {
        Res<List<ComboVo>> res = new Res<List<ComboVo>>();
        List<ComboVo> list = configService.getTaizModelCombList(queryDto);
        res.setResult(list);
        return res;
    }

    //-------------------------------- 台账模型设置 ↓ ----------------------------------

    /**
     * 获取台账模型数据
     *
     * @param queryDto
     * @return
     */
    @RequestMapping(value = "/getLedgerModuleList", method = RequestMethod.POST)
    @ApiOperation("获取台账模型数据")
    public Res<?> getLedgerModuleList(@RequestBody AccountConfigQueryDto queryDto) {
        Res<List<DigitalLedgerModule>> res = new Res<List<DigitalLedgerModule>>();
        Pagination<?> page = null;
        if (queryDto != null) {
            Integer pageSize = queryDto.getPageSize();
            if (pageSize != null && pageSize > 0) {
                Integer pageNum = queryDto.getPageNum() == null ? 1 : queryDto.getPageNum();
                page = Pagination.create(pageNum, pageSize);
            }
            if (page != null) {
                queryDto.setPage(page);
            }
        }
        List<DigitalLedgerModule> list = configService.getLedgerModuleList(queryDto);
        res.setResult(list);
        if (page != null) {
            res.setTotal(page.getTotal());
        }
        return res;
    }

    /**
     * 保存台账模型数据
     *
     * @param saveDto
     * @return
     */
    @RequestMapping(value = "/saveLedgerModuleData", method = RequestMethod.POST)
    @ApiOperation("保存台账模型数据")
    public Res<?> saveLedgerModuleData(@RequestBody AccountConfigSaveDto saveDto) {
        return Res.OK(configService.saveLedgerModuleData(saveDto));
    }

    @ApiOperation(value = "新增默认时间点")
    @RequestMapping(value = "/defaultTime", method = { RequestMethod.POST })
    public Res<String> defaultTime(@RequestParam("ledgerModuleId") String ledgerModuleId,
                                   @RequestParam("activityDate") String activityDate,
                                   @Nullable @RequestParam("inParaAlias") String inParaAlias) {
        Map<String, String> stringStringMap = AccountConfigServiceImpl.parseInParams(inParaAlias);
        String orgId = SysUserHolder.getCurrentUser().getOrgId();
        if(StringUtils.isEmpty(orgId)) {
            return Res.OK(null);
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("ledgerModuleId",ledgerModuleId);
        paramMap.put("bc",stringStringMap.get("bc"));
        List<Date> timeList = new ArrayList<>();
        List<DigitalLedgerTime> initTimeList = configService.getInitTimeList(paramMap);
        if(StringUtils.isNotEmpty(initTimeList)){
            timeList = initTimeList
                    .stream()
                    .map(DigitalLedgerTime::getStartTime)
                    .sorted(Comparator.comparing(Date::getTime))
                    .collect(Collectors.toList());
        }
        //当前用户是否当班
        boolean currentShift = false;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now = new Date();
        ShiftForeignVo shiftByDateTime = shiftService.getShiftByDateTime(sdf.format(now), orgId);
        if(StringUtils.isNotEmpty(shiftByDateTime.getShiftClassCode())){
            currentShift = true;
        }
        String result = null;
        if(currentShift){
            result = activityDate;
        }else{
            //当班
            for (int i = 0; i < timeList.size(); i++) {
                Date date = timeList.get(i);
                if(date.getTime() > now.getTime()){
                    if(i == 0){
                        result = sdf.format(timeList.get(0));
                    }else{
                        result = sdf.format(timeList.get(i-1));
                    }
                    break;
                }else if(Objects.equals(date.getTime(),now.getTime())){
                    result = sdf.format(timeList.get(i));
                    break;
                }
            }
        }
        return Res.OK(result);
    }


    //-------------------------------- 台账扩展行初始化 ↓ ----------------------------------

    /**
     *	查询采集点作为表头数据列表
     * @return
     */
    @RequestMapping(value = "/getDotTitleList", method = RequestMethod.POST)
    @ApiOperation("查询采集点作为表头数据列表")
    public Res<?> getDotTitleList(@RequestParam String ledgerId) {
        return Res.OK(configService.getDotTitleList(ledgerId));
    }

    /**
     *	获取数据源下拉框列表
     * @return
     */
    @RequestMapping(value = "/getTdsCombList", method = RequestMethod.POST)
    @ApiOperation("获取数据源下拉框列表")
    public Res<?> getTdsCombList() {
        List<TdataSource> lists = configService.getTdsCombList();
        return Res.OK(lists);
    }

    /**
     *	获取数据源输入参数列表
     * @return
     */
    @RequestMapping(value = "/getTdsInParamList", method = RequestMethod.POST)
    @ApiOperation("获取数据源输入参数列表")
    public Res<?> getTdsInParamList(@RequestParam String tdsAlias) {
        List<TdsinPara> lists = configService.getTdsInParamList(tdsAlias);
        return Res.OK(lists);
    }

    /**
     *	获取数据源输出参数列表
     * @return
     */
    @RequestMapping(value = "/getTdsOutParamList", method = RequestMethod.POST)
    @ApiOperation("获取数据源输出参数列表")
    public Res<?> getTdsOutParamList(@RequestParam String tdsAlias) {
        List<TdsoutPara> lists = configService.getTdsOutParamList(tdsAlias);
        return Res.OK(lists);
    }

    /**
     *	获取台账扩展行初始化数据
     * @param queryDto
     * @return
     */
    @RequestMapping(value = "/getInitExtendRowList", method = RequestMethod.POST)
    @ApiOperation("获取台账扩展行初始化数据")
    public Res<?> getInitExtendRowList(@RequestBody AccountConfigQueryDto queryDto) {
        Res<List<DigitalLedgerExtendRow>> res = new Res<List<DigitalLedgerExtendRow>>();
        List<DigitalLedgerExtendRow> list = configService.getInitExtendRowList(queryDto);
        res.setResult(list);
        return res;
    }

    /**
     *	保存台账扩展行初始化数据
     * @param saveDto
     * @return
     */
    @RequestMapping(value = "/saveInitExtendRowData", method = RequestMethod.POST)
    @ApiOperation("保存台账扩展行初始化数据")
    public Res<?> saveInitExtendRowData(@RequestBody AccountConfigSaveDto saveDto) {
        return Res.OK(configService.saveInitExtendRowData(saveDto));
    }

}
