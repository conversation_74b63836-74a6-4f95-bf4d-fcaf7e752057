package com.yunhesoft.system.tools.files.controller;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.aliyuncs.utils.Base64Helper;
import com.deepoove.poi.util.PoitlIOUtils;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.tools.files.entity.dto.WordParamDto;
import com.yunhesoft.system.tools.files.entity.dto.WordQueryDto;
import com.yunhesoft.system.tools.files.entity.dto.WordSaveDto;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;
import com.yunhesoft.system.tools.files.entity.po.WordExpDs;
import com.yunhesoft.system.tools.files.entity.vo.FileInfo;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;
import com.yunhesoft.system.tools.files.tools.PoitlTools;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * 上传文件信息Controller
 * 
 * <AUTHOR>
 * @date 2021-05-06
 */
@Log4j2
@RestController
@Api(tags = "上传文件信息")
@RequestMapping("/system/files")
public class FilesInfoController extends BaseRestController {
	@Autowired
	private IFilesInfoService filesInfoService;
	@Autowired
	private PoitlTools pt2;

	/**
	 * 查询上传文件信息列表
	 */
	@ApiOperation(value = "上传文件信息 列表")
	@RequestMapping(value = "/list", method = { RequestMethod.GET })
	public Res<List<SysFilesInfo>> list(SysFilesInfo filesInfo) {
		Pagination<?> page = getRequestPagination();
		return filesInfoService.selectFilesInfoList(filesInfo, page);
	}

	/**
	 * 查询上传文件信息列表
	 */
	@ApiOperation(value = "上传文件信息 列表")
	@RequestMapping(value = "/listByIds", method = { RequestMethod.GET })
	public Res<List<FileInfo>> listByIds(String ids) {
		return filesInfoService.selectFilesInfoListByIds(ids);
	}

	/**
	 * 获取上传文件信息详细信息
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "获取上传文件信息 详细信息")
	@RequestMapping(value = "/query/{id}", method = { RequestMethod.GET })
	public Res<SysFilesInfo> getInfo(@PathVariable("id") String id) {
		return Res.OK(filesInfoService.selectFilesInfoById(id));
	}

	/**
	 * 新增上传文件信息
	 */
	@ApiOperation(value = "新增上传文件信息")
	@PostMapping(value = "/add")
	public Res<?> add(@RequestBody SysFilesInfo filesInfo) {
		return Res.OK(filesInfoService.insertFilesInfo(filesInfo));
	}

	/**
	 * 修改上传文件信息
	 */
	@ApiOperation(value = "修改上传文件信息")
	@PostMapping(value = "/edit")
	public Res<?> edit(@RequestBody SysFilesInfo filesInfo) {
		return Res.OK(filesInfoService.updateFilesInfo(filesInfo));
	}

	/**
	 * 删除上传文件信息
	 */
	@ApiOperation(value = "删除上传文件信息")
	@GetMapping("/delete/{ids}")
	public Res<?> remove(@PathVariable String[] ids) {
		return Res.OK(filesInfoService.deleteFile(ids));
	}

	/**
	 * 添加附件
	 */
	@ApiOperation(value = "上传附件")
	@RequestMapping(value = "/fileUpload", method = { RequestMethod.POST })
	public Res<?> fileUpload(@RequestParam("file") MultipartFile file, @RequestParam("moduleCode") String moduleCode) {
		SysFilesInfo obj = filesInfoService.saveFiles(file, moduleCode);
		FileInfo rv = new FileInfo();
		rv.setId(obj.getId());
		rv.setName(obj.getOldFileName());
		rv.setUrl(obj.getFileUrl());
		rv.setFileExt(obj.getFileExt());
		rv.setFileSize(obj.getFileSize());
		return Res.OK(rv);
	}

	/**
	 * 添加附件
	 */
	@ApiOperation(value = "上传多附件")
	@RequestMapping(value = "/filesUpload", method = { RequestMethod.POST })
	public Res<?> filesUpload(@RequestParam("file") MultipartFile[] files,
			@RequestParam("moduleCode") String moduleCode) {
		List<SysFilesInfo> list = filesInfoService.saveFiles(files, moduleCode);
		List<FileInfo> rlist = new ArrayList<FileInfo>();
		if (list != null && !list.isEmpty()) {
			for (SysFilesInfo obj : list) {
				FileInfo rv = new FileInfo();
				rv.setId(obj.getId());
				rv.setName(obj.getOldFileName());
				rv.setUrl(obj.getFileUrl());
				rv.setFileExt(obj.getFileExt());
				rv.setFileSize(obj.getFileSize());
				rlist.add(rv);
			}
		}

		return Res.OK(rlist);
	}

	@ApiOperation(value = "上传附件支持类型")
	@RequestMapping(value = "/fileType", method = { RequestMethod.GET })
	public Res<?> fileType() {
		return Res.OK(filesInfoService.getFileType());
	}

	@ApiOperation(value = "上传附件支持类型中文")
	@RequestMapping(value = "/fileTypeStr", method = { RequestMethod.GET })
	public Res<?> fileTypeStr() {
		return Res.OK(filesInfoService.getFileTypeStr());
	}

	@ApiOperation(value = "上传附件支持大小M")
	@RequestMapping(value = "/fileSize", method = { RequestMethod.GET })
	public Res<?> fileSize() {
		return Res.OK(filesInfoService.getFileSize());
	}

	/**
	 * 获取上传文件信息详细信息
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "刪除上传文件")
	@GetMapping(value = "/del/{id}")
	public Res<SysFilesInfo> delFile(@PathVariable("id") String id) {

		filesInfoService.deleteFile(id);

		/*
		 * SysFilesInfo obj = filesInfoService.selectFilesInfoById(id); if (obj != null)
		 * { File file = new File(obj.getFileAddress()); if (file.exists()) {
		 * file.delete(); } }
		 * 
		 */

		return Res.OK();
	}

	@SuppressWarnings({ "unchecked" })
	@ApiOperation(value = "根据id获取上传文件二进制流")
	@RequestMapping(value = "/getFileStream", method = { RequestMethod.POST })
	public Res<String> getFileStream(@RequestParam("id") String id) {
//		SysFilesInfo obj = filesInfoService.selectFilesInfoById(id);
		InputStream fis = filesInfoService.getFileSteam(id);
		String imgStr = null;
		if (fis == null) {
			return Res.OK(imgStr);
		}
//		if (obj != null) {
//			File file = new File(obj.getFileAddress());
//			if (file.exists()) {
		try {
//					FileInputStream fis = new FileInputStream(file);
			byte[] buffer = IOUtils.toByteArray(fis);
//					byte[] buffer = new byte[(int) file.length()];
//					int offset = 0;
//					int numRead = 0;
//					while (offset < buffer.length
//							&& (numRead = fis.read(buffer, offset, buffer.length - offset)) >= 0) {
//						offset += numRead;
//					}
//
//					if (offset != buffer.length) {
//						return Res.OK();
//					}
			fis.close();
			// Base64Helper encoder = new Base64Helper();
			imgStr = "data:image/jpeg;base64," + Base64Helper.encode(buffer);
		} catch (Exception e) {
			log.error("", e);
		}
//				FileInputStream inputStream = null;
//				try {
//				    inputStream = new FileInputStream(file);
//				    byte[] bytes = new byte[inputStream.available()];
////				    inputStream.read(bytes, 0, inputStream.available());
////				    //将byte[]转换成String类型
////				    String logo2=new String(bytes);
////				    log.info("imgbyte:"+logo2);
////				    inputStream.close();
//				    
//				    response.setContentType("image/jpeg");  
//				    response.setCharacterEncoding("UTF-8");  
//				    OutputStream outputSream = response.getOutputStream();
//				    outputSream.write(bytes);
//				    outputSream.flush();
//				} catch (Exception e) {
//				    log.error("",e);
//				}
//			}
//		}
		return Res.OK(imgStr);
	}

//	/**
//	 * 获取上传文件信息详细信息
//	 */
//	@ApiOperation(value = "word文件测试")
//	@GetMapping(value = "/test")
//	public Res<SysFilesInfo> test() {
//		PoitlTools pt = new PoitlTools();
//		String suffix = "doc";
//		File file = new File("D:/a2.docx");
//		log.info(pt.getWordTxt(file, suffix));
//		List<String> tlist = pt.getVarList(file, suffix);
//		
//		Map<String, Object> map = new HashMap<String, Object>();
//		int i=0;
//		for (String string : tlist) {
//			log.info("var:"+string);
//			if(string.indexOf(".")!=-1) {
//				String[] s = string.split("\\.");
//				if(map.containsKey(s[0])) {
//					((Map)map.get(s[0])).put(s[1], "nr"+(i++));
//				}else {
//					Map<String, String> tmap = new HashMap<String, String>();
//					tmap.put(s[1], "nr"+(i++));
//					map.put(s[0], tmap);
//				}
//			}else {
//				if(string.startsWith("#")) {
//					List<List<String>> data = null;
//					if(string.startsWith("#tds_")) {
//						try {
//							data = getDsVal(string.replace("#tds_", ""), "aaa");
//						} catch (Exception e) {
//							log.info("数据源解析出错，"+string);
//						}
//					}
//					map.put(string.substring(1), pt.getTabObj(data, false, 2, null));
//				}else {
//					map.put(string, "nr"+(i++));
//				}
//			}
//			
//		}
//		
//		List<XWPFTemplate> tplList = new ArrayList<XWPFTemplate>();
//		XWPFTemplate main = null; 
//		try {
//			main = pt.getWord(file, map);
////			main.writeToFile("D:/t.doc");
//		} catch (Exception e) {
//			log.error("",e);
//		}
//		
//		tplList.add(main);
//		File sfile = new File("D:/b.doc");
//		XWPFTemplate sub = pt.getWord(sfile, map);
//		tplList.add(sub);
//		NiceXWPFDocument doc = pt.combineWord(tplList);
//		
//		FileOutputStream out;
//		try {
//			out = new FileOutputStream("D:/new_doc.docx");
//			doc.write(out);
//			doc.close();
//			out.close();
//		} catch (Exception e) {
//			log.error("",e);
//		}
//		
//		return Res.OK("ok");
//	}
//	
//	private List<List<String>> getDsVal(String tdsAlias, String dataId) {
//		List<List<String>> rlist = new ArrayList<List<String>>();
//		TdsQueryDto param = new TdsQueryDto();
//		param.setTdsAlias(tdsAlias);
//		param.setInParaAlias("dataId="+dataId);
////		param.setInParaRawValue("dataId="+dataId);
//		JSONArray json = dsService.getTDSData(param);
//		if(json!=null && json.size() > 0) {
//			JSONObject jobj = (JSONObject)json.get(0);
//			//输出信息
//			JSONObject props = jobj.getJSONObject("props");
//			//列id及顺序
//			JSONObject colprop = props.getJSONObject("colprop");
//			//输出列配置信息
//			JSONArray cols = props.getJSONArray("cols");
//			//数据
//			JSONArray data = jobj.getJSONArray("data");
//			
//			List<String> colIdList = new ArrayList<String>();
//			List<String> titleList = new ArrayList<String>();
//			
//			for (Object val : cols) {
//				colIdList.add(String.valueOf(val));
//			}
//			
//			for (Iterator iterator = colIdList.iterator(); iterator.hasNext();) {
//				String colId = (String) iterator.next();
//				JSONObject colInfo = colprop.getJSONObject(colId);
//				boolean hidden = "true".equalsIgnoreCase(colInfo.getString("hidden"));
//				//隐藏字段列删除
//				if(hidden) {
//					iterator.remove();
//				}else {
//					String header = colInfo.getString("header");
//					titleList.add(header);
//				}
//			}
//			rlist.add(titleList);
//			
//			if(data.size() > 0) {
//				for (int i = 0,il=data.size(); i < il; i++) {
//					JSONObject dataobj = (JSONObject)data.get(i);
//					List<String> dlist = new ArrayList<String>();
//					for (String colId : colIdList) {
//						dlist.add(dataobj.getString(colId));
//					}
//					rlist.add(dlist);
//				}
//			}
//			
//		}
//		
//		return rlist;
//	}

	/**
	 * 下载文件
	 */
	@ApiOperation(value = "下载文件")
	@RequestMapping("/docDownLoadDemo")
	public void docDownLoad(HttpServletResponse response) {

		String templateAddress = "D:/word模板test3.docx";
		String picAddress = "d:\\图章1.png";
		String picBookMark = "my_bookmark";
		Integer picWidth = 60;
		Integer picHeight = 60;

		Map<String, Map<String, String>> paraMap = new HashMap<String, Map<String, String>>();

		NiceXWPFDocument doc = pt2.getWord(templateAddress, paraMap);
		pt2.addPicToDoc(doc, picAddress, picBookMark, picWidth, picHeight, 0, 0);

		response.setHeader("content-type", "application/octet-stream");
		response.setContentType("application/octet-stream");

		OutputStream out;
		try {
			out = response.getOutputStream();
			BufferedOutputStream bos = new BufferedOutputStream(out);
			doc.write(bos);
			bos.flush();
			out.flush();
			PoitlIOUtils.closeQuietlyMulti(doc, bos, out);
			doc.close();
			bos.close();
			out.close();
		} catch (Exception e) {
			log.error("", e);
		}
	}

	@ApiOperation(value = "保存word输入参数配置")
	@RequestMapping(value = "/saveWordInparaData", method = RequestMethod.POST)
	public Res<?> saveWordInparaData(@RequestBody WordSaveDto param) {
		return Res.OK(filesInfoService.saveReplaceCharData(param));
	}

	@ApiOperation(value = "查询word输入参数配置")
	@RequestMapping("/wordInparaConfList")
	public Res<List<WordExpDs>> wordInparaConfList(@RequestBody WordQueryDto param) {
		Res<List<WordExpDs>> res = new Res<List<WordExpDs>>();
		Pagination<?> page = null;
		if (param != null) {
			if (param.getPageSize() != null && param.getPageSize() > 0) {// 创建分页信息
				page = Pagination.create(param.getPageNum() == null ? 1 : param.getPageNum(), param.getPageSize());
			}
			List<WordExpDs> list = filesInfoService.queryWordList(param, page);// 检索数据
			if (page != null) {
				res.setTotal(page.getTotal());// 总数量赋值
			}
			res.setResult(list);
		}

		return res;
	}

	@ApiOperation(value = "查询word输入参数配置")
	@RequestMapping("/getTplPara")
	public Res<List<WordExpDs>> downWordPara(@RequestBody WordQueryDto param) {
		Res<List<WordExpDs>> res = new Res<List<WordExpDs>>();
		if (param != null) {
			List<WordExpDs> list = filesInfoService.queryDownWordPara(param);// 检索数据
			res.setResult(list);
		}
		return res;
	}

	/**
	 * 下载文件
	 */
	@ApiOperation(value = "下载文件")
	@RequestMapping("/docEnterpriseDownLoad")
	public void docDownLoad(HttpServletResponse response, @RequestBody WordParamDto dto) {
		List<String> tempWordList = new ArrayList<String>();
		NiceXWPFDocument template = filesInfoService.exportWordFile(dto);

		response.setHeader("content-type", "application/octet-stream");
		response.setContentType("application/octet-stream");

		OutputStream out;
		try {
			out = response.getOutputStream();
			BufferedOutputStream bos = new BufferedOutputStream(out);
			template.write(bos);
			bos.flush();
			out.flush();
			PoitlIOUtils.closeQuietlyMulti(template, bos, out);
			template.close();
			bos.close();
			out.close();
		} catch (Exception e) {
			log.error("", e);
		}
		template = null;
		if (tempWordList != null && tempWordList.size() > 0) {
			filesInfoService.clearTempWordFile(tempWordList);
			tempWordList = null;
		}
	}

	@ApiOperation(value = "上传本地文件到文件服务器")
	@GetMapping("/uploadLocalFilesToFileSystem")
	public Res<?> uploadLocalFilesToFileSystem() {
		filesInfoService.uploadLocalFilesToFileSystem();
		return Res.OK();
	}

	/**
	 * 根据id获取图片文件二进制流
	 * 
	 * @param id 图片id
	 * @return
	 */
	@ApiOperation(value = "根据id获取图片文件二进制流")
	@RequestMapping(value = "/getImgageStream", method = { RequestMethod.POST, RequestMethod.GET })
	public Object getImgageStream(@RequestParam("id") String id) {
		InputStream fis = filesInfoService.getFileSteam(id);
		if (fis != null) {
			ServletOutputStream out = null;
			try {
				response.setContentType("multipart/form-data");
				out = response.getOutputStream();
				// 读取文件流
				int len = 0;
				byte[] buffer = new byte[1024 * 10];
				while ((len = fis.read(buffer)) != -1) {
					out.write(buffer, 0, len);
				}
				out.flush();
			} catch (Exception e) {
				log.error("", e);
			} finally {
				try {
					out.close();
				} catch (IOException e) {
					log.error("", e);
				}
				try {
					fis.close();
				} catch (IOException e) {
					log.error("", e);
				}
			}
		}
		return null;

	}

}
