package com.yunhesoft.system.tools.files.service.impl;

import com.aliyuncs.utils.Base64Helper;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.impl.TreeServiceImpl;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Update;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.files.entity.dto.FileManageQueryDto;
import com.yunhesoft.system.tools.files.entity.po.SysFileManage;
import com.yunhesoft.system.tools.files.entity.po.SysFilesInfo;
import com.yunhesoft.system.tools.files.entity.vo.FileManageVo;
import com.yunhesoft.system.tools.files.entity.vo.FileManegeDataClass;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;
import com.yunhesoft.system.tools.files.service.ISysFileManage;
import com.yunhesoft.system.tools.files.service.ISysFileService;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class FileManageImpl implements ISysFileManage {

    public static final String MODEL_ID = "FILE_MANAGE";

    @Autowired
    private ISysFileService fs;

    @Autowired
    private IFilesInfoService fileInfoSrv;

    @Autowired
    private EntityService srv;

    @Autowired
    private TreeServiceImpl treeSrv;

    @Override
    public List<FileManageVo> queryDataList(FileManageQueryDto dto) {
        List<FileManageVo> result = new ArrayList<>();
        FileManegeDataClass dataClassEnum = dto.getDataClass();
        if (FileManegeDataClass.system.equals(dataClassEnum)) {
            //系统文件查询
            return this.querySystemFileList(dto);
        }

        String moduleCode = dto.getModuleCode();
        String dataClass = dataClassEnum.toString();
        SysUser currentUser = SysUserHolder.getCurrentUser();
        String dataGroupId = null;
        if (FileManegeDataClass.personal.equals(dto.getDataClass())) {
            //查询个人
            dataGroupId = currentUser.getId();
        } else if (FileManegeDataClass.tenantpublic.equals(dto.getDataClass())) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        }

        if ("root".equals(dto.getPid())) {
            //查询层级1
            dto.setPid(getTenantRoot(moduleCode, dataClass, dataGroupId).getId());
        }

        //其余情况为文件管理中的查询
        Where where = Where.create();
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getTmused, dto.getTmused());
        where.eq(SysFileManage::getDataGroupId, dataGroupId);
        where.eq(SysFileManage::getDelFlag, dto.getDelflag());
        if (StringUtils.isNotEmpty(dto.getPid())) {
            //根据父id查
            where.eq(SysFileManage::getPid, dto.getPid());
        }
        if (dto.getFileType() > 0) {
            //指定类型
            where.eq(SysFileManage::getFileType, dto.getFileType());
        }
        if (StringUtils.isNotEmpty(dto.getName())) {
            //按名称查询
//            where.eq(SysFileManage::getName, dto.getName());
            where.like(SysFileManage::getName, dto.getName());
        }

        Order order = getOrder(dto);
        /*
        Order order = Order.create();
        order.orderByDesc(SysFileManage::getFileType);
        if ("time".equals(dto.getOrder()) && dto.getDesc() == 1) {
            order.orderByDesc(SysFileManage::getCreateTime);
        } else {
            if ("name".equals(dto.getOrder()) && dto.getDesc() == 0) {
                //名称正序
                order.orderByAsc(SysFileManage::getName);
            } else if ("name".equals(dto.getOrder()) && dto.getDesc() == 1) {
                //名称倒序
                order.orderByDesc(SysFileManage::getName);
            } else if ("size".equals(dto.getOrder()) && dto.getDesc() == 0) {
                //大小正序
                order.orderByAsc(SysFileManage::getFileSize);
            } else if ("size".equals(dto.getOrder()) && dto.getDesc() ==1) {
                //大小倒序
                order.orderByDesc(SysFileManage::getFileSize);
            }
            //默认创建时间正序
            order.orderByAsc(SysFileManage::getCreateTime);
        }
        */

        List<SysFileManage> list = srv.queryData(SysFileManage.class, where, order, null);
        if (StringUtils.isNotEmpty(list)) {
            List<String> urlIdList = new ArrayList<>();
            for (SysFileManage file : list) {
                FileManageVo fileManageVo = ObjUtils.copyTo(file, FileManageVo.class);
                result.add(fileManageVo);
                if (StringUtils.isNotEmpty(dto.getName()) && StringUtils.isEmpty(dto.getPid())) {
                    //全局按名称查
//                    urlIdList.add(file.get)
                }
            }
        }
        return result;
    }

    /**
     * 查询根节点
     * @param moduleCode
     * @param dataClass
     * @param dataGroupId
     * @return
     */
    private SysFileManage getTenantRoot (String moduleCode, String dataClass, String dataGroupId) {
        Where rootWhere = Where.create();
        rootWhere.eq(SysFileManage::getTmused, 1);
        rootWhere.eq(SysFileManage::getModelId, MODEL_ID);
        rootWhere.eq(SysFileManage::getPid, "root");
        SysFileManage root = srv.rawQueryObjectByWhere(SysFileManage.class, rootWhere);
        List<SysFileManage> insertList = new ArrayList<>();
        if (root == null) {
            root = new SysFileManage();
            root.setId(TMUID.getUID());
            root.setPid("root");
            root.setModelId(MODEL_ID);
//            root.setModuleCode(moduleCode);
//            root.setDataClass(dataClass);
//            root.setDataGroupId(dataGroupId);
            root.setDelFlag(0);
            root.setTmused(1);
            insertList.add(root);
            srv.insert(root);
        }

        Where where = Where.create();
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getModelId, MODEL_ID);
        where.eq(SysFileManage::getPid, root.getId());
        where.eq(SysFileManage::getModuleCode, moduleCode);
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getDataGroupId, dataGroupId);
        SysFileManage tenantroot = srv.rawQueryObjectByWhere(SysFileManage.class, where);
        if (tenantroot == null) {
            tenantroot = new SysFileManage();
            tenantroot.setId(TMUID.getUID());
            tenantroot.setPid(root.getId());
            tenantroot.setModelId(MODEL_ID);
            tenantroot.setModuleCode(moduleCode);
            tenantroot.setDataClass(dataClass);
            tenantroot.setDataGroupId(dataGroupId);
            tenantroot.setDelFlag(0);
            tenantroot.setTmused(1);
            insertList.add(tenantroot);
            treeSrv.append(SysFileManage.class, MODEL_ID, tenantroot.getPid(), tenantroot);
//            srv.insert(tenantroot);
        }

//        if (StringUtils.isNotEmpty(insertList)) {
//            srv.insertBatch(insertList);
//        }
        return tenantroot;
    }

    /**
     * 获取排序
     * @param dto
     * @return
     */
    private Order getOrder (FileManageQueryDto dto) {
        Order order = Order.create();
        FileManegeDataClass dataClassEnum = dto.getDataClass();
        boolean sysflag = FileManegeDataClass.system.equals(dataClassEnum);
        if (!sysflag) {
            //非系统文件（默认文件夹在前）
            order.orderByDesc(SysFileManage::getFileType);
        }
        if ("time".equals(dto.getOrder()) && dto.getDesc() == 1) {
            //创建时间倒序
            if (sysflag) {
                //系统文件
                order.orderByDesc(SysFilesInfo::getCreateTime);
            } else {
                //非系统文件
                order.orderByDesc(SysFileManage::getCreateTime);
            }
        } else {
            if ("name".equals(dto.getOrder()) && dto.getDesc() == 0) {
                //名称正序
                if (sysflag) {
                    //系统文件
                    order.orderByAsc(SysFilesInfo::getNewFileName);
                } else {
                    //非系统文件
                    order.orderByAsc(SysFileManage::getName);
                }
            } else if ("name".equals(dto.getOrder()) && dto.getDesc() == 1) {
                //名称倒序
                if (sysflag) {
                    //系统文件
                    order.orderByDesc(SysFilesInfo::getNewFileName);
                } else {
                    //非系统文件
                    order.orderByDesc(SysFileManage::getName);
                }
            } else if ("size".equals(dto.getOrder()) && dto.getDesc() == 0) {
                //大小正序
                if (sysflag) {
                    //系统文件
                    order.orderByAsc(SysFilesInfo::getFileSize);
                } else {
                    //非系统文件
                    order.orderByAsc(SysFileManage::getFileSize);
                }
            } else if ("size".equals(dto.getOrder()) && dto.getDesc() ==1) {
                //大小倒序
                if (sysflag) {
                    //系统文件
                    order.orderByDesc(SysFilesInfo::getFileSize);
                } else {
                    //非系统文件
                    order.orderByDesc(SysFileManage::getFileSize);
                }
            }

            //默认创建时间正序
            if (sysflag) {
                //系统文件
                order.orderByAsc(SysFilesInfo::getCreateTime);
            } else {
                //非系统文件
                order.orderByAsc(SysFileManage::getCreateTime);
            }
        }
        return order;
    }

    /**
     * 查询系统内文件
     * @return
     */
    private List<FileManageVo> querySystemFileList(FileManageQueryDto dto) {
        Where where = Where.create();
        if (StringUtils.isNotEmpty(dto.getName())) {
            where.like(SysFilesInfo::getNewFileName, dto.getName());
        }
        Order order = getOrder(dto);
        List<SysFilesInfo> list = srv.queryData(SysFilesInfo.class, where, order, null);
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        List<FileManageVo> result = new ArrayList<>();
        for (SysFilesInfo fileInfo : list) {
            FileManageVo file = new FileManageVo();
            file.setFileType(1);
            file.setId(fileInfo.getId());
            file.setName(fileInfo.getNewFileName() + (StringUtils.isEmpty(fileInfo.getFileExt()) ? "" : "."+fileInfo.getFileExt()));
            file.setFileExt(fileInfo.getFileExt());
            file.setFileUrl(fileInfo.getFileUrl());
            file.setFileDiskAddress(fileInfo.getFileAddress());
            file.setCreateTime(fileInfo.getCreateTime());
            file.setCreateBy(fileInfo.getCreateBy());
            result.add(file);
        }
        return result;
    }

    @Override
    public String saveFolder(SysFileManage folder) {
        String moduleCode = folder.getModuleCode();
        String dataClass = folder.getDataClass();
        SysUser currentUser = SysUserHolder.getCurrentUser();
        String dataGroupId = null;
        if (FileManegeDataClass.personal.toString().equals(dataClass)) {
            //查询个人
            dataGroupId = currentUser.getId();
        } else if (FileManegeDataClass.tenantpublic.toString().equals(dataClass)) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        }
        String id = folder.getId();
        if (StringUtils.isEmpty(folder.getPid())) {
            folder.setPid("root");
        }
        if ("root".equals(folder.getPid())) {
            folder.setPid(getTenantRoot(moduleCode, dataClass, dataGroupId).getId());
        }
        if (StringUtils.isEmpty(id)) {
            //新增
            folder.setId(TMUID.getUID());
            folder.setTmused(1);
            folder.setModelId(MODEL_ID);
            folder.setDelFlag(0);
            folder.setDataGroupId(dataGroupId);
//            folder.setFileUrl(folderUrl);

//            srv.insert(folder);
            treeSrv.append(SysFileManage.class, MODEL_ID, folder.getPid(), folder);
//            treeSrv.append(SysFileManage.class, folder.getDataGroupId(), null, folder);
//            treeSrv.getRoot(SysFileManage.class, "FILE_MANAGE");
//            treeSrv.getNode(SysFileManage.class, "FILE_MANAGE", "test1");
//            try {
//                treeSrv.correctTree(SysFileManage.class, "FILE_MANAGE");
//            } catch (Exception e) {
//                throw new RuntimeException(e);
//            }
//            treeSrv.append(SysFileManage.class, folder, folder);

        } else {
            //修改
//            srv.rawUpdateByIdDisableTenant(folder);
            treeSrv.updateNode(SysFileManage.class, MODEL_ID, folder);
        }

        return null;
    }

    private String getFileRootUrl (String dataClass) {
        SysUser currentUser = SysUserHolder.getCurrentUser();
        if (FileManegeDataClass.personal.toString().equals(dataClass)) {
            return "/filemanage/personal/user_"+currentUser.getId().toLowerCase();

        } else if (FileManegeDataClass.tenantpublic.toString().equals(dataClass)) {
            return  "/filemanage/tenantpublic/tenant_"+currentUser.getTenant_id().toLowerCase();
        }
        return null;
    }

    @Override
    public String deleteDataByIdList(List<String> idList) {
        List<SysFileManage> dataList = srv.queryData(SysFileManage.class, Where.create().in(SysFileManage::getId, idList.toArray()), null, null);
        if (StringUtils.isEmpty(dataList)) {
            throw new RuntimeException("传入参数有误");
        }
        dataList.forEach(item -> item.setTmused(0));
        srv.updateBatch(dataList);
        for (String id : idList) {
            treeSrv.delete(SysFileManage.class, true, MODEL_ID, id);
        }

        /*
        Where where = Where.create();
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getDataClass, dataList.get(0).getDataClass());
        where.eq(SysFileManage::getDataGroupId, dataList.get(0).getDataGroupId());
        List<SysFileManage> allList = srv.queryData(SysFileManage.class, where, null, null);
        if (StringUtils.isEmpty(allList)) {
            throw new RuntimeException("找不到记录");
        }
        Map<String, List<SysFileManage>> group = allList.stream().collect(Collectors.groupingBy(SysFileManage::getPid));
        List<SysFileManage> result = new ArrayList<>();
        for (SysFileManage file : dataList) {
//            deleteDataByChildrenGroup(result, file, group);
            treeSrv.delete(SysFileManage.class, true, MODEL_ID, file.getId());
        }
        */
//        srv.updateBatch(result);
//        srv.rawUpdate(SysFileManage.class, Update.create(SysFileManage::getTmused, 0), Where.create().in(SysFileManage::getId, idList.toArray()));
        return null;
    }

    private void deleteDataByChildrenGroup (List<SysFileManage> result, SysFileManage item, Map<String, List<SysFileManage>> group) {
        item.setTmused(0);
        result.add(item);
        List<SysFileManage> children = group.get(item.getId());
        if (StringUtils.isEmpty(children)) {
            return;
        }
        for (SysFileManage child : children) {
            deleteDataByChildrenGroup(result, child, group);
        }
    }

    private MultipartFile generateNewMultipartFile (MultipartFile file, SysFileManage fm) {
        String fileName = file.getOriginalFilename();
        String fileOriginalName = fileName;
        String fileExt = null;
        if (StringUtils.isNotEmpty(fileName) && fileName.contains(".")) {
            fileOriginalName = fileName.substring(0, fileName.lastIndexOf("."));
            fileExt = fileName.substring(fileName.lastIndexOf(".") + 1);
        }
        fileName = fileOriginalName+"_"+System.currentTimeMillis() + (StringUtils.isEmpty(fileExt)?"":"."+fileExt);
        MultipartFile newMultipartFile = null;
        try {
            newMultipartFile = new MockMultipartFile(fileName, fileName, file.getContentType(), IOUtils.toByteArray(file.getInputStream()));
        }catch (Exception e) {}
        return newMultipartFile;
    }

    @Override
    public String fileUpload(String pid, String dataClass, String moduleCode, MultipartFile file) {
        SysUser currentUser = SysUserHolder.getCurrentUser();
        String dataGroupId = null;
        if (FileManegeDataClass.personal.toString().equals(dataClass)) {
            //查询个人
            dataGroupId = currentUser.getId();
        } else if (FileManegeDataClass.tenantpublic.toString().equals(dataClass)) {
            //查询企业
            dataGroupId = currentUser.getTenant_id();
        }

        SysFileManage folder = "root".equals(pid) ? null : srv.queryObjectByIdDisableTenant(SysFileManage.class, pid);
        dataClass = folder == null ? dataClass : folder.getDataClass();
        Where where = Where.create();
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getTmused, 1);
        if (FileManegeDataClass.personal.toString().equals(dataClass)) {
            //查询个人
            where.eq(SysFileManage::getDataGroupId, currentUser.getId());
        } else if (FileManegeDataClass.tenantpublic.toString().equals(dataClass)) {
            //查询企业
            where.eq(SysFileManage::getDataGroupId, currentUser.getTenant_id());
        }
        if ("root".equals(pid)) {
            pid = getTenantRoot(moduleCode, dataClass, dataGroupId).getId();
        }
        if (StringUtils.isNotEmpty(pid)) {
            //根据父id查
            where.eq(SysFileManage::getPid, pid);
        }
        //指定类型
        String fileName = file.getOriginalFilename();
        where.eq(SysFileManage::getFileType, 1);
        if (StringUtils.isNotEmpty(fileName)) {
            //按名称查询
            where.eq(SysFileManage::getName, fileName);
        }
        List<SysFileManage> files = srv.queryData(SysFileManage.class, where, null, null);
        SysFileManage fm = StringUtils.isNotEmpty(files) ? files.get(0) : null;

        String fileOriginalName = fileName;
        String fileExt = null;
        if (StringUtils.isNotEmpty(fileName) && fileName.contains(".")) {
            fileOriginalName = fileName.substring(0, fileName.lastIndexOf("."));
            fileExt = fileName.substring(fileName.lastIndexOf(".") + 1);
        }

//        file = generateNewMultipartFile(file, fm);

        if (fm != null) {
//            String oldUrl = fm.getFileUrl();
            SysFilesInfo fileInfo = new SysFilesInfo();
            fileInfo.setFileUrl(fm.getFileUrl());
            fs.uploadFile(file, fileInfo);

            fm.setFileDiskAddress(fileInfo.getFileAddress());
            fm.setFileSize(file.getSize());
            //更新
//            srv.update(fm);
            treeSrv.updateNode(SysFileManage.class, MODEL_ID, fm);
        } else {
            //新增
            String fileNewName = fileOriginalName+"_"+System.currentTimeMillis() + (StringUtils.isEmpty(fileExt)?"":"."+fileExt);
            SysFilesInfo fileInfo = new SysFilesInfo();
            String url = getFileRootUrl(dataClass);
            url = url+"/"+fileNewName;
            fileInfo.setFileUrl(url);
            fs.uploadFile(file, fileInfo);
            fm = new SysFileManage();
            fm.setModuleCode(moduleCode);
            fm.setFileSize(file.getSize());
            fm.setName(fileName);
            fm.setFileOriginalName(fileOriginalName);
            fm.setFileNewName(fileNewName);

            fm.setFileExt(fileExt);
            fm.setFileDiskAddress(fileInfo.getFileAddress());
            fm.setFileType(1);
            fm.setId(TMUID.getUID());
            fm.setTmused(1);
            fm.setPid(pid);
            fm.setDelFlag(0);
            fm.setModelId(MODEL_ID);
            fm.setDataClass(dataClass);
            fm.setDataGroupId(dataGroupId);
            fm.setFileUrl(url);
//            srv.insert(fm);
            treeSrv.append(SysFileManage.class, MODEL_ID, fm.getPid(), fm);
        }

        return null;
    }

    @Override
    public void fileDownload(HttpServletResponse response, FileManageQueryDto dto) {
        FileManegeDataClass dataClass = dto.getDataClass();
        String id = dto.getId();
        if (FileManegeDataClass.system.equals(dataClass)) {
            //系统文件
            fileInfoSrv.downloadFile(id, response);
        } else {
            //个人或者企业文件
            SysFileManage file = srv.queryObjectById(SysFileManage.class, id);
            if (file == null) {
                throw new RuntimeException("找不到文件");
            }
            SysFilesInfo fileInfo = new SysFilesInfo();
            fileInfo.setFileUrl(file.getFileUrl());
            fileInfo.setFileAddress(file.getFileDiskAddress());
            fileInfo.setOldFileName(file.getName());
            fileInfoSrv.downloadFile(fileInfo, response);
        }
    }

    @Override
    public String realDeleteFileByIdList(List<String> idList) {
        if (StringUtils.isEmpty(idList)) {
            throw new RuntimeException("传入参数有误");
        }
        srv.rawUpdate(SysFileManage.class, Update.create(SysFileManage::getTmused, -1), Where.create().in(SysFileManage::getId, idList.toArray()));

        return null;
    }

    @Override
    public String restoreFiles(List<String> idList) {
        List<SysFileManage> dataList = srv.queryData(SysFileManage.class, Where.create().in(SysFileManage::getId, idList.toArray()), null, null);
        if (StringUtils.isEmpty(dataList)) {
            throw new RuntimeException("找不到要还原的记录");
        }

        String moduleCode = dataList.get(0).getModuleCode();
        String dataClass = dataList.get(0).getDataClass();
        String dataGroupId = dataList.get(0).getDataGroupId();
        Where where = Where.create();
//        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getModuleCode, moduleCode);
        where.eq(SysFileManage::getDataClass, dataClass);
        where.eq(SysFileManage::getDataGroupId, dataGroupId);

        List<SysFileManage> allList = srv.queryData(SysFileManage.class, where, null, null);
        if (StringUtils.isEmpty(allList)) {
            throw new RuntimeException("找不到记录");
        }

        Map<String, List<SysFileManage>> childGroup = new HashMap<>();
        Map<String, SysFileManage> dataMap = new HashMap<>();
        for (SysFileManage file : allList) {
            dataMap.put(file.getId(), file);
            if (idList.contains(file.getId()) || file.getTmused() == 1) {
                childGroup.computeIfAbsent(file.getPid(), v -> new ArrayList<>()).add(file);
            }
        }


        Set<String> pset = new HashSet<>();
        Set<String> cset = new HashSet<>();
        for (SysFileManage file : dataList) {
            getRestorePidList(pset, file, dataMap);
            getRestoreChildIdList (cset, file, childGroup);
        }

        //父级两个删除标识全恢复
        srv.rawUpdate(SysFileManage.class, Update.create(SysFileManage::getDelFlag,0).update(SysFileManage::getTmused,1), Where.create().in(SysFileManage::getId, pset.toArray()));
        //子节点只恢复逻辑删除标识
        srv.rawUpdate(SysFileManage.class, Update.create(SysFileManage::getTmused,1), Where.create().in(SysFileManage::getId, cset.toArray()));


        /*
        String rootId = getRoot(moduleCode, dataClass, dataGroupId).getId();
        Where where = Where.create();
        where.eq(SysFileManage::getPid, rootId);
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getFileType, 2);
        where.eq(SysFileManage::getName, "回收站还原");
        where.eq(SysFileManage::getDataGroupId, dataList.get(0).getDataGroupId());
        where.eq(SysFileManage::getDataClass, dataList.get(0).getDataClass());
        List<SysFileManage> folders = srv.queryData(SysFileManage.class, where, null, null);
        SysFileManage folder = null;
        if (StringUtils.isEmpty(folders)) {
            //没有还原文件夹
            folder = new SysFileManage();
            folder.setId(TMUID.getUID());
            folder.setPid(rootId);
            folder.setFileType(2);
            folder.setName("回收站还原");
            folder.setTmused(1);
            folder.setDataGroupId(dataList.get(0).getDataGroupId());
            folder.setDataClass(dataList.get(0).getDataClass());
            folder.setModelId(MODEL_ID);
            folder.setDelFlag(0);
            srv.insert(folder);
        } else {
            folder = folders.get(0);
        }
        for (SysFileManage file : dataList) {
            file.setTmused(1);
            file.setPid(folder.getId());
        }
        srv.updateBatch(dataList);
        */

        return null;
    }

    @Override
    public boolean fileExists(FileManageQueryDto dto) {

        if ("root".equals(dto.getPid())) {
            //查询层级1
            FileManegeDataClass dataClassEnum = dto.getDataClass();
            String moduleCode = dto.getModuleCode();
            String dataClass = dataClassEnum.toString();
            SysUser currentUser = SysUserHolder.getCurrentUser();
            String dataGroupId = null;
            if (FileManegeDataClass.personal.equals(dto.getDataClass())) {
                //查询个人
                dataGroupId = currentUser.getId();
            } else if (FileManegeDataClass.tenantpublic.equals(dto.getDataClass())) {
                //查询企业
                dataGroupId = currentUser.getTenant_id();
            }
            dto.setPid(getTenantRoot(moduleCode, dataClass, dataGroupId).getId());
        }

        Where where = Where.create();
        where.eq(SysFileManage::getPid, dto.getPid());
        where.eq(SysFileManage::getTmused, 1);
        where.eq(SysFileManage::getDelFlag, 0);
        if (dto.getFileType() > 0) {
            //指定类型
            where.eq(SysFileManage::getFileType, dto.getFileType());
        }
        if (StringUtils.isNotEmpty(dto.getName())) {
            //按名称查询
            where.eq(SysFileManage::getName, dto.getName());
        }

        List<SysFileManage> list = srv.queryData(SysFileManage.class, where, null, null);
        return StringUtils.isNotEmpty(list);
    }

    @Override
    public String getFileBase64(SysFileManage file) {
        String dataClass = file.getDataClass();
        SysFilesInfo fileInfo = new SysFilesInfo();
        fileInfo.setFileUrl(file.getFileUrl());
        fileInfo.setFileAddress(file.getFileDiskAddress());
        InputStream fis = fs.getFileSteam(fileInfo);
        String res = null;
        if (fis == null) {
            return null;
        }
        try {
            byte[] buffer = IOUtils.toByteArray(fis);
            res = Base64Helper.encode(buffer);
        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                }
            }
        }
        return res;
    }


    private void getRestorePidList (Set<String> set, SysFileManage file, Map<String, SysFileManage> dataMap) {
        set.add(file.getId());
        String pid = file.getPid();
        SysFileManage p = dataMap.get(pid);
        if (p != null) {
            getRestorePidList(set, p, dataMap);
        }
    }

    private void getRestoreChildIdList (Set<String> set, SysFileManage file, Map<String, List<SysFileManage>> childGroup) {
        set.add(file.getId());
        List<SysFileManage> children = childGroup.get(file.getId());
        if (StringUtils.isNotEmpty(children)) {
            for (SysFileManage child : children) {
                getRestoreChildIdList (set, child, childGroup);
            }
        }
    }

}
