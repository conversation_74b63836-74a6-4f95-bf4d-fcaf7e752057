
package com.yunhesoft.system.tools.dbDictionary.service.impl;

import java.lang.reflect.Field;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Column;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.common.entity.BaseEntity;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.tools.dbDictionary.entity.po.DictionariesData;
import com.yunhesoft.system.tools.dbDictionary.entity.po.DictionariesInfo;
import com.yunhesoft.system.tools.dbDictionary.service.IDataBaseOperationService;
import com.yunhesoft.system.tools.dbDictionary.service.IDictionariesColumn;
import com.yunhesoft.system.tools.dbDictionary.service.IDictionariesDataBaseService;
import com.yunhesoft.system.tools.dbDictionary.service.IDictionariesInfoBaseService;

import io.swagger.annotations.ApiModelProperty;

/**
 * DictionariesColumn
 *
 * <AUTHOR>
 * @date 2019/12/24
 */
@Service
public class DictionariesColumnImpl implements IDictionariesColumn {
	@Autowired
	public DataSource ds;
	@Autowired
	public IDictionariesInfoBaseService dim;

	@Autowired
	public IDictionariesDataBaseService ddm;
	private Connection conn = null;
	private ResultSet rs = null;
	private PreparedStatement ps = null;
	@Autowired
	private IDataBaseOperationService iDataBaseOperationService;

	private static final List<String> defaultCol;
	private static final List<DictionariesData> defaultColumn;
	// 程序启动获取默认实体类信息
	static {
		defaultCol = new ArrayList<>();
		defaultColumn = new ArrayList<>();
		Field[] fields = BaseEntity.class.getDeclaredFields();
		for (Field field : fields) {
			DictionariesData bean = new DictionariesData();
			Column column = field.getAnnotation(Column.class);
			if (ObjUtils.notEmpty(column)) {
				// 默认列名
				if (ObjUtils.notEmpty(column)) {
					bean.setColumnName(column.name());
				}
				// 默认列备注
				ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
				if (ObjUtils.notEmpty(apiModelProperty)) {
					bean.setColumnDescribes(apiModelProperty.value());
				}
				// 列类型
				String t;
				if (field.getType().getName().contains("String")) {
					t = "varchar(" + column.length() + ")";
				} else {
					t = "datetime";
				}
				bean.setColumnType(t);
				bean.setIsUpdate(0);
				if (ObjUtils.notEmpty(column)) {
					defaultCol.add(column.name());
					defaultColumn.add(bean);
				}
			}

		}
	}

	/**
	 * 获取默认列
	 * 
	 * <AUTHOR>
	 * @param
	 * @return
	 */
	public static List<DictionariesData> getDefaultCol() {
		return defaultColumn;
	}

	/**
	 * 保存列信息
	 *
	 * @param list
	 * @return int
	 */
//	@Override
//	public boolean saveDataColumn(List<DictionariesData> list, String tableName) {
//		if (list == null || list.size() <= 0) {
//			return true;
//		}
//		// 保存列信息到数据字典中
//		boolean bool = ddm.saveBatch(list);
//		// 如果保存成功，将创建真实数据库表
//		if (bool && list != null && list.size() > 0) {
//			// 判断表是否存在
//			boolean tableBool = verdictTableName(tableName);
//			if (tableBool) {
//				// 如果表存在，就插入列
//				createColumn(list, tableName);
//			} else {
//				// 如果表不存在，就创建表
//				// 生成创建语句
//				createTable(list, tableName);
//			}
//		}
//		return bool;
//	}

	/**
	 * 更新列信息
	 *
	 * @param list
	 * @return int
	 */
	@Override
	public boolean upDataColumn(List<DictionariesData> list, String tableName) {
		if (list == null || list.size() <= 0) {
			return true;
		}
		// 一下是获取保存前得信息
		String tableTmuid = list.get(0).getParentId();
		List<DictionariesData> listdata = new ArrayList<DictionariesData>();
		DictionariesData query = new DictionariesData();
		query.setParentId(tableTmuid);
		listdata = ddm.selectList(query);

		boolean bool = ddm.updateBatchById(list);
		bool = updateColumn(list, listdata, tableName);
		return bool;
	}

	/**
	 * 判断表是否存在
	 *
	 * @return sql 创建语句
	 */
	public boolean verdictTableName(String tableName) {
		boolean bool = false;
		try {
			String sql = "SELECT count(name) as num FROM SysObjects Where XType='U' and name='" + tableName + "' ";
			ResultSet rs = selectSql(sql);
			while (rs.next()) {
				int num = rs.getInt("num");
				if (num > 0) {
					bool = true;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			closeConn();
		}
		return bool;
	}

	/**
	 * 创建表
	 *
	 * @return sql 创建语句
	 */
	public boolean createTable(List<DictionariesData> list, String tableName) {
		boolean bool = false;
		try {
			if (list == null || list.size() <= 0) {
				return bool;
			}
			String iskey = "";
			StringBuffer sql = new StringBuffer();
			StringBuffer sqlCl = new StringBuffer();
			StringBuffer sqlMs = new StringBuffer();
			sql.append(" CREATE TABLE " + tableName + "(");
			for (int i = 0; i < list.size(); i++) {
				DictionariesData e = list.get(i);
				// 列名
				String columnName = e.getColumnName();
				// 类型
				String columnType = e.getColumnType();
				// 描述
				String columnDescribes = e.getColumnDescribes();
				// 主键
				Integer keyable = e.getKeyable();
				// 是否为空
				Integer notNullable = e.getNotNullable();
				// 默认值
//					String columnDefault=e.getColumnDefault();
				// 标识
//					int columnMark =e.getColumnMark();

				// 插入列语句
				sqlCl.append(columnName + " " + columnType);
				// 为空
				String isnulls = "";
				if (notNullable == null || notNullable == 0) {
					isnulls = " null,";
				} else {
					isnulls = " not null,";
				}
				// 主键--如果前台传过来多个 主键，就只取第一个，其他的不算主键
				if (keyable != null && keyable > 0 && iskey.equals("")) {
					isnulls = " not null,";
					iskey = columnName;
				}
				sqlCl.append(isnulls);
				// 如果有描述
				if (columnDescribes != null && !"".equals(columnDescribes)) {
//					sqlMs.append(" GO ");
					sqlMs.append("  EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'"
							+ columnDescribes
							+ "' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'"
							+ tableName + "', @level2type=N'COLUMN',@level2name=N'" + columnName + "' ");

				}
			}
			if (!"".equals(iskey)) {
				sql.append(sqlCl.toString());
				sql.append(" CONSTRAINT PK_" + tableName + " PRIMARY KEY CLUSTERED");
				sql.append(" ( ");
				sql.append(iskey + " ASC ");
				sql.append(
						" )WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY] ");
				sql.append(" ) ON [PRIMARY] ");
			} else {
				sql.append(sqlCl.toString().substring(0, sqlCl.length() - 1));
				sql.append(" ) ON [PRIMARY] ");
			}

//			sql.append(" GO ");
//			sql.append(" SET ANSI_PADDING OFF ");
			sql.append(sqlMs.toString());
			System.out.println(sql.toString());
			bool = updateSql(sql.toString());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return bool;
	}

	/**
	 * 添加列
	 *
	 * @return sql 添加列
	 */
	public boolean createColumn(List<DictionariesData> list, String tableName) {
		boolean bool = false;
		try {
			List<String> listSql = new ArrayList<String>();
			String deletekeyable = "";
			String addkeyableSql = "";
			// 如果表存在，就插入列
			if (list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					DictionariesData e = list.get(i);
					// 列名
					String columnName = e.getColumnName();
					// 类型
					String columnType = e.getColumnType();
					// 描述
					String columnDescribes = e.getColumnDescribes();
					// 主键
					Integer keyable = e.getKeyable();
					// 是否为空
					Integer notNullable = e.getNotNullable();
					// 默认值
//					String columnDefault=e.getColumnDefault();
					// 标识
//					int columnMark =e.getColumnMark();

					// 插入列语句
					String sql = "alter table " + tableName + " add " + columnName + " " + columnType + "";
					// 为空
					String isnulls = "";
					if (notNullable == null || notNullable == 0) {
						isnulls = " null";
					} else {
						isnulls = " not null";
					}
					// 主键--如果前台传过来多个 主键，就只取第一个，其他的不算主键
					if (keyable != null && keyable > 0 && deletekeyable.equals("")) {
						isnulls = " not null";
						// 刪除主键
						deletekeyable = "alter table " + tableName + " drop constraint PK_" + tableName;
						// 添加主键
						addkeyableSql = "alter table " + tableName + " add constraint PK_" + tableName
								+ " primary key CLUSTERED ( " + columnName + " )";
					}
					sql = sql + isnulls;
					listSql.add(sql);
					// 如果有描述
					if (columnDescribes != null && !"".equals(columnDescribes)) {
						sql = "EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'" + columnDescribes
								+ "' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'"
								+ tableName + "', @level2type=N'COLUMN',@level2name=N'" + columnName + "'";
						listSql.add(sql);
					}
				}
				if (listSql != null && listSql.size() > 0) {
					// 先添加列
					bool = updateListSql(listSql);
					// 如果有主键，先删除旧主键，在添加新主键
					if (!"".equals(deletekeyable)) {
						try {
							// 刪除主键
							updateSql(deletekeyable);
						} catch (Exception e) {
						}
						try {
							// 添加主键
							updateSql(addkeyableSql);
							// 不用管主键是否删除成功
						} catch (Exception e) {
						}
					}
				}

			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return bool;
	}

	/**
	 * 修改列
	 *
	 * @return sql 修改列
	 */
	public boolean updateColumn(List<DictionariesData> list, List<DictionariesData> listData, String tableName) {
		boolean bool = false;
		try {
			Map<String, String> mapData = new HashMap<String, String>();
			if (listData != null && listData.size() > 0) {
				for (int i = 0; i < listData.size(); i++) {
					DictionariesData e = listData.get(i);
					String key = e.getId();
					String value = e.getColumnName();
					mapData.put(key, value);
				}
			}

			Map<String, String> map = getExplain(tableName);
			List<String> listSql = new ArrayList<String>();
			String deletekeyable = "";
			String addkeyableSql = "";
			if (list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					DictionariesData e = list.get(i);
					// tmuid
					String tmuid = e.getId();
					// 列名
					String columnName = e.getColumnName();
					String oldColumnName = "";
					if (mapData != null && mapData.containsKey(tmuid)) {
						oldColumnName = mapData.get(tmuid);
					}
					// 类型
					String columnType = e.getColumnType();
					// 描述
					String columnDescribes = e.getColumnDescribes();
					// 主键
					Integer keyable = e.getKeyable();
					// 是否为空
					Integer notNullable = e.getNotNullable();
					// 默认值
//					String columnDefault=e.getColumnDefault();
					// 标识
//					int columnMark =e.getColumnMark();
//					ALTER TABLE "+tableName+" alter column "+columnName+" "+columnType+" ;
					// 插入列语句

					// 修改了类名
					String sql = "";
					if (!oldColumnName.equals(columnName)) {
						sql = " exec sp_rename '" + tableName + "." + oldColumnName + "','" + columnName + "' ";
						listSql.add(sql);
					}

					sql = sql + " ALTER TABLE " + tableName + " alter column " + columnName + " " + columnType;
					// 为空
					String isnulls = "";
					if (notNullable == null || notNullable == 0) {
						isnulls = " null";
					} else {
						isnulls = " not null";
					}
					// 主键--如果前台传过来多个 主键，就只取第一个，其他的不算主键
					if (keyable != null && keyable > 0 && deletekeyable.equals("")) {
						isnulls = " not null";
						// 刪除主键
						deletekeyable = "alter table " + tableName + " drop constraint PK_" + tableName;
						// 添加主键
						addkeyableSql = "alter table " + tableName + " add constraint PK_" + tableName
								+ " primary key CLUSTERED ( " + columnName + " )";
					}

					sql = sql + isnulls;
					listSql.add(sql);
					// 如果有描述
					if (columnDescribes != null && !"".equals(columnDescribes)) {
						// 判断创建表的时候是否有说明
						String colDs = "";
						if (map != null && map.containsKey(columnName)) {
							colDs = map.get(columnName);
						}
						if (!colDs.equals("")) {
							// 如果有说明，就更新
							sql = "EXECUTE sys.sp_updateextendedproperty N'MS_Description', N'" + columnDescribes
									+ "', N'USER', N'dbo',N'TABLE', N'" + tableName + "', N'COLUMN', N'" + columnName
									+ "'";
						} else {
							// 如果没有说明，就添加
							sql = "EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'" + columnDescribes
									+ "' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'"
									+ tableName + "', @level2type=N'COLUMN',@level2name=N'" + columnName + "'";

						}
						listSql.add(sql);
					}
				}
				if (listSql != null && listSql.size() > 0) {
					// 先添加列
					bool = updateListSql(listSql);
					// 如果有主键，先删除旧主键，在添加新主键
					if (!"".equals(deletekeyable)) {
						// 刪除主键
						try {
							updateSql(deletekeyable);
						} catch (Exception e) {
						}
						// 添加主键 --不用管主键是否删除成功
						try {
							updateSql(addkeyableSql);
						} catch (Exception e) {
						}

					}
				}

			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return bool;
	}

	/**
	 * 获取表的列，对应的描述
	 *
	 * @param tableName
	 * @return
	 */
	public Map<String, String> getExplain(String tableName) {
		Map<String, String> map = new HashMap<String, String>();
		try {
			StringBuffer sql = new StringBuffer();
			sql.append(" SELECT ");
			sql.append(" A.name, ");
			sql.append(" isnull(G.[value],'') as value ");
			sql.append(" FROM ");
			sql.append(" syscolumns A ");
			sql.append(" Inner Join ");
			sql.append(" sysobjects D ");
			sql.append(" On ");
			sql.append(" A.id=D.id and D.xtype='U' and D.name<>'dtproperties' ");
			sql.append(" Left Join ");
			sql.append(" sys.extended_properties G ");
			sql.append(" on ");
			sql.append(" A.id=G.major_id and A.colid=G.minor_id ");
			sql.append(" where d.name='" + tableName + "'  ");
			sql.append(" Order By ");
			sql.append(" A.id,A.colorder ");
			System.out.println(sql);
			ResultSet rs = selectSql(sql.toString());
			while (rs.next()) {
				// 表名
				String key = rs.getString("name");
				// 表描述
				String value = rs.getString("value");
				map.put(key, value);
			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			closeConn();
		}
		return map;
	}

	/**
	 * 反转数据库表到数据库字典
	 */
	@Override
	public Boolean getFzSql() {
		boolean bln = false;
		try {
			List<DictionariesData> listdata = new ArrayList<DictionariesData>();
			int sort = 1;
			// 判断是否同步数据库分类
			DictionariesInfo query = new DictionariesInfo();
			List<DictionariesInfo> list = new ArrayList<DictionariesInfo>();
			// 根据分类信息
			query.setChineseName("同步数据库分类");
			list = dim.selectList(query);
			if (list != null && list.size() > 0) {
				// 如果存在不添加这个分类
				// 查询分类下的数据有几个，用于新添加表时sort排序使用
				String rootId = list.get(0).getId();
				query.setParentId(rootId);
				list = dim.selectList(query);
				if (list != null && list.size() > 0) {
					sort = list.size() + 1;
				}
			} else {
				// 生成这个分类
				query.setChineseName("同步数据库分类");
				list = dim.selectList(query);
				if (!StringUtils.isNotEmpty(list)) {
					DictionariesInfo e = new DictionariesInfo();
					String rootId = TMUID.getUID();
					e.setId(rootId);
					e.setParentId("");
					e.setName("同步数据库分类");
					e.setChineseName("同步数据库分类");
					e.setMemo("");
					e.setIsLeaf(0);
					e.setPathAll("/" + rootId);
					e.setUsed(1);
					e.setSort(9999);
					dim.insert(e);
				}
			}

			// 生成查询数据库中要反正表的sql
			StringBuffer sql1 = new StringBuffer();
			ResultSet rs;
			if (iDataBaseOperationService.getType().contains("sqlserver")) {
				sql1.append("SELECT \n"
						+ "tableName       = CASE WHEN A.COLORDER=1 THEN CONVERT(varchar(200),D.NAME) ELSE '' END,\n"
						+ "tableDescribes     = CASE WHEN A.COLORDER=1 THEN CONVERT(varchar(200),ISNULL(F.VALUE,'')) ELSE '' END,\n"
						+ "px   = A.COLORDER,\n" + "columnName     = CONVERT(varchar(200),A.NAME),\n" +
						// "自增标识 = CASE WHEN COLUMNPROPERTY( A.ID,A.NAME,'ISIDENTITY')=1 THEN '1'ELSE
						// '0' END,\n" +
						"keyable       = CASE WHEN EXISTS(SELECT 1 FROM SYSOBJECTS WHERE XTYPE='PK' AND PARENT_OBJ=A.ID AND NAME IN (\n"
						+ " SELECT NAME FROM SYSINDEXES WHERE INDID IN(\n"
						+ " SELECT INDID FROM SYSINDEXKEYS WHERE ID = A.ID AND COLID=A.COLID))) THEN 1 ELSE 0 END,\n"
						+ "columnType       = CONVERT(varchar(200),B.NAME),\n" +
						// "占用字节数 = A.LENGTH,\n" +
						"maxValue       = COLUMNPROPERTY(A.ID,A.NAME,'PRECISION'),\n"
						+ "decimals   = CONVERT(int,ISNULL(COLUMNPROPERTY(A.ID,A.NAME,'SCALE'),0)),\n"
						+ "notNullable     = CASE WHEN A.ISNULLABLE=1 THEN 1 ELSE 0 END,\n"
						+ "columnDefault     = CONVERT(varchar(200),ISNULL(E.TEXT,'')),\n"
						+ "columnDescribes   = CONVERT(varchar(200),ISNULL(G.[VALUE],''))\n" + " FROM \n"
						+ " SYSCOLUMNS A\n" + " LEFT JOIN SYSTYPES B  ON  A.XUSERTYPE=B.XUSERTYPE\n"
						+ " INNER JOIN  SYSOBJECTS D  ON  A.ID=D.ID  AND D.XTYPE='U' AND  D.NAME<>'DTPROPERTIES'\n"
						+ " LEFT JOIN  SYSCOMMENTS E  ON  A.CDEFAULT=E.ID\n"
						+ " LEFT JOIN  sys.extended_properties G  ON  A.ID=G.major_id AND A.COLID=G.minor_id  \n"
						+ " LEFT JOIN  sys.extended_properties F  ON  D.ID=F.major_id AND F.minor_id=0\n"
						+ " ORDER BY D.NAME,A.ID,A.COLORDER");
				rs = selectSql(sql1.toString());

				String parentId = "";
				int i = 1;
				while (rs.next()) {
					// 表名
					String tableName = rs.getString("tableName");
					// 表描述
					String tableDescribes = rs.getString("tableDescribes");
					// 排序
					Integer px = rs.getInt("px");
					// 列名
					String columnName = rs.getString("columnName");
					// 列描述
					String columnDescribes = rs.getString("columnDescribes");
					// 是否为标示1是0否
					// Integer columnMark = rs.getInt("columnMark");
					// 是否为主键1是0否
					Integer keyable = rs.getInt("keyable");
					// 列类型
					String columnType = rs.getString("columnType");
					// 最大值
					Integer maxValue = rs.getInt("maxValue");
					// 小数
					Integer decimals = rs.getInt("decimals");
					// 不为空
					Integer notNullable = rs.getInt("notNullable");
					// 默认值
					String columnDefault = rs.getString("columnDefault");
					// 表名不为空
					if (tableName != null && !tableName.equals("")) {
						// 添加表信息
						// Date datatime = new Date();
						String tmuid = TMUID.getUID();
						DictionariesInfo disinfo = new DictionariesInfo();
						parentId = tmuid;
						disinfo.setId(tmuid);
						query.setChineseName("同步数据库分类");
						query.setParentId(null);
						list = dim.selectList(query);
						disinfo.setParentId(list.get(0).getId());
						disinfo.setName(tableName);
						disinfo.setChineseName("新表");
						disinfo.setMemo(tableDescribes);
						disinfo.setIsLeaf(1);
						disinfo.setPathAll(list.get(0).getId() + tmuid);
						disinfo.setUsed(1);
						disinfo.setSort(sort + i);
						i = i + 1;
						dim.insert(disinfo);
					}
					// 添加字段
					// Date datatime = new Date();
					String tmuid = TMUID.getUID();
					DictionariesData bean = new DictionariesData();
					bean.setId(tmuid);
					bean.setParentId(parentId);
					bean.setSort(px);
					bean.setColumnName(columnName);
					bean.setChineseName(columnDescribes);
					bean.setColumnDefault(columnDefault);
					bean.setColumnDescribes(columnDescribes);
					// bean.setColumnMark(columnMark);
					columnType = getColumnTypeZh(columnType, maxValue, decimals);
					bean.setColumnType(columnType);
					bean.setKeyable(keyable);
					bean.setNotNullable(notNullable);
					bean.setUsed(1);
					// 如果是默认列
					if (defaultCol.contains(columnName)) {
						bean.setIsUpdate(0);
					} else {
						bean.setIsUpdate(1);
					}
					listdata.add(bean);
				}

			} else {
				// mysql 操作 查所有表
				sql1.append("select table_name tableName, \n" + "   table_comment tableDescribes, \n"
						+ "   create_time createTime \n" + "from information_schema.tables\n"
						+ "where table_schema = (select database())\n" + "order by create_time desc");
				rs = selectSql(sql1.toString());
				int i = 1;
				while (rs.next()) {
					String parentId;
					// 表名
					String tableName = rs.getString("tableName");
					// 表描述
					String tableDescribes = rs.getString("tableDescribes");
					// 添加表信息
					String tmuid = TMUID.getUID();
					DictionariesInfo disinfo = new DictionariesInfo();
					parentId = tmuid;
					disinfo.setId(tmuid);
					query.setChineseName("同步数据库分类");
					query.setParentId(null);
					list = dim.selectList(query);
					disinfo.setParentId(list.get(0).getId());
					disinfo.setName(tableName);
					disinfo.setChineseName("新表");
					disinfo.setMemo(tableDescribes);
					disinfo.setIsLeaf(1);
					disinfo.setPathAll(list.get(0).getPathAll() + "/" + tmuid);
					disinfo.setUsed(1);
					disinfo.setSort(sort + i);
					i = i + 1;
					dim.insert(disinfo);
					// 查询列
					StringBuffer sql2 = new StringBuffer();
					sql2.append("select column_name columnName, \n" + "   column_comment columnDescribes, \n"
							+ "   column_key columnKey, \n" + "   is_nullable as notNullable,\n"
							+ "   column_type as columnType \n" + "from information_schema.columns\n"
							+ "where table_name = '");
					sql2.append(tableName);
					sql2.append("'\n" + "   and table_schema = (select database()) \n" + "order by ordinal_position;");
					ResultSet rs1 = selectSql(sql2.toString());
					while (rs1.next()) {
						// 列名
						String columnName = rs1.getString("columnName");
						// 列描述
						String columnDescribes = rs1.getString("columnDescribes");
						// 是否为主键1是0否
						Integer keyable = rs1.getString("columnKey").equals("PRI") ? 1 : 0;
						// 列类型
						String columnType = rs1.getString("columnType");
						// 不为空
						Integer notNullable = rs1.getString("notNullable").equals("YES") ? 1 : 0;

						// 添加字段
						String id = TMUID.getUID();
						DictionariesData bean = new DictionariesData();
						bean.setId(id);
						bean.setParentId(parentId);
						bean.setColumnName(columnName);
						bean.setChineseName(columnDescribes);
						bean.setColumnDescribes(columnDescribes);
						bean.setColumnType(columnType);
						bean.setKeyable(keyable);
						bean.setNotNullable(notNullable);
						bean.setUsed(1);
						if (defaultCol.contains(columnName)) {
							bean.setIsUpdate(0);
						} else {
							bean.setIsUpdate(1);
						}
						listdata.add(bean);
					}
				}
			}
			if (StringUtils.isNotEmpty(listdata)) {
				bln = ddm.insertBatch(listdata);// saveBatch(listdata);
			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			closeConn();
		}
		return bln;
	}

	/**
	 * 返回列类型
	 *
	 * @param columnType
	 * @param maxValue
	 * @param decimals
	 * @return
	 */
	public String getColumnTypeZh(String columnType, Integer maxValue, Integer decimals) {
		String colType = "";
		try {
			if (columnType == null || columnType.equals("")) {
				return columnType;
			}
			if (columnType.equals("varchar") && !maxValue.equals(-1)) {
				colType = columnType + "(" + maxValue + ")";
			} else if (columnType.equals("datetime")) {
				colType = columnType;
			} else if (columnType.equals("datetime2")) {
				colType = columnType + "(" + maxValue + ")";
			} else if (columnType.equals("datetimeoffset")) {
				colType = columnType + "(" + maxValue + ")";
			} else if (columnType.equals("decimal")) {
				colType = columnType + "(" + maxValue + "," + decimals + ")";
			} else if (columnType.equals("float")) {
				colType = columnType;
			} else if (columnType.equals("geography")) {
				colType = columnType;
			} else if (columnType.equals("geometry")) {
				colType = columnType;
			} else if (columnType.equals("hierarchyid")) {
				colType = columnType;
			} else if (columnType.equals("image")) {
				colType = columnType;
			} else if (columnType.equals("money")) {
				colType = columnType;
			} else if (columnType.equals("int")) {
				colType = columnType;
			} else if (columnType.equals("nchar") && !maxValue.equals(-1)) {
				colType = columnType + "(" + maxValue + ")";
			} else if (columnType.equals("ntext")) {
				colType = columnType;
			} else if (columnType.equals("nvarchar") && !maxValue.equals(-1)) {
				colType = columnType + "(" + maxValue + ")";
			} else if (columnType.equals("nvarchar") && maxValue.equals(-1)) {
				colType = columnType + "(MAX)";
			} else if (columnType.equals("real")) {
				colType = columnType;
			} else if (columnType.equals("smalldatetime")) {
				colType = columnType;
			} else if (columnType.equals("smallint")) {
				colType = columnType;
			} else if (columnType.equals("smallmoney")) {
				colType = columnType;
			} else if (columnType.equals("sql_variant")) {
				colType = columnType;
			} else if (columnType.equals("text")) {
				colType = columnType;
			} else if (columnType.equals("time")) {
				colType = columnType + "(" + maxValue + ")";
			} else if (columnType.equals("timestamp")) {
				colType = columnType;
			} else if (columnType.equals("tinyint")) {
				colType = columnType;
			} else if (columnType.equals("uniqueidentifier")) {
				colType = columnType;
			} else if (columnType.equals("varbinary") && !maxValue.equals(-1)) {
				colType = columnType + "(" + maxValue + ")";
			} else if (columnType.equals("varbinary") && maxValue.equals(-1)) {
				colType = columnType + "(MAX)";
			} else if (columnType.equals("varchar") && maxValue.equals(-1)) {
				colType = columnType + "(MAX)";
			} else if (columnType.equals("xml")) {
				colType = columnType;
			} else if (columnType.equals("numeric")) {
				colType = columnType + "(" + maxValue + "," + decimals + ")";
			} else if (columnType.equals("bigint")) {
				colType = columnType;
			} else if (columnType.equals("binary")) {
				colType = columnType + "(" + maxValue + ")";
			} else if (columnType.equals("bit")) {
				colType = columnType;
			} else if (columnType.equals("char") && !maxValue.equals(-1)) {
				colType = columnType + "(" + maxValue + ")";
			} else if (columnType.equals("date")) {
				colType = columnType;
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
		return colType;
	}

	/**
	 * 通过sql获取数据
	 *
	 * @param sql
	 * @return ResultSet
	 */
	public ResultSet selectSql(String sql) {
		try {
			conn = ds.getConnection();
			System.out.println(sql);
			ps = conn.prepareStatement(sql);
			rs = ps.executeQuery();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return rs;
	}

	public void closeConn() {
		try {
			if (rs != null) {
				rs.close();
			}
			if (ps != null) {
				ps.close();
			}
			if (conn != null) {
				conn.close();
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	/**
	 * 批量执行sql
	 *
	 * @param sql
	 * @return ResultSet
	 */
	public boolean updateListSql(List<String> list) {
		boolean bool = false;
		Connection conn = null;
		Statement ps = null;
		try {
			conn = ds.getConnection();
			ps = conn.createStatement();
			if (list != null && list.size() > 0) {
				for (int i = 0; i < list.size(); i++) {
					String sql = list.get(i);
					//System.out.println(sql);
					ps.addBatch(sql);
				}
			}
			ps.executeBatch();
			bool = true;
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			try {
				if (ps != null) {
					ps.close();
				}
				if (conn != null) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}

		}
		return bool;
	}

	/**
	 * 执行sql语句
	 *
	 * @param sql
	 * @return ResultSet
	 */
	public boolean updateSql(String sql) {
		boolean bool = false;
		Connection conn = null;
		PreparedStatement ps = null;
		try {
			conn = ds.getConnection();
			System.out.println(sql);
			ps = conn.prepareStatement(sql);
			bool = ps.execute();
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			try {
				if (ps != null) {
					ps.close();
				}
				if (conn != null) {
					conn.close();
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}

		}
		return bool;
	}
}
