package com.yunhesoft.system.tools.formulaParam.service.impl;


import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.menu.entity.po.SysModule;
import com.yunhesoft.system.menu.service.ISysMenuLibService;
import com.yunhesoft.system.tds.entity.dto.TdsDataQueryDto;
import com.yunhesoft.system.tds.entity.po.TdataSource;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.entity.po.TdsoutPara;
import com.yunhesoft.system.tds.entity.vo.TdataSourceVo;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tools.formulaParam.entity.dto.TdsFormulaQueryDto;
import com.yunhesoft.system.tools.formulaParam.entity.vo.TdsFormulaTreeVo;
import com.yunhesoft.system.tools.formulaParam.service.ITdsFormulaService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 *	要素库设置相关服务接口
 * <AUTHOR>
 * @date 2025-03-07
 */
@Service
public class TdsFormulaServiceImpl implements ITdsFormulaService {
	
	@Autowired
	private IDataSourceService tdsServ; // 数据源相关服务
	
	@Autowired
	private ISysMenuLibService menuLibServ; // 模块相关服务
	
	/**
	 *	获取数据源公式树形数据
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<TdsFormulaTreeVo> getTdsFormulaTreeList(TdsFormulaQueryDto queryDto) {
		List<TdsFormulaTreeVo> result = new ArrayList<TdsFormulaTreeVo>();
		List<TdsFormulaTreeVo> tdsList = this.getTdsList(queryDto);
		if(StringUtils.isNotEmpty(tdsList)) {
			result.addAll(tdsList);
		}
		return result;
	}
	
	/**
	 *	获取数据源列表
	 * @param queryDto
	 * @return
	 */
	private List<TdsFormulaTreeVo> getTdsList(TdsFormulaQueryDto queryDto) {
		List<TdsFormulaTreeVo> result = new ArrayList<TdsFormulaTreeVo>();
		String nodeId = "tds";
		String nodeName = "数据源";
		String nodeType = "tds";
		TdsFormulaTreeVo tdsVo = new TdsFormulaTreeVo();
		tdsVo.setNodeId(nodeId);
		tdsVo.setNodeName(nodeName);
		tdsVo.setNodeType(nodeType);
		tdsVo.setId(nodeId);
		tdsVo.setPid("root");
		tdsVo.setIsleaf(0);
		tdsVo.setParamCode(nodeId);
		tdsVo.setParamName(nodeName);
		result.add(tdsVo);
		//判断是否显示所有数据源
		String showTdsType = "";
		if(queryDto!=null) {
			showTdsType = queryDto.getShowTdsType();
		}
		//查询模块
		List<SysModule> moduleList = menuLibServ.getModuleList();
		if(StringUtils.isNotEmpty(moduleList)) {
			//查询数据源
			TdsDataQueryDto dto = new TdsDataQueryDto();
			TdataSourceVo tdsDataVo = tdsServ.getListTDataSource(dto);
			if(tdsDataVo!=null) {
				List<TdataSource> tdsDataList = tdsDataVo.getList();
				if(StringUtils.isNotEmpty(tdsDataList)) {
					HashMap<String, List<TdataSource>> tdsDataMap = new HashMap<String, List<TdataSource>>();
					List<String> hasTdsAliasList = new ArrayList<String>();
					for (int i = 0; i < tdsDataList.size(); i++) {
						TdataSource tdsObj = tdsDataList.get(i);
						int used = tdsObj.getUsed()==null?0:tdsObj.getUsed();
						String tdsclassname = tdsObj.getTdsclassName();
						String modulecode = tdsObj.getModuleCode();
						String tdsAlias = tdsObj.getTdsalias();
						if(used==1&&StringUtils.isNotEmpty(tdsclassname)&&(StringUtils.isEmpty(showTdsType)||(","+showTdsType+",").indexOf(tdsclassname)!=-1)
							&&StringUtils.isNotEmpty(modulecode)&&StringUtils.isNotEmpty(tdsAlias)) {
							if(!hasTdsAliasList.contains(tdsAlias)) {
								if(tdsDataMap.containsKey(modulecode)) {
									List<TdataSource> list = tdsDataMap.get(modulecode);
									list.add(tdsObj);
								}else {
									List<TdataSource> list = new ArrayList<TdataSource>();
									list.add(tdsObj);
									tdsDataMap.put(modulecode, list);
								}
								hasTdsAliasList.add(tdsAlias);
							}
						}
					}
					//组对树形
					if(StringUtils.isNotEmpty(tdsDataMap)) {
						//模块节点
						List<TdsFormulaTreeVo> moduleNodeList = new ArrayList<TdsFormulaTreeVo>();
						//遍历模块数据
						List<String> hasModuleCodeList = new ArrayList<String>();
						for (int i = 0; i < moduleList.size(); i++) {
							SysModule moduleObj = moduleList.get(i);
							String moduleCode = moduleObj.getModuleCode();
							String moduleName = moduleObj.getModuleName();
							if(StringUtils.isNotEmpty(moduleCode)&&StringUtils.isNotEmpty(moduleName)&&!hasModuleCodeList.contains(moduleCode)) {
								if(tdsDataMap.containsKey(moduleCode)) {
									//生成模块节点
									TdsFormulaTreeVo moduleNode = new TdsFormulaTreeVo();
									moduleNode.setNodeId(moduleCode);
									moduleNode.setNodeName(moduleName);
									moduleNode.setNodeType(nodeType);
									moduleNode.setId(moduleCode);
									moduleNode.setPid(nodeId);
									moduleNode.setIsleaf(0);
									moduleNode.setParamCode(moduleCode);
									moduleNode.setParamName(moduleName);
									moduleNodeList.add(moduleNode);
									//生成数据源节点
									List<TdataSource> tdsList = tdsDataMap.get(moduleCode);
									if(StringUtils.isNotEmpty(tdsList)) {
										//数据源节点
										List<TdsFormulaTreeVo> tdsNodeList = new ArrayList<TdsFormulaTreeVo>();
										//遍历数据源数据
										for (int j = 0; j < tdsList.size(); j++) {
											TdataSource tdsObj = tdsList.get(j);
											String tdsalias = tdsObj.getTdsalias();
											String tdsName = tdsObj.getTdsname();
											//生成数据源节点
											TdsFormulaTreeVo tdsNode = new TdsFormulaTreeVo();
											tdsNode.setNodeId(tdsalias);
											tdsNode.setNodeName(tdsName);
											tdsNode.setNodeType(nodeType);
											tdsNode.setId(tdsalias);
											tdsNode.setPid(moduleCode);
											tdsNode.setIsleaf(1);
											tdsNode.setParamCode(tdsalias);
											tdsNode.setParamName(tdsName);
											tdsNodeList.add(tdsNode);
										}
										moduleNode.setChildren(tdsNodeList);
									}
								}
								hasModuleCodeList.add(moduleCode);
							}
						}
						tdsVo.setChildren(moduleNodeList);
					}
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取数据源参数树形数据
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<TdsFormulaTreeVo> getTdsParamTreeList(TdsFormulaQueryDto queryDto) {
		List<TdsFormulaTreeVo> result = new ArrayList<TdsFormulaTreeVo>();
		if(queryDto!=null) {
			String nodeId = queryDto.getNodeId(); //节点id
			String nodeName = queryDto.getNodeName(); //节点名称
			String tdsAlias = nodeId;
			//生成数据源节点
			TdsFormulaTreeVo tdsVo = new TdsFormulaTreeVo();
			tdsVo.setNodeId(nodeId);
			tdsVo.setNodeName(nodeName);
			tdsVo.setNodeType("tds");
			tdsVo.setId(nodeId);
			tdsVo.setPid("root");
			tdsVo.setIsleaf(0);
			tdsVo.setIconStr("el-icon-coin");
			tdsVo.setParamCode(tdsAlias);
			tdsVo.setParamName(nodeName);
			result.add(tdsVo);
			//子节点
			List<TdsFormulaTreeVo> childList = new ArrayList<TdsFormulaTreeVo>();
			//输入参数节点
			String inNodeId = "tds_inParam";
			String inNodeType = "inParam";
			List<TdsFormulaTreeVo> inParamList = this.getTdsInParamList(inNodeId, tdsAlias, inNodeType);
			if(StringUtils.isNotEmpty(inParamList)) {
				TdsFormulaTreeVo inNode = new TdsFormulaTreeVo();
				inNode.setNodeId(inNodeId);
				inNode.setNodeName("输入参数");
				inNode.setNodeType(inNodeType);
				inNode.setId(inNodeId);
				inNode.setPid(nodeId);
				inNode.setIsleaf(0);
				inNode.setIconStr("el-icon-folder-opened");
				inNode.setParamCode(inNodeId);
				inNode.setParamName("输入参数");
				inNode.setChildren(inParamList);
				childList.add(inNode);
			}
			//输入参数节点
			String outNodeId = "tds_outParam";
			String outNodeType = "outParam";
			List<TdsFormulaTreeVo> outParamList = this.getTdsOutParamList(outNodeId, tdsAlias, outNodeType);
			if(StringUtils.isNotEmpty(outParamList)) {
				TdsFormulaTreeVo childNode = new TdsFormulaTreeVo();
				childNode.setNodeId(outNodeId);
				childNode.setNodeName("输出参数");
				childNode.setNodeType(outNodeType);
				childNode.setId(outNodeId);
				childNode.setPid(nodeId);
				childNode.setIsleaf(0);
				childNode.setIconStr("el-icon-folder-opened");
				childNode.setParamCode(outNodeId);
				childNode.setParamName("输出参数");
				childNode.setChildren(outParamList);
				childList.add(childNode);
			}
			//扩展函数
			String extendNodeId = "tds_extendFun";
			String extendNodeType = "extendFun";
			List<TdsFormulaTreeVo> extendFunList = this.getExtendFunList(extendNodeId, extendNodeType, outParamList);
			if(StringUtils.isNotEmpty(extendFunList)) {
				TdsFormulaTreeVo childNode = new TdsFormulaTreeVo();
				childNode.setNodeId(extendNodeId);
				childNode.setNodeName("扩展函数");
				childNode.setNodeType(extendNodeType);
				childNode.setId(extendNodeId);
				childNode.setPid(nodeId);
				childNode.setIsleaf(0);
				childNode.setIconStr("el-icon-folder-opened");
				childNode.setParamCode(extendNodeId);
				childNode.setParamName("扩展函数");
				childNode.setChildren(extendFunList);
				childList.add(childNode);
			}
			tdsVo.setChildren(childList);
		}
		return result;
	}
	
	/**
	 *	数据源输入参数列表
	 * @param pNodeId
	 * @param tdsAlias
	 * @param nodeType
	 * @return
	 */
	private List<TdsFormulaTreeVo> getTdsInParamList(String pNodeId, String tdsAlias, String nodeType) {
		List<TdsFormulaTreeVo> result = new ArrayList<TdsFormulaTreeVo>();
		if(StringUtils.isNotEmpty(pNodeId)&&StringUtils.isNotEmpty(tdsAlias)&&StringUtils.isNotEmpty(nodeType)) {
			List<TdsinPara> tdsParamList = tdsServ.getTDSInPara(tdsAlias);
			if(StringUtils.isNotEmpty(tdsParamList)) {
				for (int i = 0; i < tdsParamList.size(); i++) {
					TdsinPara tdsParamObj = tdsParamList.get(i);
					String paraAlias = tdsParamObj.getParaAlias();
					String paraName = tdsParamObj.getParaName();
					if(StringUtils.isNotEmpty(paraAlias)&&StringUtils.isNotEmpty(paraName)) {
						TdsFormulaTreeVo paramVo = new TdsFormulaTreeVo();
						paramVo.setNodeId(pNodeId+"___"+paraAlias);
						paramVo.setNodeName(paraName+"（"+paraAlias+"）");
						paramVo.setNodeType(nodeType);
						paramVo.setId(pNodeId+"___"+paraAlias);
						paramVo.setPid(pNodeId);
						paramVo.setIconStr("el-icon-edit");
						paramVo.setParamCode(paraAlias);
						paramVo.setParamName(paraName);
						paramVo.setIsleaf(1);
						result.add(paramVo);
					}
				}
			}
		}
		return result;
	}
	
	/**
	 *	数据源输出参数列表
	 * @param pNodeId
	 * @param tdsAlias
	 * @param nodeType
	 * @return
	 */
	private List<TdsFormulaTreeVo> getTdsOutParamList(String pNodeId, String tdsAlias, String nodeType) {
		List<TdsFormulaTreeVo> result = new ArrayList<TdsFormulaTreeVo>();
		if(StringUtils.isNotEmpty(pNodeId)&&StringUtils.isNotEmpty(tdsAlias)&&StringUtils.isNotEmpty(nodeType)) {
			List<TdsoutPara> tdsParamList = tdsServ.getTDSOutPara(tdsAlias);
			if(StringUtils.isNotEmpty(tdsParamList)) {
				for (int i = 0; i < tdsParamList.size(); i++) {
					TdsoutPara tdsParamObj = tdsParamList.get(i);
					String paraAlias = tdsParamObj.getParaAlias();
					String paraName = tdsParamObj.getParaName();
					if(StringUtils.isNotEmpty(paraAlias)&&StringUtils.isNotEmpty(paraName)) {
						TdsFormulaTreeVo paramVo = new TdsFormulaTreeVo();
						paramVo.setNodeId(pNodeId+"___"+paraAlias);
						paramVo.setNodeName(paraName+"（"+paraAlias+"）");
						paramVo.setNodeType(nodeType);
						paramVo.setId(pNodeId+"___"+paraAlias);
						paramVo.setPid(pNodeId);
						paramVo.setIconStr("el-icon-edit");
						paramVo.setParamCode(paraAlias);
						paramVo.setParamName(paraName);
						paramVo.setIsleaf(1);
						result.add(paramVo);
					}
				}
			}
		}
		return result;
	}
	
	/**
	 *	数据源扩展函数列表
	 * @param pNodeId
	 * @param nodeType
	 * @param outParamList
	 * @return
	 */
	private List<TdsFormulaTreeVo> getExtendFunList(String pNodeId, String nodeType, List<TdsFormulaTreeVo> outParamList) {
		List<TdsFormulaTreeVo> result = new ArrayList<TdsFormulaTreeVo>();
		if(StringUtils.isNotEmpty(pNodeId)&&StringUtils.isNotEmpty(nodeType)) {
			//获取数值节点
			TdsFormulaTreeVo getNode = new TdsFormulaTreeVo();
			getNode.setNodeId("getValue");
			getNode.setNodeName("获取数值");
			getNode.setNodeType(nodeType);
			getNode.setId("getValue");
			getNode.setPid(pNodeId);
			getNode.setIconStr("el-icon-edit");
			getNode.setIsleaf(1);
			getNode.setParamCode("getValue");
			getNode.setParamName("获取数值");
			result.add(getNode);
			//查找数值节点
			if(StringUtils.isNotEmpty(outParamList)) {
				//查找数值
				String queryValue_nodeType = "queryValue";
				TdsFormulaTreeVo queryNode = new TdsFormulaTreeVo();
				queryNode.setNodeId("queryValue");
				queryNode.setNodeName("查找数值");
				queryNode.setNodeType(queryValue_nodeType);
				queryNode.setId("queryValue");
				queryNode.setPid(pNodeId);
				queryNode.setIconStr("el-icon-edit");
				queryNode.setIsleaf(0);
				queryNode.setParamCode("queryValue");
				queryNode.setParamName("查找数值");
				result.add(queryNode);
				//关键列
				List<TdsFormulaTreeVo> colmKeyQueryList = new ArrayList<TdsFormulaTreeVo>();
				String queryColmKeyNodeId = "queryColmKey";
				String queryColmKeyNodeName = "查找条件（关键列）";
				TdsFormulaTreeVo queryColmKeyNode = new TdsFormulaTreeVo();
				queryColmKeyNode.setNodeId(queryColmKeyNodeId);
				queryColmKeyNode.setNodeName(queryColmKeyNodeName);
				queryColmKeyNode.setNodeType(queryValue_nodeType);
				queryColmKeyNode.setId(queryColmKeyNodeId);
				queryColmKeyNode.setPid(queryNode.getNodeId());
				queryColmKeyNode.setIconStr("el-icon-search");
				queryColmKeyNode.setIsleaf(0);
				queryColmKeyNode.setParamCode(queryColmKeyNodeId);
				queryColmKeyNode.setParamName(queryColmKeyNodeName);
				colmKeyQueryList.add(queryColmKeyNode);
				queryNode.setChildren(colmKeyQueryList);
				
				//统计数值
				String totalValue_nodeType = "totalValue";
				TdsFormulaTreeVo totalNode = new TdsFormulaTreeVo();
				totalNode.setNodeId("totalValue");
				totalNode.setNodeName("统计数值");
				totalNode.setNodeType(totalValue_nodeType);
				totalNode.setId("totalValue");
				totalNode.setPid(pNodeId);
				totalNode.setIconStr("el-icon-edit");
				totalNode.setIsleaf(0);
				totalNode.setParamCode("totalValue");
				totalNode.setParamName("统计数值");
				result.add(totalNode);
				//关键列
				List<TdsFormulaTreeVo> colmKeyTotalList = new ArrayList<TdsFormulaTreeVo>();
				String totalColmKeyNodeId = "totalColmKey";
				String totalColmKeyNodeName = "统计条件（关键列）";
				TdsFormulaTreeVo totalColmKeyNode = new TdsFormulaTreeVo();
				totalColmKeyNode.setNodeId(totalColmKeyNodeId);
				totalColmKeyNode.setNodeName(totalColmKeyNodeName);
				totalColmKeyNode.setNodeType(totalValue_nodeType);
				totalColmKeyNode.setId(totalColmKeyNodeId);
				totalColmKeyNode.setPid(queryNode.getNodeId());
				totalColmKeyNode.setIconStr("el-icon-tickets");
				totalColmKeyNode.setIsleaf(0);
				totalColmKeyNode.setParamCode(totalColmKeyNodeId);
				totalColmKeyNode.setParamName(totalColmKeyNodeName);
				colmKeyTotalList.add(totalColmKeyNode);
				totalNode.setChildren(colmKeyTotalList);
				
				//生成参数节点
				List<TdsFormulaTreeVo> childColmKeyQueryList = new ArrayList<TdsFormulaTreeVo>();
				List<TdsFormulaTreeVo> childColmKeyTotalList = new ArrayList<TdsFormulaTreeVo>();
				for (int i = 0; i < outParamList.size(); i++) {
					TdsFormulaTreeVo outParamVo = outParamList.get(i);
					//查询参数
					TdsFormulaTreeVo queryParamObj = new TdsFormulaTreeVo();
					BeanUtils.copyProperties(outParamVo, queryParamObj);
					String queryNodeId = queryColmKeyNodeId+"___"+outParamVo.getNodeId();
					queryParamObj.setNodeId(queryNodeId);
					queryParamObj.setNodeType(queryValue_nodeType);
					queryParamObj.setId(queryNodeId);
					queryParamObj.setPid(queryColmKeyNodeId);
					queryParamObj.setIconStr("el-icon-position");
					queryParamObj.setIsleaf(1);
					queryParamObj.setDisabled(false);
					childColmKeyQueryList.add(queryParamObj);
					//统计参数
					TdsFormulaTreeVo totalParamObj = new TdsFormulaTreeVo();
					BeanUtils.copyProperties(outParamVo, totalParamObj);
					String totalNodeId = totalColmKeyNodeId+"___"+outParamVo.getNodeId();
					totalParamObj.setNodeId(totalNodeId);
					totalParamObj.setNodeType(totalValue_nodeType);
					totalParamObj.setId(totalNodeId);
					totalParamObj.setPid(totalColmKeyNodeId);
					totalParamObj.setIconStr("el-icon-position");
					totalParamObj.setIsleaf(1);
					totalParamObj.setDisabled(false);
					childColmKeyTotalList.add(totalParamObj);
				}
				queryColmKeyNode.setChildren(childColmKeyQueryList);
				totalColmKeyNode.setChildren(childColmKeyTotalList);
			}
		}
		return result;
	}
	
}
