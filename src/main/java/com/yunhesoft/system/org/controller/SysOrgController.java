package com.yunhesoft.system.org.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

//import com.yunhesoft.tm4.core.http.Res;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.kernel.utils.excel.ExcelImport;
import com.yunhesoft.system.org.entity.dto.SubOrgSearch;
//import com.yunhesoft.tm4.http.BaseController;
import com.yunhesoft.system.org.entity.dto.SysOrgAdd;
import com.yunhesoft.system.org.entity.dto.SysOrgDel;
import com.yunhesoft.system.org.entity.dto.SysOrgHandleDrop;
import com.yunhesoft.system.org.entity.dto.SysOrgMove;
import com.yunhesoft.system.org.entity.dto.SysOrgSelect;
import com.yunhesoft.system.org.entity.dto.SysOrgSort;
import com.yunhesoft.system.org.entity.dto.SysOrgType;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.entity.vo.SysOrgTree;
import com.yunhesoft.system.org.entity.vo.SysOrgTreeData;
import com.yunhesoft.system.org.service.ISysOrgService;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * SysOrgController
 *
 * <AUTHOR>
 * @category SysOrg接口
 * @date 2020/03/10
 */
@Api(tags = "机构管理接口")
@RestController
@RequestMapping("/system/org/sysOrg")
@Log4j2
public class SysOrgController extends BaseRestController {
    @Autowired
    private ISysOrgService iser;

    /**
     * 获取单节点信息数据
     *
     * @param id
     * @return List<SysOrg>
     */
    @ApiOperation(value = "初始化机构信息")
    @RequestMapping(value = "/initOrg", method = {RequestMethod.GET})
    public Res<String> initOrg(String orgname) {
        Boolean bool = null;
        if (iser.initOrg(orgname) != null) {
            bool = true;
        } else {
            bool = false;
        }
        Res<String> res = new Res<String>();
        if (bool) {
            res.setMessage("创建成功");
            res.setSuccess(true);
        } else {
            res.fail("创建失败");
        }
        return res;
    }

    /**
     * 获取单节点信息数据
     *
     * @param id
     * @return List<SysOrg>
     */

    @RequestMapping(value = "/getSysOrg", method = {RequestMethod.GET})
    @ApiImplicitParam(name = "getSysOrg", value = "获取单节点机构信息数据", required = true, paramType = "body", dataType = "id")
    @ApiOperation(value = "获取单节点机构信息数据")
    public List<SysOrg> getSysOrg(@RequestBody String id) {
        List<SysOrg> list = iser.listData(id);
        return list;
    }

    /**
     * 获取树形整体信息
     *
     * @param pid         父节点id
     * @param includeNode ['true':'包含父节点'],['false':'不包含父节点']
     * @param datatype    数据类型['all':'全部下级'],['subordinate':'仅下级']
     * @return List<SysOrg>
     */
    @RequestMapping(value = "/getSysOrgData", method = {RequestMethod.POST})
    @ApiOperation(value = "获取全机构树形信息数据")
    public Res<List<SysOrgTreeData>> getSysOrgData(@RequestBody SysOrgSelect data) {
        List<SysOrgTreeData> list = new ArrayList<SysOrgTreeData>();
        if (data != null && data.getIsParentTree()) { //只获取父机构树形
            list = iser.getParentOrgTree(data);
        } else {
            list = iser.listDatas(data);
        }
        if (StringUtils.isNotEmpty(data.getShowOrgType())) {//需要过滤机构（按类型过滤）
            iser.cutOrgTree(list, data.getShowOrgType());
        }
        if (StringUtils.isNotEmpty(data.getDefaultValue())) {//有默认值，需要对默认值的机构进行添加
            iser.addDefaultTreeNode(list, data.getDefaultValue());
        }
        Res<List<SysOrgTreeData>> res = new Res<List<SysOrgTreeData>>();
        res.setResult(list);
        res.setSuccess(true);
        res.setMessage("成功");
        return res;
    }

    /**
     * 创建数据
     *
     * @param SysOrg
     * @return boolean
     */
    @RequestMapping(value = "/addSysOrg", method = {RequestMethod.POST})
    @ApiImplicitParam(name = "addSysOrg", value = "新增机构", required = true, paramType = "body", dataType = "SysOrgAdd")
    @ApiOperation(value = "新增机构")
    public Res<?> addSysOrg(@RequestBody List<SysOrgAdd> list) {
        if (list != null && list.size() > 0) {
            String str = iser.getDuplicationName(list);
            if ("".equals(str)) {
                if (iser.insertData(list)) {
                    return Res.OK(200, "添加成功");
                } else {
                    return Res.FAIL("添加失败");
                }
            } else {
                return Res.FAIL("名称不能重复");
            }
        } else {
            return Res.FAIL("请添加一个部门");
        }
    }

    /**
     * 更新数据
     *
     * @param SysOrg
     * @return boolean
     */
    @RequestMapping(value = "/updateSysOrg", method = {RequestMethod.POST})
    @ApiImplicitParam(name = "updateSysOrg", value = "修改机构", required = true, paramType = "body", dataType = "SysOrgUp")
    @ApiOperation(value = "修改机构")
    public Res<String> updateSysOrg(@RequestBody SysOrgAdd orgup) {
        boolean bool = true;// iser.getDuplicationName(orgup.getOrgname(), orgup.getPorgcode(),
        // orgup.getOrglevel());
        // 先不校验名称是否重复，修改其他属性无法修改 by x.zhong 2021.5.15
        Res<String> res = new Res<String>();
        if (bool) {
            bool = iser.updateData(orgup);
            res.setSuccess(true);
            res.setMessage("修改成功");
        } else {
            // res.setSuccess(false);
            // res.setError("部门名称已存在");
            res.fail("部门名称已存在");
        }
        return res;
    }

    /**
     * 更新数据
     *
     * @param SysOrg
     * @return boolean
     */
    @RequestMapping(value = "/updateSysOrgList", method = {RequestMethod.POST})
    @ApiImplicitParam(name = "updateSysOrgList", value = "修改机构", required = true, paramType = "body", dataType = "SysOrgUp")
    @ApiOperation(value = "批量修改机构")
    public Res<String> updateSysOrgList(@RequestBody List<SysOrgAdd> list) {
        Res<String> res = new Res<String>();
        boolean ret = iser.updateData(list);
        res.setSuccess(ret);
        res.setMessage("保存" + (ret ? "成功" : "失败") + "！");
        return res;
    }

    /**
     * 删除数据
     *
     * @param SysOrg
     * @return id
     */
    @RequestMapping(value = "/deleteSysOrg", method = {RequestMethod.POST})
    @ApiImplicitParam(name = "deleteSysOrg", value = "删除机构", required = true, paramType = "body", dataType = "SysOrgDel")
    @ApiOperation(value = "删除机构")
    public Res<String> deleteSysOrg(@RequestBody SysOrgDel orgdel) {
        boolean bool = iser.deleteData(orgdel);
        Res<String> res = new Res<String>();
        if (bool) {
            res.setSuccess(true);
            res.setMessage("删除成功");
        } else {
            // res.setSuccess(false);
            // res.setError("删除失败");
            res.fail("删除失败");
        }
        return res;
    }

    /**
     * 移动部门
     *
     * @param SysOrg
     * @return id
     */
    @RequestMapping(value = "/mmoveSysOrg", method = {RequestMethod.POST})
    @ApiImplicitParam(name = "mmoveSysOrg", value = "移动机构", required = true, paramType = "body", dataType = "SysOrgMove")
    @ApiOperation(value = "移动机构")
    public Res<String> moveSysOrg(@RequestBody SysOrgMove orgadd) {
        boolean bool = iser.moveData(orgadd);
        Res<String> res = new Res<String>();
        if (bool) {
            res.setSuccess(true);
            res.setMessage("移动成功");
        } else {
            // res.setSuccess(false);
            // res.setError("移动失败");
            res.fail("移动失败");
        }
        return res;
    }

    /**
     * 部门排序
     *
     * @param SysOrg
     * @return id
     */
    @RequestMapping(value = "/SysOrgSort", method = {RequestMethod.POST})
    @ApiImplicitParam(name = "sysOrgSort", value = "机构排序", required = true, paramType = "body", dataType = "SysOrgSort")
    @ApiOperation(value = "机构排序")
    public Res<String> sysOrgSort(@RequestBody SysOrgSort orgasort) {
        boolean bool = iser.sortData(orgasort);
        Res<String> res = new Res<String>();
        if (bool) {
            res.setSuccess(true);
            res.setMessage("移动成功");
        } else {
            // res.setSuccess(false);
            // res.setError("移动失败");
            res.fail("移动失败");
        }
        return res;
    }

    @RequestMapping(value = "/handleDropSysOrg", method = {RequestMethod.POST})
    @ApiImplicitParam(name = "handleDropSysOrg", value = "机构拖拽调节位置", required = true, paramType = "body", dataType = "SysOrgSort")
    @ApiOperation(value = "机构拖拽调节位置")
    public Res<String> handleDropSysOrg(@RequestBody SysOrgHandleDrop sysOrgHandleDrop) {
        Res<String> res = new Res<String>();
        boolean bool = iser.handleDropSysOrg(sysOrgHandleDrop);
        if (bool) {
            res.setSuccess(true);
            res.setMessage("移动成功");
        } else {
            // res.setSuccess(false);
            // res.setError("移动失败");
            res.fail("移动失败");
        }
        return res;
    }

    @RequestMapping(value = "/orgType", method = {RequestMethod.POST})
    @ApiOperation(value = "获取机构类型数据")
    public Res<List<SysOrgType>> getOrgType() {
        List<SysOrgType> list = iser.getOrgType();
        Res<List<SysOrgType>> res = new Res<List<SysOrgType>>();
        res.setResult(list);
        res.setSuccess(true);
        res.setMessage("成功");
        return res;
    }


    @RequestMapping(value = "/getOrgClassify", method = {RequestMethod.POST})
    @ApiOperation(value = "获取机构分类数据")
    public Res<List<SysOrgType>> getOrgClassify() {
        List<SysOrgType> list = iser.getOrgClassify();
        Res<List<SysOrgType>> res = new Res<List<SysOrgType>>();
        res.setResult(list);
        res.setSuccess(true);
        res.setMessage("成功");
        return res;
    }

    /**
     * 导出Excel
     *
     * @param querySysRoleDto
     */
    @RequestMapping(value = "/toExcel", method = RequestMethod.POST)
    @ApiOperation(value = "机构数据导出Excel")
    public void toExcel(@RequestBody SysOrgSelect data) {
        try {
            List<SysOrgTree> listData = iser.getExportData(data);
            String s = iser.getImportLevelChar();
            String sub = "注意：1、ID列为标识列，空代表添加记录，否则为修改记录； 2、“" + s + "” 代表机构的层级关系；3、标题名称不可以修改。4、新增机构表格中一定存在一个不带‘#’的根节点";
//			Map<String, String> map = iser.getOrgTypeMap();
//			Class<SysOrgTree> clazz = ExcelExport.setRepalce(SysOrgTree.class, "orgType", map);// 设置导出替换值
            Map<String, List<String>> selectMap = new HashMap<String, List<String>>();// 设置下拉框
            selectMap.put("orgTypeLabel", iser.getOrgTypeList());
            selectMap.put("orgClassifyLabel", iser.getOrgClassfList());
            // ExcelExport.exportExcel("组织机构", sub, clazz, listData, selectMap, response);
            ExcelExport.exportExcel("组织机构", sub, false, SysOrgTree.class, listData, selectMap, response);
        } catch (Exception e) {
            log.error("组织机构导出", e);
        }
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "机构数据导入")
    @RequestMapping(value = "/import", method = {RequestMethod.POST})
    public Res<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam("params") String params)
            throws Exception {
        ExcelImportResult<?> result = ExcelImport.importExcel(file.getInputStream(), SysOrgTree.class, 2, 1, false);
        if (result != null) {
            if (result.isVerifyFail()) {// 校验失败
                return Res.FAIL(500, "数据校验失败！");// Res.OK(result.getFailList());// 校验失败的数据
            }
            List<?> list = result.getList();// 导入的结果数据
            if (StringUtils.isNotEmpty(list)) {
                List<SysOrgTree> dataList = (List<SysOrgTree>) list;
                List<SysOrgType> orgTypes = iser.getOrgType();
                Map<String, String> orgTypesMap = new HashMap<>();
                for (SysOrgType orgType : orgTypes) {
                    orgTypesMap.put(orgType.getLabel(), orgType.getValue());
                }

                List<SysOrgType> orgClass = iser.getOrgClassify();
                Map<String, String> orgClassMap = new HashMap<>();
                for (SysOrgType orgType : orgClass) {
                    orgClassMap.put(orgType.getLabel(), orgType.getValue());
                }
                // 将机构类型数据转换为编码
                for (SysOrgTree sysOrgTree : dataList) {
                    String orgTypeLabel = sysOrgTree.getOrgTypeLabel();
                    if (StringUtils.isNotEmpty(orgTypeLabel)) {
                        String[] split = orgTypeLabel.split(",");
                        List<String> orgType = new ArrayList<>();
                        for (String s : split) {
                            String s1 = orgTypesMap.get(s);
                            if (StringUtils.isNotEmpty(s1)) {
                                orgType.add(s1);
                            }
                        }
                        if (StringUtils.isNotEmpty(orgType)) {
                            sysOrgTree.setOrgType(orgType.get(0));
                        }

                    }

                    if (StringUtils.isNotEmpty(sysOrgTree.getOrgClassifyLabel())) {
                        if (orgClassMap.containsKey(sysOrgTree.getOrgClassifyLabel())) {
                            sysOrgTree.setOrgClassify(orgClassMap.get(sysOrgTree.getOrgClassifyLabel()));
                        }
                    }

                }
                try {
                    return Res.OK(iser.importOrgData(dataList, params));
                } catch (Exception e) {
                    return Res.FAIL(500, e.getMessage());
                }
            } else {
                return Res.OK("未解析出导入数据！");
            }
        }
        return Res.FAIL("");
    }

    @RequestMapping(value = "/getParentOrgCode", method = {RequestMethod.POST})
    @ApiOperation(value = "获取父机构代码")
    public Res<?> getParentOrgCode(@RequestParam String orgcode) {
        return Res.OK(iser.getParentOrgCode(orgcode));
    }

    @RequestMapping(value = "/getFullPathOrgName", method = {RequestMethod.POST})
    @ApiOperation(value = "获取机构全路径名称")
    public Res<?> getParentOrgCode(@RequestParam String orgcode, @RequestParam Integer deep) {
        return Res.OK(iser.getFullPathOrgName(orgcode, deep));
    }

    @RequestMapping(value = "/getOrgByOrgName", method = {RequestMethod.POST})
    @ApiOperation(value = "通过机构名称获取机构信息")
    public Res<?> getOrgByOrgName(@RequestParam String orgName) {
        return Res.OK(iser.getOrgByOrgName(orgName));
    }

    @RequestMapping(value = "/getOrgByOrgcode", method = {RequestMethod.POST})
    @ApiOperation(value = "通过机构编码获取机构信息")
    public Res<?> getOrgByOrgcode(@RequestParam String orgcode) {
        return Res.OK(iser.getOrgByOrgcode(orgcode));
    }

    @RequestMapping(value = "/isOrgTypeLevelLow", method = {RequestMethod.POST})
    @ApiOperation(value = "机构编码是否是目标机构类型及以下级别")
    public Res<?> isOrgTypeLevelLow(@RequestParam("orgCode") String orgCode, @RequestParam("orgType") String orgType) {
        return Res.OK(iser.isOrgTypeLevelLow(orgCode, orgType));
    }

    @RequestMapping(value = "/getSubOrgWithOutOrgType", method = {RequestMethod.POST})
    @ApiOperation(value = "通过机构编码获取子机构信息（可以去除无机构类型或指定机构类型的子节点）")
    public Res<?> getSubOrgWithOutOrgType(@RequestBody SubOrgSearch param) {
        return Res.OK(iser.getSubOrgWithOutOrgType(param.getOrgCode(), param.getOrgType()));
//		return Res.OK(iser.getParentOrgByOrgType(param.getOrgCode(),3));
    }

    @RequestMapping(value = "/getSubOrgByOrgType", method = {RequestMethod.POST})
    @ApiOperation(value = "通过机构编码获取子机构信息（需制定具体要获取的子机构类型）")
    public Res<?> getSubOrgByOrgType(@RequestBody SubOrgSearch param) {
        return Res.OK(iser.getSubOrgByOrgType(param.getOrgCode(), param.getOrgType()));
//		return Res.OK(iser.getParentOrgByOrgType(param.getOrgCode(),3));
    }

    @RequestMapping(value = "/getUserCjOrgDm", method = {RequestMethod.POST})
    @ApiOperation(value = "获取人员所在车间代码")
    public Res<?> getUserCjOrgDm() {
        return Res.OK(iser.getUserCjOrgDm());
    }

}
