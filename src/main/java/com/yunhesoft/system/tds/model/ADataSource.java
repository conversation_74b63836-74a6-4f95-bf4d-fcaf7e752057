package com.yunhesoft.system.tds.model;

import com.alibaba.druid.pool.DruidPooledCallableStatement;
import com.alibaba.druid.pool.DruidPooledPreparedStatement;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.aviator.AviatorUtils;
import com.yunhesoft.core.common.utils.BigArithTools;
import com.yunhesoft.core.common.utils.Maths;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.tds.entity.po.TdstableInfo;
import com.yunhesoft.system.tds.entity.vo.TdsInParaRelVo;
import com.yunhesoft.system.tds.entity.vo.TdsTableColumn;
import com.yunhesoft.system.tds.service.IDataSourceInParaRelService;
import com.yunhesoft.system.tds.utils.TdsTools;
import lombok.extern.log4j.Log4j2;

import javax.script.ScriptException;
import java.io.UnsupportedEncodingException;
import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数据源抽象类 <br>
 * 数据源接口的抽象类<br>
 * 该类重写了大部分的数据源接口的函数<br>
 * 仅少部分接口的函数才需要在子类中重写
 *
 * <AUTHOR>
 * @category 数据源抽象类
 * @since $Date: 2010-04-12 14:27:04
 */
@Log4j2
public abstract class ADataSource implements IDataSource {

    private static final long serialVersionUID = 1L;

    // 异步执行数据库脚本查询类
    static class LoadJob implements Callable<ResultSet> {
        private CallableStatement cs = null;
        private PreparedStatement st = null;
        private Integer obji = null;
        private boolean iso = false;// 判断是否为Oracle数据库
        private String msg = null; // 提示信息

        private String err = null; // 错误信息

        public LoadJob(CallableStatement cs) {
            this.cs = cs;
        }

        public LoadJob(CallableStatement cs, Integer obji) {
            this.cs = cs;
            this.obji = obji;
            iso = true;
        }

        public LoadJob(CallableStatement cs, String msg) {
            this.msg = msg;
            this.cs = cs;
        }

        public LoadJob(CallableStatement cs, Integer obji, String msg) {
            this.msg = msg;
            this.cs = cs;
            this.obji = obji;
            iso = true;
        }

        public LoadJob(PreparedStatement st) {
            this.st = st;
        }

        public LoadJob(PreparedStatement st, String msg) {
            this.st = st;
        }

        public ResultSet call() {
            ResultSet rs = null;
            if (cs != null) {
                try {
                    if (iso) {
                        cs.execute();
                        if (obji != null)
                            rs = (ResultSet) cs.getObject(obji.intValue());
                    } else {
                        rs = cs.executeQuery();
                    }
                } catch (SQLException e) {
                    this.printMsg();
                    this.setErr(e.getMessage());
                    log.error(((DruidPooledCallableStatement) cs).getSql(), e);
                }
            } else if (st != null) {
                try {
                    rs = st.executeQuery();
                } catch (SQLException e) {
                    this.printMsg();
                    this.setErr(e.getMessage());
                    log.error(((DruidPooledPreparedStatement) st).getSql(), e);
                }
            }
            return rs;
        }

        /**
         * 打印错误信息
         */
        private void printMsg() {
            if (this.msg != null) {
                log.info(this.msg);
            }
        }

        public String getErr() {
            return err;
        }

        public void setErr(String err) {
            this.err = err;
        }

    }

    /** 数据库连接池Session */
    // protected Session sess = null;
    /**
     * 数据库连接connection
     */
    protected Connection conn = null;
    /**
     * session的时间
     */
    protected Date openSessTime = new Date();

    /**
     * 模块编码
     */
    protected String moduleCode;
    /**
     * 数据源ID 每个数据源或是每一个查询，<br>
     * 均提供一个ID<br>
     * 对于报表中心，则ID是报表中心的数据表格的ID
     */
    protected String dataSourceId;
    /**
     * 数据源名称
     */
    protected String dsName;
    /**
     * 数据源别名
     */
    protected String dsAlias;
    /**
     * 数据源类型 自定义或者是SQL
     */
    protected String dsType;
    /**
     * 数据源数据存储类型<br>
     * 数据源数据集合 dataStroe<br>
     * 数据源内置对象集合 object<br>
     * 该参数决定输出参数显示来自于<br>
     * dataStore或是callFun字段
     */
    protected String dsDataType = "dataStore";
    /**
     * 自定义数据源类
     */
    protected String dsClassName;
    /**
     * 查询SQL文或存储过程
     */
    protected String tdsQuerySql;

    /**
     * 初始化
     */
    protected String tdsInitSql;

    /**
     * 计算脚本
     */
    protected String tdsCalScript;

    /**
     * 编辑后脚本
     */
    protected String tdsAfterEditScript;

    /**
     * 控件是否可以编辑
     */
    protected String tdsCanEditScript;
    /**
     * 更新SQL文或存储过程
     */
    protected String tdsUpdateSql;
    /**
     * 允许保存
     */
    protected boolean allowSaved;
    /**
     * 数据源备注
     */
    protected String memo;
    /**
     * 数据源是否使用
     */
    protected boolean used;
    /**
     * 数据源注册时间
     */
    protected Date regTime;
    /**
     * 自动加载
     */
    protected boolean autoLoad;
    /**
     * Excel数据源存储到数据库
     */
    protected boolean importDataBase;
    /**
     * 当前是否正在调用自动加载函数 （实时）
     */
    // 关键代码 tds.TDataSourceManager.getDataSource(String, boolean, Object) ->
    // ids.load();
    protected boolean nowAutoLoad; // zouhao 2013.09.24日添加
    /**
     * 是否是主库
     */
    protected boolean isMainDB;
    /**
     * 公式调用时是否允许返回空值
     */
    protected boolean allowNull;
    /**
     * 数据源分类id
     */
    protected String categoryId;
    /**
     * 数据源排序
     */
    protected Integer dsSort;
    /**
     * 创建数据源的人员ID
     */
    protected String createUid;
    /**
     * 创建人姓名
     */
    protected String createUname;
    /**
     * 更新数据源人员ID
     */
    protected String updateUid;
    /**
     * 更新数据源人员姓名
     */
    protected String updateUname;
    /**
     * 更新日期
     */
    protected Date updateDate;
    /**
     * 数据库结果级信息
     */
    private LinkedHashMap<String, TOutPara> rsCols;
    /**
     * 输出参数列表
     */
    protected List<TOutPara> tOutParas;
    /**
     * 输出关键参数列表
     */
    protected List<TOutPara> tOutKeyParas;
    /**
     * 输入参数列表
     */
    protected List<TInPara> tInParas;
    /**
     * 数据集
     */
    protected TDataStore tds;
    /**
     * 数据源管理器
     */
    protected TDataSourceManager dsm;
    /**
     * 后期处理脚本
     */
    protected String script;
    /**
     * 错误信息
     */
    protected String errorInfo;
    /**
     * Excel数据源文件路径
     */
    protected String excelPath;
    /**
     * Excel数据源是否未审核的数据，默认false
     */
    protected boolean showUnCheckExcel = false;

    /**
     * 内置对象 公式解析调用的Java数据对象集合
     */
    Map<String, Object> mapCustomObj;
    /**
     * 初始化时输出参数个数
     */
    protected Integer initOutParaSize = 0;
    /**
     * Excel数据源是否自动建表
     */
    protected boolean excelAutoCreate = false;
    /**
     * 外部数据连接配置ID
     */
    protected String dbConnInfoId;
    /**
     * 数据库表名称
     */
    protected String dbTableName;
    /**
     * Excel数据源，是否多SHEET模式：0单SHEET（默认），1多SHEET
     **/
    protected boolean excelMultiSheet = false;
    /**
     * 是否为流程使用的数据源
     **/
    protected boolean isFlow = false;

    protected Integer bpmMode = 0;// 0：查询静态化并审核通过的数据（用于后续数据源公式引用等） 1：动态数据 2：静态数据（不判断审核）

    protected Integer tdsBpmAudit = null;// 数据源工作流审核数据

    // 模板id
    protected String templateId = null;

    protected Boolean advancedSearch = false;

    /**
     * 按关键列解析
     **/
    protected boolean findByKey = false;

    /**
     * 加载时是否初始化执行语句
     **/
    private boolean isLoadInitData = false;
    // 子数据源
    protected String cTdsName = "";
    // 子数据源别名
    protected String cTdsAlias = "";

    // 获取数据是否为渲染函数的结果值
    protected boolean showRenderValue = false;

    // 获取数据源初始化的类型
    protected Integer tdsInitType = 2;

    // 获取数据源编辑绑定的数据源
    protected String bindTdsAlias = "";
    // 是否只加载dataList
    private boolean isLoadDataList = false;
    // 数据源数据
    private List<Map<String, Object>> dataList;

    private String inParaAliasFront; // 前端输入参数别名

    // /** 当前页 */
    // protected int currPage = 0;
    // /** 每页显示个数 */
    // protected int pageSize = 50;

    public ADataSource() {
        tds = new TDataStore((IDataSource) this);
        mapCustomObj = new LinkedHashMap<String, Object>();
        errorInfo = "";
        tInParas = new ArrayList<TInPara>();
        tOutParas = new ArrayList<TOutPara>();
        rsCols = new LinkedHashMap<String, TOutPara>();
        dataList = new ArrayList<Map<String, Object>>();
        inParaAliasFront = "";
    }

    // ***************************************************************************
    // * 属性参数
    // **************************************************************************/

    /**
     * 初始化父对象
     *
     * @param dsm 父对象(数据源管理器)
     * @category 初始化父对象
     */
    public void initParent(TDataSourceManager dsm) {
        if (dsm == null) {
            errorInfo += "<br>初始化数据源管理器出错，数据源管理器为null";
        }
        this.dsm = dsm;
    }

    public String getInParaAliasFront() {
        return inParaAliasFront;
    }

    public void setInParaAliasFront(String inParaAliasFront) {
        this.inParaAliasFront = inParaAliasFront;
    }

    public boolean isMainDB() {
        return isMainDB;
    }

    public void setMainDB(boolean isMainDB) {
        this.isMainDB = isMainDB;
    }

    public String getDataSourceAlias() {
        return dsAlias;
    }

    public TDataStore getDataStore() {
        return tds;
    }

    public String getDSAlias() {
        return dsAlias;
    }

    public String getTdsQuerySql() {
        return tdsQuerySql;
    }

    public void setTdsQuerySql(String tdsQuerySql) {
        this.tdsQuerySql = tdsQuerySql;
    }

    public String getTdsUpdateSql() {
        return tdsUpdateSql;
    }

    public void setTdsUpdateSql(String tdsUpdateSql) {
        this.tdsUpdateSql = tdsUpdateSql;
    }

    public String getDSClassName() {
        return dsClassName;
    }

    public String getDSName() {
        return dsName;
    }

    public String getDSType() {
        return dsType;
    }

    public void setDataStore(TDataStore tds) {
        this.tds = tds;

    }

    public void setDSAlias(String alias) {
        this.dsAlias = alias;

    }

    public void setDSClassName(String className) {
        this.dsClassName = className;

    }

    public void setDSName(String name) {
        dsName = name;

    }

    public void setDSType(String type) {
        dsType = type;

    }

    public void setInPara(List<TInPara> inList) {
        tInParas = inList;

    }

    public void setOutPara(List<TOutPara> outList) {
        tOutParas = outList;

    }

    public String getModuleCode() {
        return moduleCode;
    }

    public void setModuleCode(String moduleCode) {
        this.moduleCode = moduleCode;
    }

    public Boolean getAllowSaved() {
        return allowSaved;
    }

    public void setAllowSaved(Boolean allowSaved) {
        this.allowSaved = allowSaved;
    }

    public String getMemo() {
        return memo;
    }

    public void setRegTime(Date regTime) {
        this.regTime = regTime;
    }

    public Date getRegTime() {
        return regTime;
    }

    public Boolean getUsed() {
        return used;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public void setUsed(Boolean used) {
        this.used = used;
    }

    public Boolean getAutoLoad() {
        return autoLoad;
    }

    public void setAutoLoad(Boolean autoLoad) {
        this.autoLoad = autoLoad;

    }

    public Boolean getImportDataBase() {
        return importDataBase;
    }

    public void setImportDataBase(Boolean importDataBase) {
        this.importDataBase = importDataBase;
    }

    public void setAllowNull(Boolean allowNull) {
        this.allowNull = allowNull;
    }

    public boolean isAllowNull() {
        return allowNull;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public Integer getDsSort() {
        return dsSort;
    }

    public void setDsSort(Integer dsSort) {
        this.dsSort = dsSort;
    }

    public Boolean getNowAutoLoad() {
        return nowAutoLoad;
    }

    public void setNowAutoLoad(Boolean nowAutoLoad) {
        this.nowAutoLoad = nowAutoLoad;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public Map<String, Object> getMapCustomObj() {
        return mapCustomObj;
    }

    public void setMapCustomObj(Map<String, Object> mapCustomObj) {
        this.mapCustomObj = mapCustomObj;
    }

    public String getDsDataType() {
        return dsDataType;
    }

    public void setDsDataType(String dsDataType) {
        this.dsDataType = dsDataType;
    }

    public void setExcelPath(String excelPath) {
        this.excelPath = excelPath;
    }

    public void setShowUnCheckExcel(boolean showUnCheckExcel) {
        this.showUnCheckExcel = showUnCheckExcel;
    }

    public boolean isExcelAutoCreate() {
        return excelAutoCreate;
    }

    public void setExcelAutoCreate(boolean excelAutoCreate) {
        this.excelAutoCreate = excelAutoCreate;
    }

    public boolean isExcelMultiSheet() {
        return excelMultiSheet;
    }

    public void setExcelMultiSheet(boolean excelMultiSheet) {
        this.excelMultiSheet = excelMultiSheet;
    }

    public String getDbConnInfoId() {
        return dbConnInfoId;
    }

    public void setDbConnInfoId(String dbConnInfoId) {
        this.dbConnInfoId = dbConnInfoId;
    }

    public String getDbTableName() {
        return dbTableName;
    }

    public void setDbTableName(String dbTableName) {
        this.dbTableName = dbTableName;
    }

    /***************************************************************************
     * 数据源操作函数
     **************************************************************************/

    public Integer getBpmMode() {
        return bpmMode;
    }

    public void setBpmMode(Integer bpmMode) {
        this.bpmMode = bpmMode;
    }

    /**
     * @return the templateId
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     * @param templateId the templateId to set
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    /**
     * 清除数据源数据
     *
     * @category 清除数据源数据
     */
    public void clear() {
        tds.clear();
        this.clearData();
    }

    /**
     * 清除输出参数
     *
     * @category 清除输出参数
     */
    public void clearOutParas() {
        if (this.getInitOutParaSize() <= 0) {
            if (tOutParas != null) {
                this.tOutParas.clear();
            }
            if (tOutKeyParas != null) {
                tOutKeyParas.clear();
                tOutKeyParas = null;
            }
        }
    }

    /***************************************************************************
     * 输入参数操作函数
     **************************************************************************/

    /**
     * 设置默认参数值
     *
     * @param ip
     * @param okey
     * @param ovalue
     */
    @SuppressWarnings("unchecked")
    private void setDefaultValue(TInPara ip, Object okey, Object ovalue) {
        List<Object> keylist = new ArrayList<Object>();
        List<Object> valuelist = new ArrayList<Object>();
        if (ip.getDataType().equals(IDataSource.DataType.tdsBoolean)) {
            if (okey == null)
                okey = false;
            if (ovalue == null)
                ovalue = false;
        } else if (ip.getDataType().equals(IDataSource.DataType.tdsString)) {
            if (okey == null)
                okey = "";
            if (ovalue == null)
                ovalue = "";
        } else if (ip.getDataType().equals(IDataSource.DataType.tdsDate)) {
            if (okey == null)
                okey = new Date();
            if (ovalue == null)
                ovalue = new Date();
        } else if (ip.getDataType().equals(IDataSource.DataType.tdsDouble)) {
            if (okey == null)
                okey = 0;
            if (ovalue == null)
                ovalue = 0;
        } else if (ip.getDataType().equals(IDataSource.DataType.tdsLong)) {
            if (okey == null) {
                okey = 0;
            } else {// 转换为Long类型
                okey = Double.valueOf(okey.toString()).longValue();
            }
            if (ovalue == null)
                ovalue = 0;
        } else if (ip.getDataType().equals(IDataSource.DataType.tdsInteger)) {
            if (okey == null) {
                okey = 0;
            } else {// 转换为Integer类型
                okey = Double.valueOf(okey.toString()).intValue();
            }
            if (ovalue == null)
                ovalue = 0;
        } else if (ip.getDataType().equals(IDataSource.DataType.tdsJson)) {
            if (okey == null)
                okey = "";
            if (ovalue == null)
                ovalue = "";
        }
        if (okey instanceof Collection<?>) {
            Collection<Object> okeys = (Collection<Object>) okey;
            keylist.addAll(okeys);
        } else {
            keylist.add(okey);
        }
        if (ovalue instanceof Collection<?>) {
            Collection<Object> ovalues = (Collection<Object>) ovalue;
            valuelist.addAll(ovalues);
        } else {
            valuelist.add(ovalue);
        }
        int l = keylist.size() < valuelist.size() ? keylist.size() : valuelist.size();
        for (int j = 0; j < l; j++) {
            Object k = keylist.get(j);
            Object v = valuelist.get(j);
            ip.getDefaultValueMap().put(k, v);
        }
        if (keylist != null && keylist.size() > 0) {// 设置输入参数默认值
            ip.setValue(keylist.get(0));
        }
        if (valuelist != null && valuelist.size() > 0) {// 设置输入参数默认显示值
            ip.setRawValue(valuelist.get(0));
        }
    }

    /**
     * 替换变量
     *
     * @param str
     * @param map
     * @return
     */
//	private String replaceVar(String s, Map<String, String> map) {
//		String str = s;
//		try {
//			String rex = "[@][_a-zA-Z][_a-zA-Z0-9]*";
//			Pattern pattern = Pattern.compile(rex);
//			Matcher m = pattern.matcher(str);
//			while (m.find()) {
//				String param = m.group().substring(1);
//				String value = map.get(param);
//				if (value != null) {
//					str = str.replaceFirst(m.group(), value);
//				}
//			}
//		} catch (Exception e) {
//			log.error("", e);
//		}
//		return str;
//	}

    /**
     * 分析数据源输入参数默认值
     *
     * @category 分析数据源输入参数默认值
     */

    public List<TInPara> parseInParaDefaultValue() {
        Map<String, String> map = new LinkedHashMap<String, String>();
        return this.parseInParaDefaultValue(map);
    }

    /**
     * 获得数据源
     *
     * @param alias
     * @param valueMap
     * @return
     */
    private IDataSource getDs(String alias, Map<String, Object> valueMap) {
        IDataSource ds = dsm.getDataSource(alias);
        try {
            ds = dsm.getDataSource(alias);
            if (ds != null) {
                if (StringUtils.isNotEmpty(valueMap)) {
                    for (String key : valueMap.keySet()) {
                        ds.setInParaByAlias(key, valueMap.get(key));
                    }
                }
                if (!ds.getAutoLoad()) {
                    ds.load();
                }
            }
        } catch (Exception e) {
            log.error("输入参数数据源加载错误[" + alias + "]", e);
        }
        return ds;
    }

    /**
     * 加载脚本中的数据源
     *
     * @param script
     * @return
     */
    private Map<String, IDataSource> parseInParaTds(String script, Map<String, Object> valueMap) {
        Map<String, IDataSource> map = new LinkedHashMap<String, IDataSource>();
        if (script != null && script.trim().length() > 0) {
            List<String> list = this.parseDataSourceAliasList(script);
            if (list != null && list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    String s = list.get(i);
                    if (map.get(s) == null) {
                        map.put(s, getDs(s, valueMap));// 此处需考虑下拉框支持带参数的数据源加载
                    }
                }
            }
        }
        return map;
    }

    /**
     * 输入参数脚本校验
     *
     * @param script
     * @return
     * @throws Exception
     */
    public Object checkInParaScript(String script) throws Exception {
        Map<String, IDataSource> map = this.parseInParaTds(script, null);
        return this.eval(script, map);
    }

    /**
     * @param script   公式
     * @param valueMap 输入参数
     * @return
     * @throws Exception
     * @category 解析数据源公式
     */
    public Object parseScript(String script, Map<String, Object> valueMap) throws Exception {
        Map<String, IDataSource> map = this.parseInParaTds(script, valueMap);
        return this.eval(script, map);
    }

    public TInPara parseInParaDefaultValue(TInPara ip, Map<String, Object> valueMap) {
        String keyScript = null;
        String valueScript = null;
        try {

            keyScript = ip.getDefaultKeyScript();
            if (keyScript == null) {
                keyScript = "";
            }
            valueScript = ip.getDefaultValueScript();
            if (valueScript == null) {
                valueScript = "";
            }
            Object okey = null;// 初始化值
            Object ovalue = null;// 初始化名称
            if (ip.getDataType().equals(IDataSource.DataType.tdsJson)) {// json格式不解析
                okey = keyScript;// 初始化值
                ovalue = valueScript;// 初始化名称
            } else {
                Map<String, IDataSource> dataMap = new HashMap<String, IDataSource>();
                if (keyScript.length() > 0 || valueScript.length() > 0) {
                    List<String> scriptList = new ArrayList<String>();
                    scriptList.add(keyScript);
                    scriptList.add(valueScript);
                    List<String> dsList = this.parseDataSourceAliasList(scriptList);// 数据源别名列表
                    if (StringUtils.isNotEmpty(dsList)) {
                        for (String s : dsList) {
                            if (!dataMap.containsKey(s)) {
                                dataMap.put(s, getDs(s, valueMap));
                            }
                        }
                    }
                    List<Object> valList = this.eval(scriptList, dataMap);// 计算默认脚本
                    if (valList != null && valList.size() >= 2) {
                        okey = valList.get(0);
                        ovalue = valList.get(1);
                    }
                }
            }
            this.setDefaultValue(ip, okey, ovalue);
        } catch (Exception e) {
            log.error("解析输入参数默认关键脚本或默认数据脚本出错:" + keyScript + " = " + valueScript, e);
            errorInfo += "<br>解析输入参数默认关键脚本或默认数据脚本出错:<br>" + e.getMessage();
        }
        return ip;
    }

    /**
     * 数据源输入参数默认值
     */
    public List<TInPara> parseInParaDefaultValue(Map<String, String> mapInParas) {

        if (StringUtils.isNotEmpty(tInParas)) {
            String valueScript = "";
            String keyScript = "";
            try {
                // Map<String, IDataSource> dataMap = new HashMap<String, IDataSource>();
                Map<String, Object> valueMap = new HashMap<String, Object>();
                LinkedHashMap<String, TdsInParaRelVo> relMap = SpringUtils.getBean(IDataSourceInParaRelService.class)
                        .getInParaRelVoMap(this.dsAlias);
                if (StringUtils.isNotEmpty(mapInParas)) {
                    for (String ky : mapInParas.keySet()) {
                        valueMap.put(ky, mapInParas.get(ky));
                    }
                }
                if (StringUtils.isEmpty(relMap)) {// 没有联动功能，正常加载
                    for (int i = 0; i < tInParas.size(); i++) {
                        TInPara ip = tInParas.get(i);
                        // dataMap.clear();
                        this.parseInParaDefaultValue(ip, valueMap);
                        if (mapInParas.get(ip.getParaAlias()) != null) {// 传入的默认值
                            ip.setValue(mapInParas.get(ip.getParaAlias()));
                        }
                    }
                } else {// 有联动功能
                    Map<String, TInPara> inParaMap = new HashMap<String, TInPara>();
                    // 1、先计算没有关联关系的输入参数默认值
                    for (int i = 0; i < tInParas.size(); i++) {
                        TInPara ip = tInParas.get(i);
                        inParaMap.put(ip.getParaAlias(), ip);
                        if (!relMap.containsKey(ip.getParaAlias())) {
                            // dataMap.clear();
                            this.parseInParaDefaultValue(ip, valueMap);
                            if (ip.getValue() != null) {
                                if (valueMap.get(ip.getParaAlias()) == null) {// 页面传入值为空时才加载默认值
                                    valueMap.put(ip.getParaAlias(), ip.getValue());
                                }
                            }
                        }
                    }
                    // 2.计算有关联关系的输入参数，按照顺序计算
                    for (String alias : relMap.keySet()) {
                        TInPara ip = inParaMap.get(alias);
                        if (ip != null) {
                            // TdsInParaRelVo relVo = relMap.get(alias);
                            this.parseInParaDefaultValue(ip, valueMap);
                            if (ip.getValue() != null) {
                                if (valueMap.get(ip.getParaAlias()) == null) {// 页面传入值为空时才加载默认值
                                    valueMap.put(ip.getParaAlias(), ip.getValue());
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析输入参数默认关键脚本或默认数据脚本出错:" + keyScript + " = " + valueScript, e);
                errorInfo += "<br>解析输入参数默认关键脚本或默认数据脚本出错:<br>" + e.getMessage();
            }

        }

        return tInParas;
    }

    /**
     * 返回脚本中涉及到的数据源别名列表
     *
     * @param script 脚本字串
     * @return 数据源别名列表
     * @category 返回脚本中涉及到的数据源别名列表
     */
    private List<String> parseDataSourceAliasList(String script) {
        if (script == null) {
            return null;
        }
        return this.getDsm().parseDataSourceAliasList(script);
    }

    /**
     * 返回脚本中涉及到的数据源别名列表
     *
     * @param script 脚本字串
     * @return 数据源别名列表
     * @category 返回脚本中涉及到的数据源别名列表
     */
    private List<String> parseDataSourceAliasList(List<String> scriptList) {
        if (scriptList == null || scriptList.size() == 0) {
            return null;
        }
        List<String> rtnList = new ArrayList<String>();
        for (String s : scriptList) {
            List<String> list = this.parseDataSourceAliasList(s);
            if (StringUtils.isNotEmpty(list)) {
                for (String alias : list) {
                    if (!rtnList.contains(alias)) {
                        rtnList.add(alias);
                    }
                }
            }
        }
        // return this.dsm.parseDataSourceAliasList(script);
        return rtnList;
    }


    /**
     * 脚本计算
     *
     * @param s   计算公式
     * @param map 参数
     * @return
     * @throws Exception
     */
    private Object eval(String s, Map<String, IDataSource> map) throws Exception {
        return this.getDsm().getScrictDefaultValue(s, map);
    }

    /**
     * 脚本计算
     *
     * @param script
     * @param map
     * @return
     * @throws Exception
     */
    private List<Object> eval(List<String> scriptList, Map<String, IDataSource> map) throws Exception {
        List<Object> value = new ArrayList<Object>();
        if (scriptList != null && scriptList.size() > 0) {
            for (String s : scriptList) {
                if (s != null && s.length() > 0) {
                    Object val = this.eval(s, map);// Object val = jse.eval(s);
                    value.add(val);
                } else {
                    value.add(null);
                }
            }
        }
        return value;
    }

    /**
     * 读取数据源的输入参数对象列表
     *
     * @return 输入参数对象列表
     * @category 读取数据源的输入参数列表
     */
    public List<TInPara> getInParaList() {
        return tInParas;
    }

    /**
     * 返回指定输入参数序号的输入参数对象
     *
     * @param paraid 输入参数序号
     * @return 输入参数对象
     * @category 返回指定输入参数序号的输入参数对象
     */
    public TInPara getInPara(int paraId) {
        return tInParas.get(paraId);
    }

    public TInPara getInParaByAlias(String paraAlias) {
        return this.getInParaByAlias(paraAlias, tInParas);
    }

    public TInPara getInParaByAlias(String paraAlias, List<TInPara> list) {
        TInPara rtip = null;
        if (list != null) {
            for (TInPara tip : list) {
                if (tip.getParaAlias().equalsIgnoreCase(paraAlias)) {
                    rtip = tip;
                    break;
                }
            }
        }
        return rtip;
    }

    /**
     * 返回指定输入别名的输入参数对象
     *
     * @param paraalias 参数别名
     * @return 输入参数对象
     * @category 返回指定输入别名的输入参数对象
     */
    public TInPara getInPara(String paraName, List<TInPara> list) {
        TInPara rtip = null;
        for (TInPara tip : list) {
            if (tip.getParaName().equalsIgnoreCase(paraName)) {
                rtip = tip;
                break;
            }
        }
        return rtip;
    }

    public TInPara getInPara(String paraName) {
        return this.getInPara(paraName, tInParas);
    }

    public void sortInPara() {
        Collections.sort(tInParas, new Comparator<TInPara>() {
            public int compare(TInPara arg0, TInPara arg1) {
                if (arg0.getParaID() > arg1.getParaID()) {
                    return 1;
                } else if (arg0.getParaID() < arg1.getParaID()) {
                    return -1;
                } else {
                    return 0;
                }
            }
        });
        for (int i = 0; i < tInParas.size(); i++) {
            tInParas.get(i).setParaID(i);
        }
    }

    public void sortOutPara() {
        Collections.sort(tOutParas, new Comparator<TOutPara>() {
            public int compare(TOutPara arg0, TOutPara arg1) {
                if (arg0.getID() > arg1.getID()) {
                    return 1;
                } else if (arg0.getID() < arg1.getID()) {
                    return -1;
                } else {
                    return 0;
                }
            }
        });
        for (int i = 0; i < tOutParas.size(); i++) {
            tOutParas.get(i).setID(i);
        }
    }

    /**
     * 读取输入参数名值对
     *
     * @return Map<String, String>名值对列表
     * @category 读取输入参数名值对
     */
    public Map<String, String> getInParaNameFunList() {
        if (tInParas == null)
            return null;
        Map<String, String> map = new LinkedHashMap<String, String>();
        Iterator<TInPara> it = tInParas.iterator();
        while (it.hasNext()) {
            TInPara para = it.next();
            map.put(para.getParaName() + "(" + para.getParaAlias() + ")", para.getIF());
        }
        return map;
    }

    /**
     * 增加一个输入参数
     *
     * @param inpara
     * @category 增加一个输入参数
     */
    public void addInPara(TInPara inpara) {
        inpara.setParaID(tInParas.size());
        tInParas.add(inpara);
    }

    /**
     * 设置输入参数值
     *
     * @param values 参数对象数组
     * @category 设置输入参数值
     */
    public void setInPara(Object... values) {
        for (int i = 0; i < values.length; i++) {
            if (i < tInParas.size()) {
                tInParas.get(i).setValue(values[i]);
            }
        }
    }

    /**
     * 设置输入参数值
     *
     * @param paraalias 参数别名
     * @param value     参数值
     * @category 设置输入参数值
     */
    public void setInPara(String paraName, Object value) {
        TInPara tin = getInPara(paraName);
        if (tin != null)
            tin.setValue(value);
    }

    public void setInParaByAlias(String paraAlias, Object value) {
        TInPara tin = getInParaByAlias(paraAlias);
        if (tin != null)
            tin.setValue(value);
    }

    public Iterator<TInPara> getInParaIterator() {
        if (tInParas == null)
            tInParas = new ArrayList<TInPara>();
        return tInParas.iterator();
    }

    /***************************************************************************
     * 输出参数操作函数
     **************************************************************************/

    /**
     * 读取数据源的输出参数列表
     *
     * @return 输出参数列表对象
     * @category 读取数据源的输出参数列表
     */
    public List<TOutPara> getOutParaList() {
        return tOutParas;
    }

    /**
     * 读取数据源的输出参数列表(仅isKey为真)
     *
     * @return 输出参数列表对象
     * @category 读取数据源的输出参数列表(仅isKey为真)
     */
    public List<TOutPara> getOutParaListByKey() {
        if (tOutKeyParas == null) {
            tOutKeyParas = new ArrayList<TOutPara>();
            for (TOutPara op : tOutParas) {
                if (op.getIsKey()) {
                    tOutKeyParas.add(op);
                }
            }
        }
        return tOutKeyParas;
    }

    /**
     * 返回指定输出参数序号的输出参数对象
     *
     * @param paraid 输出参数序号
     * @return 输出参数对象
     * @category 返回指定输出参数序号的输出参数对象
     */
    public TOutPara getOutParaById(int paraId) {
        if (paraId >= 0 && paraId < tOutParas.size())
            return tOutParas.get(paraId);
        else
            return null;
    }

    /**
     * 返回指定输出参数别名的输出参数对象
     *
     * @param paraalias 输出参数别名
     * @return 输出参数对象
     * @category 返回指定输出参数别名的输出参数对象
     */
    public TOutPara getOutParaByName(String paraName) {
        TOutPara top = null;
        for (TOutPara op : tOutParas) {
            if (op.getName().equalsIgnoreCase(paraName)) {
                top = op;
                break;
            }
        }
        return top;
    }

    public TOutPara getOutParaByAlias(String paraAlias) {
        TOutPara top = null;
        for (TOutPara op : tOutParas) {
            if (op.getAlias().equalsIgnoreCase(paraAlias)) {
                top = op;
                break;
            }
        }
        return top;
    }

    /**
     * 读取输出参数名值对
     *
     * @return Map<String, String>名值对列表
     * @category 读取输出参数名值对
     * @deprecated
     */
    public Map<String, String> getOutParaNameFunListOld() {
        Map<String, String> map = new LinkedHashMap<String, String>();
        // boolean autoload = getAutoLoad();
        // boolean haskey = false;
        // 读取keyList
        List<TOutPara> keyList = new ArrayList<TOutPara>();
        Iterator<TOutPara> it = tOutParas.iterator();
        while (it.hasNext()) {
            TOutPara para = it.next();
            if (para.getIsKey()) {
                // haskey = true;
                keyList.add(para);
            }
        }
        // 有关键字,根据数据读取关键字内容
        if (keyList.size() > 0) {
            int rowcount = tds.getRowCount();
            // 如果有关键字且有数据
            if (rowcount > 0) {
                // 循环数据
                for (int i = 0; i < rowcount; i++) {
                    // 查找关键字的值
                    it = keyList.iterator();
                    String s = "", s1 = "";
                    while (it.hasNext()) {
                        TOutPara para = it.next();
                        Object o = tds.get(i).get(para.getID());
                        String v = o.toString();
                        if (para.getDataType() == IDataSource.DataType.tdsString) {
                            v = "\"" + o.toString() + "\"";
                        }
                        if (it.hasNext()) {
                            s += tds.get(i).get(para.getID()) + ",";
                            s1 += v + ",";
                        } else {
                            s += tds.get(i).get(para.getID());
                            s1 += v;
                        }
                    }
                    // 放入到map中
                    s1 = "$" + getDSAlias() + ".get(" + s1 + ")";
                    map.put(s, s1);
                }
            } else {
                // 有关键字无数据，非自动加载或是给定条件无加载数据
                // 查找关键字的值
                it = keyList.iterator();
                String s = "", s1 = "";
                while (it.hasNext()) {
                    TOutPara para = it.next();
                    s += para.getName();
                    if (para.getDataType() == IDataSource.DataType.tdsString) {
                        s1 += "\"@" + para.getName() + "\"";
                    } else {
                        s1 += "@" + para.getName();
                    }
                    if (it.hasNext()) {
                        s1 += ",";
                        s += ",";
                    }
                }
                // 放入到map中
                s1 = "$" + getDSAlias() + ".get(" + s1 + ")";
                map.put(s, s1);
            }
        } else {
            // 如果无关键字
            it = tOutParas.iterator();
            while (it.hasNext()) {
                TOutPara para = it.next();
                map.put(para.getName() + "(" + para.getAlias() + ")", para.getIF());
            }
        }
        return map;
    }

    /**
     * 读取输出参数名值对
     *
     * @return Map<String, String>名值对列表
     * @category 读取输出参数名值对
     */
    public Map<String, String> getOutParaNameFunList() {
        Map<String, String> map = new LinkedHashMap<String, String>();
        if (tOutParas != null && tOutParas.size() > 0) {// 有输出参数
            for (TOutPara temp : tOutParas) {
                map.put(temp.getName() + "(" + temp.getAlias() + ")",
                        "$" + dsAlias + ".getColValues(\"" + temp.getName() + "\")");
            }
        }
        return map;
    }

    /**
     * 读取输出扩展参数名值对
     *
     * @category 读取输出扩展参数名值对
     */
    public Map<String, String> getOutParaNameFunListExt() {
        Map<String, String> map = new LinkedHashMap<String, String>();
        Iterator<TOutPara> it = tOutParas.iterator();
        while (it.hasNext()) {
            TOutPara para = it.next();
            map.put(para.getName() + "(" + para.getAlias() + ")列表",
                    "$" + dsAlias + ".getColValues(\"" + para.getName() + "\");");
        }
        map.put("====其它函数===", "");
        map.put("查找(\"条件\"，\"列名\")", "$" + dsAlias + ".find(\"?\",\"?\");");
        map.put("查找(\"条件\"，列号)", "$" + dsAlias + ".find(\"?\",?);");
        map.put("总行数()", "$" + dsAlias + ".getRowCount();");
        map.put("总列数()", "$" + dsAlias + ".getColCount();");
        map.put("移到最前()", "$" + dsAlias + ".moveFirst();");
        map.put("移到最后()", "$" + dsAlias + ".moveLast();");
        map.put("前移一条()", "$" + dsAlias + ".movePrevious();");
        map.put("后移一条()", "$" + dsAlias + ".moveNext();");
        map.put("移到(行号)", "$" + dsAlias + ".moveTo(?);");
        map.put("取值(行号,列号)", "$" + dsAlias + ".get(?,?);");
        map.put("取值(单元格ID)", "$" + dsAlias + ".getByCellID(?);");
        map.put("取当前行号()", "$" + dsAlias + ".getCurrRowId()");
        map.put("求和(\"列名\")", "$" + dsAlias + ".sum(\"@列名\")");
        map.put("求和(列号)", "$" + dsAlias + ".sum(@列号)");
        map.put("平均值(\"列名\")", "$" + dsAlias + ".avg(\"@列名\")");
        map.put("平均值(列号)", "$" + dsAlias + ".avg(@列号)");
        map.put("平均值(\"列名\",不包含空值)", "$" + dsAlias + ".avg(\"@列名\",false)");
        map.put("平均值(列号,不包含空值)", "$" + dsAlias + ".avg(@列号,false)");
        return map;
    }

    /**
     * 读取输出参数名值对
     *
     * @param pId 关键列
     * @return Map<String, String>名值对列表
     * @category 读取输出参数名值对
     */
    public Map<String, String> getOutParaNameFunList(String pId) {
        Map<String, String> map = new LinkedHashMap<String, String>();
        if (getAutoLoad()) {
            Iterator<TOutPara> it = tOutParas.iterator();
            while (it.hasNext()) {
                TOutPara para = it.next();
                map.put(para.getName(), pId + "." + para.getIFN());
            }
        } else {
            boolean b = false;
            Iterator<TOutPara> it = tOutParas.iterator();
            while (it.hasNext()) {
                if (it.next().getIsKey()) {
                    b = true;
                    break;
                }
            }
            it = tOutParas.iterator();
            if (b) {
                while (it.hasNext()) {
                    TOutPara para = it.next();
                    map.put(para.getName(), pId);// + "." + para.getIFN());
                }
            }
        }
        return map;
    }

    /**
     * 返回输出参数二级参数的个数
     *
     * @return
     * @category 返回输出参数二级参数的个数
     */
    public int getOutParaNameFunChildCount() {
        int count = 0;
        if (getAutoLoad()) {
            count = tOutParas.size();
        }
        Iterator<TOutPara> it = tOutParas.iterator();
        while (it.hasNext()) {
            if (it.next().getIsKey()) {
                count = tOutParas.size();
                break;
            }
        }
        return count;
    }

    /**
     * 增加一个输出参数
     *
     * @param outpara 输出参数
     * @category 增加一个输出参数
     */
    public void addOutPara(TOutPara outpara) {
        tOutParas.add(outpara);
        tds.addCol();
    }

    /**
     * 读取列名
     *
     * @param colId 列号
     * @return
     * @category 读取列名
     */
    public String getColName(int colId) {
        if (colId >= 0 && colId <= tOutParas.size()) {
            return tOutParas.get(colId - 1).getName();
        } else {
            return "";
        }
    }

    /**
     * 读取列名
     *
     * @param colAlias 列别名
     * @return
     * @category 读取列名
     */
    public String getColName(String colAlias) {
        String name = "";
        for (int i = 0; i < tOutParas.size(); i++) {
            if (tOutParas.get(i).getAlias().equalsIgnoreCase(colAlias)) {
                name = tOutParas.get(i).getName();
                break;
            }
        }
        return name;
    }

    /**
     * 读取列别名
     *
     * @param colId 列号
     * @return
     * @category 读取列别名
     */
    public String getColAlias(int colId) {
        if (colId - 1 >= 0 && colId - 1 < tOutParas.size() - 1) {
            return tOutParas.get(colId - 1).getAlias();
        } else {
            return "";
        }
    }

    /***************************************************************************
     * 数据源数据操作函数
     **************************************************************************/
    /**
     * 设置排序条件
     *
     * @param sort 排序条件
     * @category 设置排序条件
     */
    public void setSort(String sort) {
        tds.setSort(sort);
    }

    /**
     * 排序
     *
     * @category 排序
     */
    public void sort() {
        tds.sort();
    }

    /**
     * 设置过滤条件
     *
     * @param filter 过滤条件
     * @category 设置过滤条件
     */
    public void setFilter(String filter) {
        tds.setFilter(filter);
    }

    /**
     * 过滤
     *
     * @category 过滤
     */
    public void filter() {
        tds.filter();
    }

    /**
     * 过滤
     *
     * @param filter (String) 过滤条件
     * @return IDataSource 新创建的过滤后的数据源
     * @category 过滤
     */
    public IDataSource filter(String filter) {
        return tds.filter(filter);
    }

    /**
     * 过滤
     *
     * @param filter (String) 过滤条件
     * @return IDataSource 数据源本身
     * @category 过滤
     */
    public IDataSource filterext(String filter) {
        tds.setFilter(filter);
        tds.filter();
        return this;
    }

    /**
     * 根据输入参数名称读取输入参数值
     *
     * @param paraName 参数名称
     * @return 参数值
     * @category 根据输入参数名称读取输入参数值
     */
    public Object getin(String inParaName) {
        return getInPara(inParaName).getValue();
    }

    /**
     * 根据输入参数序号读取输入参数值
     *
     * @param paraId 输入参数序号
     * @return 字符型参数值
     * @category 根据输入参数序号读取输入参数值
     */
    public Object getin(int inParaId) {
        return getInPara(inParaId).getValue();
    }

    /**
     * 根据输入参数名称返回double型数据
     *
     * @param name 输入参数名称
     * @return double输入参数值
     * @category 根据输入参数名称返回double型数据
     */
    public double getinf(String inParaName) {
        return Double.valueOf(getin(inParaName).toString());
    }

    /**
     * 根据输入参数名称返回String型数据
     *
     * @param name 输入参数名称
     * @return String输入参数值
     * @category 根据输入参数名称返回String型数据
     */
    public String getins(String inParaName) {
        return getin(inParaName).toString();
    }

    /**
     * 根据输入参数序号返回double型数据
     *
     * @param name 输入参数序号
     * @return double输入参数值
     * @category 根据输入参数序号返回double型数据
     */
    public double getinf(int inParaId) {
        return Double.valueOf(getin(inParaId).toString());
    }

    /**
     * 根据输入参数序号返回String型数据
     *
     * @param name 输入参数序号
     * @return String输入参数值
     * @category 根据输入参数序号返回String型数据
     */
    public String getins(int inParaId) {
        return getin(inParaId).toString();
    }

    public Iterator<TOutPara> getOutParaIteror() {
        if (tOutParas == null)
            tOutParas = new ArrayList<TOutPara>();
        return tOutParas.iterator();
    }

    /***************************************************************************
     * 数据操作函数
     **************************************************************************/
    /**
     * 求某列的合计
     *
     * @param colName 列名
     * @return
     * @category 求某列的合计
     */
    public double sum(String colName) {
        List<Object> list = getColValues(colName);
        String ssum = "0";
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                try {
                    ssum = BigArithTools.add(ssum, list.get(i).toString());
                } catch (Exception e) {
                    // log.error("",e);
                }
            }
        }
        return Double.parseDouble(ssum);
    }

    /**
     * 求某列的合计
     *
     * @param colId 列号
     * @return
     * @category 求某列的合计
     */
    public double sum(int colId) {
        List<Object> list = getColValues(colId);
        double d = 0;
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                try {
                    d += Double.parseDouble(list.get(i).toString());
                } catch (Exception e) {
                }
            }
        }
        return d;
    }

    /**
     * 求某列的平均值
     *
     * @param colName 列名
     * @return
     * @category 求某列的平均值
     */
    public double avg(String colName) {
        return avg(colName, true);
    }

    /**
     * 求某列的平均值 <br>
     * 包括空值
     *
     * @param colId 列号
     * @return
     * @category 求某列的平均值
     */
    public double avg(int colId) {
        return avg(colId, true);
    }

    /**
     * 求某列的平均值 <br>
     * 包括空值
     *
     * @param colName 列名
     * @param hasNull 是否包括空值 true包含空值false不包含空值
     * @return
     * @category 求某列的平均值
     */
    public double avg(String colName, boolean hasNull) {
        List<Object> list = getColValues(colName);
        double d = 0;
        int c = 0;
        if (list != null) {
            for (int i = 0; i < list.size(); i++) {
                try {
                    Object v = list.get(i);
                    if (!isChinese(v.toString())) {
                        if (v != null && !"".equals(v)) {
                            d += Double.parseDouble(v.toString());
                        }
                        if (hasNull || (v != null && !"".equals(v)))
                            c++;
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        if (c == 0)
            return 0;
        else
            return d / c;
    }

    /**
     * 求某列的平均值
     *
     * @param colId   列号
     * @param hasNull 是否包括空值
     * @return
     * @category 求某列的平均值
     */
    public double avg(int colId, boolean hasNull) {
        List<Object> list = getColValues(colId);
        double d = 0;
        int c = 0;
        if (list != null) {
            for (int i = 0; i < list.size(); i++) {
                try {
                    Object v = list.get(i);
                    if (!isChinese(v.toString())) {
                        if (v != null && !"".equals(v)) {
                            d += Double.parseDouble(v.toString());
                        }
                        if (hasNull || (v != null && !"".equals(v)))
                            c++;
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        if (c == 0)
            return 0;
        else
            return d / c;
    }

    /**
     * 求最大值
     *
     * @param colId 列号
     * @return
     */
    public double max(int colId) {
        return max(colId, true);
    }

    /**
     * 求最大值
     *
     * @param colName 列名
     * @return
     */
    public double max(String colName) {
        return max(colName, true);
    }

    /**
     * 求最大值
     *
     * @param colName 列名
     * @param hasNull 是否包括空值
     * @return
     */
    public double max(String colName, boolean hasNull) {
        double d = 0;
        List<Object> list = getColValues(colName);
        if (list != null && list.size() > 0) {
            // 赋第一个值
            for (int i = 0; i < list.size(); i++) {
                try {
                    Object v = list.get(i);
                    d = Double.parseDouble(v.toString());
                    break;
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            for (int i = 0; i < list.size(); i++) {
                try {
                    Object v = list.get(i);
                    if (hasNull && (v != null && "".equals(v))) {
                        v = "0";
                    }
                    if (Double.parseDouble(v.toString()) > d) {
                        d = Double.parseDouble(v.toString());
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        return d;
    }

    /**
     * 求最大值
     *
     * @param colId   列号
     * @param hasNull 是否包括空值
     * @return
     */
    public double max(int colId, boolean hasNull) {
        double d = 0;
        List<Object> list = getColValues(colId);
        if (list != null && list.size() > 0) {
            // 赋第一个值
            for (int i = 0; i < list.size(); i++) {
                try {
                    Object v = list.get(i);
                    d = Double.parseDouble(v.toString());
                    break;
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            for (int i = 0; i < list.size(); i++) {
                try {
                    Object v = list.get(i);
                    if (hasNull && (v != null && "".equals(v))) {
                        v = "0";
                    }
                    if (Double.parseDouble(v.toString()) > d) {
                        d = Double.parseDouble(v.toString());
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        return d;
    }

    /**
     * 求最大值
     *
     * @param colId 列号
     * @return
     */
    public double min(int colId) {
        return min(colId, true);
    }

    /**
     * 求最大值
     *
     * @param colName 列名
     * @return
     */
    public double min(String colName) {
        return min(colName, true);
    }

    /**
     * 求最大值
     *
     * @param colName 列名
     * @param hasNull 是否包括空值
     * @return
     */
    public double min(String colName, boolean hasNull) {
        double d = 0;
        List<Object> list = getColValues(colName);
        if (list != null && list.size() > 0) {
            // 赋第一个值
            for (int i = 0; i < list.size(); i++) {
                try {
                    Object v = list.get(i);
                    d = Double.parseDouble(v.toString());
                    break;
                } catch (Exception e) {
                }
            }
            for (int i = 0; i < list.size(); i++) {
                try {
                    Object v = list.get(i);
                    if (hasNull && (v != null && "".equals(v))) {
                        v = "0";
                    }
                    if (Double.parseDouble(v.toString()) < d) {
                        d = Double.parseDouble(v.toString());
                    }
                } catch (Exception e) {
                }
            }
        }
        return d;
    }

    /**
     * 求最大值
     *
     * @param colId   列号
     * @param hasNull 是否包括空值
     * @return
     */
    public double min(int colId, boolean hasNull) {
        double d = 0;
        List<Object> list = getColValues(colId);
        if (list != null && list.size() > 0) {
            // 赋第一个值
            for (int i = 0; i < list.size(); i++) {
                try {
                    Object v = list.get(i);
                    d = Double.parseDouble(v.toString());
                    break;
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            for (int i = 0; i < list.size(); i++) {
                try {
                    Object v = list.get(i);
                    if (hasNull && (v != null && "".equals(v))) {
                        v = "0";
                    }
                    if (Double.parseDouble(v.toString()) < d) {
                        d = Double.parseDouble(v.toString());
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }
        return d;
    }

    /***************************************************************************
     * 行操作函数
     **************************************************************************/
    /**
     * 移到第一行
     *
     * @category 移到第一行
     */
    public boolean moveFirst() {
        return tds.moveFirst();
    }

    /**
     * 上移一行
     *
     * @category 上移一行
     */
    public boolean movePrevious() {
        return tds.movePrevious();
    }

    /**
     * 下移一行
     *
     * @category 下移一行
     */
    public boolean moveNext() {
        return tds.moveNext();
    }

    /**
     * 移到最后一行
     *
     * @category 移到最后一行
     */
    public boolean moveLast() {
        return tds.moveLast();
    }

    /**
     * 移到指定行
     *
     * @param rowId 指定行号
     * @category 移到指定行
     */
    public boolean moveTo(int rowId) {
        return tds.moveTo(rowId);
    }

    public boolean isEof() {
        return tds.isEof();
    }

    public boolean isBof() {
        return tds.isBof();
    }

    /**
     * 读取当前行号
     *
     * @return 当前行号
     * @category 读取当前行号
     */
    public int getCurrRowId() {
        return tds.getCurrRowId();
    }

    /**
     * 读取当前行数据
     *
     * @return TRow对象
     * @category 读取当前行数据
     */
    public TRow get() {
        return tds.get();
    }

    /**
     * 根据行号返回行数据对象
     *
     * @param rowId
     * @return
     * @category 根据行号返回行数据对象
     */

    public TRow get(int rowId) {
        return tds.get(rowId);
    }

    /**
     * 根据条件字符串查找一行数据<br>
     * wzdm="10101000100001" and ybwh="abc"
     *
     * @param find 查找字符串
     * @return
     * @category 根据条件字符串查找一行数据
     */
    public TRow find(String find) {
        return tds.find(find);
    }

    /**
     * 根据条件字符串查找一行数据<br>
     * wzdm="10101000100001" and ybwh="abc"
     *
     * @param find    查找字符串
     * @param colName 列名称
     * @return
     * @category 根据条件字符串查找一行数据
     */
    public Object find(String find, String colName) {
        return tds.find(find).get(colName);
    }

    /**
     * 根据条件字符串查找一行数据<br>
     * wzdm="10101000100001" and ybwh="abc"
     *
     * @param find 查找字符串
     * @return
     * @category 根据条件字符串查找一行数据
     * @para colId 列号
     */
    public Object find(String find, int colId) {
        return tds.find(find).get(colId);
    }

    /**
     * 插入行，0插入到最后
     *
     * @param rowid 行号
     * @return 插入后的行号
     * @category 插入行，0插入到最后
     */
    public int insertRow(int rowId) {
        return tds.insertRow(rowId);
    }

    /**
     * 删除行
     *
     * @param rowId 删除的行号
     * @return 成功或失败
     * @category 删除行
     */
    public boolean deleteRow(int rowId) {
        return tds.deleteRow(rowId);
    }

    /**
     * 读取数据行数
     *
     * @return 返回行数
     * @category 读取数据行数
     */
    public int getRowCount() {
        if (this.isLoadDataList) {
            return this.getDataList().size();// this.dataList.size();
        } else {
            return tds.getRowCount();
        }
    }

    /**
     * 读取数据列数
     *
     * @return 返回列数
     * @category 读取数据列数
     */
    public int getColCount() {
        return tOutParas.size();
    }

    public List<Object> getColValues(int colId) {
        List<Object> list = null;
        if (colId >= 0 && colId < tOutParas.size()) {
            if (getRowCount() > 0) {
                if (list == null) {
                    list = new ArrayList<Object>();
                }
                for (int i = 0; i < tds.getRowCount(); i++) {
                    list.add(tds.getRow(i).get(colId));
                }
            }
        }
        return list;
    }

    public List<Object> getColValues(String colName) {
        int colId = -1;
        for (TOutPara op : tOutParas) {
            if (op.getName().equalsIgnoreCase(colName)) {
                colId = op.getID();
                break;
            }
        }
        if (colId == -1) {
            return this.getColValuesByAlias(colName);
        }
        return getColValues(colId);
    }

    /**
     * 返回列数据
     *
     * @param colName 输出参数别名
     * @return 数据列表
     * @category 返回列数据
     */
    public List<Object> getColValuesByAlias(String colAlias) {
        int colId = -1;
        for (TOutPara op : tOutParas) {
            if (op.getAlias().equals(colAlias)) {
                colId = op.getID();
                break;
            }
        }
        return getColValues(colId);
    }

    /**
     * 设置数据
     *
     * @param rowId 行号
     * @param colId 列号
     * @param value 数据
     * @category 设置数据
     */
    public void set(int rowId, int colId, Object value) {
        if (rowId >= 0 && getRowCount() > rowId)
            tds.get(rowId).set(colId, value);
    }

    public Object getByCellID(String cellId) {
        return tds.getByCellID(cellId);
    }

    /**
     * 直接读取数据
     *
     * @param rowId 行号
     * @param colId 列号
     * @return 返回值
     * @category 直接读取数据
     */
    public Object get(int rowId, int colId) {
        return tds.get(rowId, colId);
    }

    /**
     * 直接读取数据
     *
     * @param rowId   行号
     * @param colName 列名
     * @return 返回值
     * @category 直接读取数据
     */
    public Object get(int rowId, String colName) {
        return tds.get(rowId, getOutParaId(colName));
    }

    /**
     * 直接读取数据
     *
     * @param rowId 行号
     * @param colId 列号
     * @return 返回值
     * @category 直接读取数据
     */
    public String gets(int rowId, int colId) {
        String s = "";
        try {
            Object o = this.get(rowId, colId);
            if (o != null) {
                s = o.toString();
            }
        } catch (Exception e) {
            s = "";
            log.error("", e);
        }
        return s;
    }

    /**
     * 直接读取数据
     *
     * @param rowId   行号
     * @param colName 列名
     * @return 返回值
     * @category 直接读取数据
     */
    public String gets(int rowId, String colName) {
        String s = "";
        try {
            Object o = this.get(rowId, colName);
            if (o != null) {
                s = o.toString();
            }
        } catch (Exception e) {
            s = "";
            log.error("", e);
        }
        return s;
    }

    /**
     * 直接读取数据
     *
     * @param rowId 行号
     * @param colId 列号
     * @return 返回值
     * @category 直接读取数据
     */
    public double getf(int rowId, int colId) {
        double d = 0.00;
        try {
            Object o = this.get(rowId, colId);
            if (o != null) {
                d = Double.parseDouble(o.toString());
            }
        } catch (Exception e) {
            d = 0.00;
        }
        return d;
    }

    /**
     * 直接读取数据
     *
     * @param rowId   行号
     * @param colName 列名
     * @return 返回值
     * @category 直接读取数据
     */
    public double getf(int rowId, String colName) {
        double d = 0.00;
        try {
            Object o = this.get(rowId, colName);
            if (o != null) {
                d = Double.parseDouble(o.toString());
            }
        } catch (Exception e) {
            d = 0.00;
            log.error("", e);
        }
        return d;
    }

    public Object execScript() {
        return execScript(script);
    }

    public Object execScript(String script) {
        Object o = null;
//		if (script != null && !"".equals(script)) {
//			// 设置默认数据源变量
//			if (jsEngine == null) {
//				jsEngine = new TJSEngine();
//			}
//			// jsEngine.put("ds", this);
//			// 系统内数据源的变量设置
//			for (String key : mapCustomObj.keySet()) {
//				Object obj = mapCustomObj.get(key);
//				jsEngine.put(key, obj);
//			}
//			try {
//				o = jsEngine.eval(script);
//			} catch (ScriptException e) {
//				log.error(script, e);
//				errorInfo += "<br>执行数据源脚本出错,错误原因<br>" + e.getMessage();
//			}
//		}
        return o;
    }

    /**
     * 从jsondata数据恢复到数据源，但不包含数据源名称及其它的内容 <br>
     * 字段类型一律以字符串形式存储
     *
     * @param jsonData 不包含数据源名，输入参数等，仅数据，并且是json数组
     * @return
     */
    public boolean fromJsonData(String jsondata) {
        boolean b = false;
        boolean f = false;
        try {
            JSONArray json = JSON.parseArray(jsondata);
            @SuppressWarnings("unchecked")
            Iterator<Object> it = json.iterator();
            while (it.hasNext()) {
                JSONObject o = (JSONObject) it.next();
                int i = 0;
                // 第一次进入，生成数据源输出参数
                if (!f) {
                    for (String key : o.keySet()) {
                        TOutPara op = new TOutPara(this);
                        op.setAlias(key);
                        op.setName(key);
                        op.setWidth(100);
                        op.setID(i);
                        op.setDataType(DataType.tdsString);
                        addOutPara(op);
                        i++;
                    }
                    f = true;
                }
                int row = tds.addRow();
                i = 0;
                for (String key : o.keySet()) {
                    tds.set(row, i, o.get(key));// 插入数据
                    i++;
                }
            }
        } catch (Exception e) {
            b = false;
            log.error("", e);
        }
        return b;
    }

    /**
     * 获取JsonArray 格式数据
     */
    public JSONArray getJsonArrayData() {
        JSONArray jsonAry = new JSONArray();
        for (int i = 0; i < tds.getRowCount(); i++) {
            TRow row = tds.get(i);
            if (tds.getFilter() != null && tds.getFilter().length() > 0) {
                if (!row.isFiltered()) {
                    continue;
                }
            }
            JSONObject jobj = new JSONObject();
            for (int j = 0; j < tOutParas.size(); j++) {
                TOutPara op = tOutParas.get(j);
                Object value = row.get(op.getID());
                if (this.isShowRenderValue() && StringUtils.isNotEmpty(op.getRendererFun())) {// 解析渲染函数
                    try {
                        value = this.getRenderValue(value, row, op, i);
                    } catch (Exception e) {
                        log.error("数据源渲染函数解析错误(" + tds.getDs().getDSAlias() + "," + op.alias() + ")", e);
                    }
                }
                if (value == null || value.equals("null")) {
                    value = "";
                } else if (Maths.isNumType(value)) {// 数字，格式化防止科学计数法
                    String val = Maths.formatNum(value);
                    if (val == null || val.length() == 0 || "null".equals(val)) {
                        val = "0";
                    }
                    value = val;
                } else if (value instanceof JSONArray) {
                    value = JSONArray.toJSONString(value).replaceAll("\r\n", "\\n");
                }
                jobj.put(op.getAlias(), value);
            }
            jsonAry.add(jobj);
        }
        return jsonAry;
    }

    /**
     * 获取渲染函数值
     *
     * @param value    单元格值
     * @param row      行数据
     * @param outPara  列信息
     * @param rowIndex 行号
     * @return
     * @throws ScriptException
     */
    private Object getRenderValue(Object value, TRow row, TOutPara outPara, int rowIndex) throws Exception {
        return this.getRenderValue(outPara.getRendererFun(), value, this.getRowObj(row), outPara.getAlias(), rowIndex,
                outPara.getID());
    }


    /**
     * TRow 转换为 json对象
     *
     * @param row
     * @return
     */
    private JSONObject getRowObj(TRow row) {
        JSONObject obj = new JSONObject();
        for (int n = 0; n < tOutParas.size(); n++) {
            TOutPara op = tOutParas.get(n);
            Object value = row.get(op.getID());
            obj.put(op.getAlias(), value == null ? "" : value);
        }
        return obj;
    }

    /**
     * 返回数据源数据，数据json格式
     *
     * @return JSON格式
     * @category 返回数据源数据，数据json格式
     */
    public String getJsonData() {
        JSONArray jsonAry = this.getJsonArrayData();
        if (jsonAry == null) {
            return "[]";
        } else {
            return jsonAry.toJSONString();
        }
    }

    /**
     * 返回数据源数据
     *
     * @return JSON格式
     * @category 返回数据源数据
     */
    public String getJson() {
        return getJson(false);
    }

    /**
     * 返回数据源的Json格式数据(不含数据)
     *
     * @return
     * @category 返回数据源的Json格式数据
     */
    public JSONArray getJsonWithoutData() {
        try {
            String json = this.getJson(false, false);
            return JSONArray.parseArray("[" + json + "]");
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    public String getJson(boolean hasErrInfo) {
        return this.getJson(hasErrInfo, true);
    }

    /**
     * 返回数据源数据
     *
     * @return JSON格式
     * @category 返回数据源数据
     */
    private String getJson(boolean hasErrInfo, boolean hasData) {
        StringBuffer qd = new StringBuffer();// 查询条件的数据
        StringBuffer qp = new StringBuffer();// 查询条件的属性
        StringBuffer dp = new StringBuffer(); // 列的属性
        StringBuffer cols = new StringBuffer(); // 列名
        StringBuffer mcfg = new StringBuffer(); // 移动端配置
        int li = tInParas.size();
        for (int i = 0; i < li; i++) {
            TInPara ip = tInParas.get(i);
            Object value = ip.getValue();
            if (value == null || "null".equals(value))
                value = "";
            if (value instanceof String)
                value = correctJsonString(value.toString());
            qd.append("\"" + ip.getParaAlias() + "\":\"" + value + "\"");
            qp.append("\"" + ip.getParaAlias() + "\":\"" + ip.getParaName() + "\"");
            if (i < li - 1) {
                qd.append(",");
                qp.append(",");
            }
        }
        List<String> listItem = new ArrayList<String>();
        TdstableInfo tInfo = this.dsm.getTDSTableInfo(getDSAlias());
        if (tInfo != null) {
            if (tInfo.getListItem() != null && tInfo.getListItem().length() > 0) {
                String[] mobileItem = tInfo.getListItem().split(",");
                for (String s : mobileItem) {
                    listItem.add(s);
                }
            }
        }
        LinkedHashMap<String, TOutPara> outParasMap = new LinkedHashMap<String, TOutPara>();
        for (int i = 0; i < tOutParas.size(); i++) {
            TOutPara op = tOutParas.get(i);
            outParasMap.put(op.getAlias(), op);
        }
        // 列的属性
        for (int i = 0; i < tOutParas.size(); i++) {
            TOutPara op = tOutParas.get(i);
            if (listItem.size() == 0) {// 未设置
                if (op.getVisible()) {
                    mcfg.append("\"" + op.getAlias() + "\":\"" + op.getName() + "\",");
                }
            } else {
                if (listItem.contains(op.getAlias())) {
                    mcfg.append("\"" + op.getAlias() + "\":\"" + op.getName() + "\",");
                }
            }
            cols.append("\"" + op.getAlias() + "\"");
            dp.append("\"" + op.getAlias() + "\":");
            // 输出参数
            TdsTableColumn column = this.tdsOut2Column(op, outParasMap);
            dp.append(JSON.toJSONString(column));
            if (i < tOutParas.size() - 1) {
                dp.append(",");
                cols.append(",");
            }
        }
        // 手机端配置
        String mobile = "";
        if (mcfg.length() > 0) {
            String s1 = mcfg.toString();
            s1 = s1.substring(0, s1.length() - 1);
            String g1 = "";
            String g2 = "";

            if (tInfo != null) {
                g1 = tInfo.getGroupProject1() == null ? "" : tInfo.getGroupProject1();
                g2 = tInfo.getGroupProject2() == null ? "" : tInfo.getGroupProject2();
            } else {
                tInfo = new TdstableInfo();
            }
            mobile = "{\"items\":{" + s1 + "},";
            mobile += "\"datas\":" + JSONObject.toJSONString(tInfo) + ",";
            mobile += "\"group1\":\"" + g1 + "\",";
            mobile += "\"group2\":\"" + g2 + "\" ";
            mobile += "}";
        }
        // log.info(mobile);
        // 分页信息
        StringBuffer pageInfo = new StringBuffer();
        pageInfo.append("\"page\":" + tds.getPage() + ",");
        pageInfo.append("\"pageSize\":" + tds.getPageSize() + ",");
        pageInfo.append("\"pageCount\":" + tds.getPageCount() + ",");
        pageInfo.append("\"recordCount\":" + tds.getRecordCount());
        StringBuffer sb = new StringBuffer();
        sb.append("{");
        sb.append("\"name\":\"" + getDSAlias() + "\",");
        sb.append("\"desc\":\"" + getDSName() + "\",");
        sb.append("\"class\":\"" + getDSClassName() + "\",");
        if (getExcelPath() != null) {
            sb.append("\"excelPath\":\"" + getExcelPath() + "\",");
        }
        sb.append("\"version\":\"1\",");
        sb.append("\"props\":{");
        // 表头合并
        List<TdsTableColumn> listTableColumn = this.getSpanColumn(outParasMap, tInfo);
        if (listTableColumn != null) {
            sb.append("\"colspan\":");
            sb.append(JSON.toJSONString(listTableColumn));
            sb.append(",");
        }
        sb.append("\"queryprop\":{" + qp.toString() + "},");
        sb.append("\"cols\":[" + cols.toString() + "],");
        // sb.append("\"cols\":{" + cols.toString() + "},");
        sb.append("\"colprop\":{" + dp.toString() + "}");
        sb.append("},");
        sb.append("\"query\":{" + qd.toString() + "},");
        sb.append("\"pageInfo\":{" + pageInfo.toString() + "},");
        if (mobile.length() > 0) {
            sb.append("\"mobileCfg\":" + mobile + ",");
        }
        if (hasData) {
            sb.append("\"data\":" + this.getJsonData() + ",");
        }
        if (hasErrInfo) {
            String err = correctJsonString(getErrorInfo());
            sb.append("\"errorinfo\":\"" + err + "\" ,");
        }
        String script = this.getTdsCalScript();// 页面计算脚本
        if (StringUtils.isNotEmpty(script)) {
            script = this.rendererString(script);
            sb.append("\"calScript\":\"" + script + "\",");
        }
        String afterEditscript = this.getTdsAfterEditScript();// 编辑后脚本
        if (StringUtils.isNotEmpty(afterEditscript)) {
            script = this.rendererString(afterEditscript);
            sb.append("\"tdsAfterEditScript\":\"" + afterEditscript + "\",");
        }
        script = this.getTdsCanEditScript();// 页面控件是否可以编辑脚本
        if (StringUtils.isNotEmpty(script)) {
            script = this.rendererString(script);
            sb.append("\"canEditScript\":\"" + script + "\",");
        }
        // sb.append(",\"auditStatus\":" + this.tdsBpmAudit + "");

        if (this.cTdsName == null) {
            this.cTdsName = "";
        }
        sb.append("\"cTdsName\":\"" + this.cTdsName + "\",");
        if (this.cTdsAlias == null) {
            this.cTdsAlias = "";
        }
        sb.append("\"cTdsAlias\":\"" + this.cTdsAlias + "\"");
        sb.append("}");
        // log.info(sb.toString());
        return sb.toString();
    }

    /**
     * 获取表头合并信息
     *
     * @param listOp
     * @param tInfo
     * @return
     */
    private List<TdsTableColumn> getSpanColumn(LinkedHashMap<String, TOutPara> opMap, TdstableInfo tInfo) {
        List<TdsTableColumn> list = null;
        boolean spanTitle = false;
        if (tInfo.getIsShowSpanTitle() != null && tInfo.getIsShowSpanTitle() == 1) {
            spanTitle = true;
        }
        if (spanTitle && opMap != null && opMap.size() > 0) {
            list = this.getDsm().getSpanColumn(opMap);
        }
        return list;

    }

    /**
     * 输出参数转换为数据源表格表头对象
     *
     * @param op
     * @return
     */
    private TdsTableColumn tdsOut2Column(TOutPara op, Map<String, TOutPara> outMap) {
        Map<String, String> mapInParasValues = null;
        if (StringUtils.isNotEmpty(tInParas)) {
            mapInParasValues = new HashMap<String, String>();
            for (TInPara inPara : tInParas) {
                if (inPara.getValue() != null) {
                    mapInParasValues.put(inPara.getParaAlias(), inPara.getValue().toString());
                }
            }
        }
        return this.getDsm().tdsOut2Column(op, mapInParasValues, outMap);
    }

    /**
     * 关闭session
     *
     * @category 关闭数据库连接Session
     */

    public void disconnection() {
        double times = (new Date().getTime() - openSessTime.getTime()) / 1000.00;
        SpringUtils.getBean(EntityService.class).closeConnection(this.conn);
        log.trace(this.hashCode() + " = 数据源[" + this.getDSName() + "(" + this.getDSAlias() + ")关闭session连接...用时: "
                + times + " 秒");
    }

    public boolean connection() {
        return connection(null);
    }

    public boolean connection(String connstr) {
        log.trace(this.hashCode() + " = 数据源[" + this.getDSName() + "(" + this.getDSAlias() + ")打开session连接...");
        this.conn = SpringUtils.getBean(EntityService.class).getConnection();
        openSessTime = new Date();
        return true;
    }

    /**
     * 返回数据源执行过程中产生的错误信息
     *
     * @category 返回数据源执行过程中产生的错误信息
     */
    public String getErrorInfo() {
        return errorInfo;
    }

    /**
     * 设置数据源执行过程中产生的错误
     *
     * @param err
     * @category 设置数据源执行过程中产生的错误
     */
    public void setErrorInfo(String err) {
        errorInfo += err;
    }

    /**
     * 清理数据源执行过程中的错误信息
     *
     * @category 清理数据源执行过程中的错误信息
     */
    public void clearErrorInfo() {
        errorInfo = "";
    }

    private String correctJsonString(String str) {
        return this.getDsm().correctJsonString(str);
    }

    private String correctXMLString(String str) {
        str = str.replaceAll("&", "&amp;");
        str = str.replaceAll("\r\n", "&lt;br&gt;");
        str = str.replaceAll("<", "&lt;");
        str = str.replaceAll(">", "&gt;");
        str = str.replaceAll("\"", "&quot;");
        str = str.replaceAll("'", "&apos;");
        return str;
    }

    /**
     * 渲染函数字符替换
     *
     * @param rendererFun
     * @return
     */
    private String rendererString(String rendererFun) {
        return this.getDsm().rendererString(rendererFun);
    }

    public String getCreateUid() {
        return createUid;
    }

    public void setCreateUid(String createUid) {
        this.createUid = createUid;
    }

    public String getUpdateUid() {
        return updateUid;
    }

    public void setUpdateUid(String updateUid) {
        this.updateUid = updateUid;
    }

    public String getCreateUname() {
        return createUname;
    }

    public void setCreateUname(String createUname) {
        this.createUname = createUname;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateUname() {
        return updateUname;
    }

    public void setUpdateUname(String updateUname) {
        this.updateUname = updateUname;
    }

    public void setDataSourceId(String dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public String getDataSourceId() {
        return dataSourceId;
    }

    public boolean isShowUnCheckExcel() {
        return showUnCheckExcel;
    }

    public String getExcelPath() {
        return excelPath;
    }

    public Integer getInitOutParaSize() {
        return initOutParaSize;
    }

    public void setInitOutParaSize(Integer size) {
        initOutParaSize = size;
    }

    public boolean isAdvancedSearch() {
        return advancedSearch;
    }

    public void setAdvancedSearch(Boolean advancedSearch) {
        this.advancedSearch = advancedSearch;
    }

    public Boolean getFindByKey() {
        return findByKey;
    }

    public void setFindByKey(Boolean findByKey) {
        this.findByKey = findByKey;
    }

    /**
     * 根据输出参数名称或者别名获得参数ID
     *
     * @param name 输出参数名称/别名
     * @return
     */
    @Override
    public int getOutParaId(String name) {
        int id = -1;
        TOutPara op = this.getOutParaByName(name);
        if (op == null) {
            op = this.getOutParaByAlias(name);
        }
        if (op != null) {
            id = op.getID();
        }
        return id;
    }

    /**
     * 求记录数
     *
     * @category 求记录数
     */
    @Override
    public int count() {
        return getRowCount();
    }

    /**
     * 设置数据源的内部数据对象集合(注:仅内部使用外部不允许使用)
     *
     * @param tds
     * @category 设置数据源的内部数据对象集合(注 : 仅内部使用外部不允许使用)
     * @see 由于需要构建一个新数据源，只能采用这种方法 <br>
     * modify by G.fj 2014-06-10
     */
    @Override
    public void setTDataStore(TDataStore tds) {
        this.tds = tds;
    }

    /**
     * 数据源结果转换为数组
     *
     * @return 数组字符串 [{"装置代码":"0050060601"},{},{}]
     */

    public String getArray() {
        return this.getArray("");
    }

    /**
     * 数据源结果转换为数组
     *
     * @param outParas $dxlsjy.filter("装置代码='4030010201'").getArray("装置代码1,班组代码,班组名称"
     *                 )
     * @return 数组字符串 [{"装置代码1":"","班组代码":"1","班组名称":"公司领导"},{"装置代码1":"","班组代码":"2"
     * ,"班组名称":"副总工程师"}]
     */
    public String getArray(String outParas) {
        String json = "";
        try {
            if (this.tds != null && this.tds.getRowCount() <= 0) {
                json = "[]";
            } else {
                String[] outs = null;
                if (outParas == null || outParas.length() <= 0) { // 输出所有列
                    if (this.tOutParas != null && this.tOutParas.size() > 0) {
                        outs = new String[this.tOutParas.size()];
                        for (int i = 0; i < this.tOutParas.size(); i++) {
                            outs[i] = this.tOutParas.get(i).getName();
                        }
                    }
                } else {// 输出指定列
                    outs = outParas.split(",");
                }
                if (outs != null && outs.length > 0) {
                    StringBuffer sb = new StringBuffer();
                    for (int i = 0; i < this.tds.getRowCount(); i++) {
                        TRow tr = this.tds.get(i);
                        StringBuffer sb1 = new StringBuffer();
                        for (int j = 0; j < outs.length; j++) {
                            String v = replace(tr.gets(outs[j]));
                            sb1.append("\"" + replace(outs[j]) + "\":\"" + v + "\",");
                        }
                        String s = sb1.toString();
                        if (s.length() > 0) {
                            s = "{" + s.substring(0, s.length() - 1) + "}";
                            sb.append(s);
                            sb.append(",");
                        }
                    }
                    String s = sb.toString();
                    if (s.length() > 0) {
                        json = "[" + s.substring(0, s.length() - 1) + "]";
                    } else {
                        json = "[]";
                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
            json = "[]";
        }
        return json;

    }

    /**
     * 数据源结果转换为数组
     *
     * @param outParas    outParas
     *                    $dxlsjy.filter("装置代码='4030010201'").getArray("装置代码1,班组代码,班组名称"
     *                    )
     * @param enableEmpty 允许空值。字符串true或false true时保留空值，false时将空值过滤不显示
     * @return 数组字符串 [{"装置代码1":"","班组代码":"1","班组名称":"公司领导"},{"装置代码1":"","班组代码":"2"
     * ,"班组名称":"副总工程师"}]
     */
    public String getArray(String outParas, Integer enableEmpty) {
        String json = "";
        try {
            if (this.tds != null && this.tds.getRowCount() <= 0) {
                json = "[]";
            } else {
                String[] outs = null;
                if (outParas == null || outParas.length() <= 0) { // 输出所有列
                    if (this.tOutParas != null && this.tOutParas.size() > 0) {
                        outs = new String[this.tOutParas.size()];
                        for (int i = 0; i < this.tOutParas.size(); i++) {
                            outs[i] = this.tOutParas.get(i).getName();
                        }
                    }
                } else {// 输出指定列
                    outs = outParas.split(",");
                }
                if (outs != null && outs.length > 0) {
                    StringBuffer sb = new StringBuffer();
                    for (int i = 0; i < this.tds.getRowCount(); i++) {
                        TRow tr = this.tds.get(i);
                        StringBuffer sb1 = new StringBuffer();
                        for (int j = 0; j < outs.length; j++) {
                            String v = replace(tr.gets(outs[j]));
                            if (new Integer(0).equals(enableEmpty)) {
                                if (v != null && v.length() > 0) {
                                    sb1.append("\"" + replace(outs[j]) + "\":\"" + v + "\",");
                                }
                            } else {
                                sb1.append("\"" + replace(outs[j]) + "\":\"" + v + "\",");
                            }
                        }
                        String s = sb1.toString();
                        if (s.length() > 0) {
                            s = "{" + s.substring(0, s.length() - 1) + "}";
                            sb.append(s);
                            sb.append(",");
                        }
                    }
                    String s = sb.toString();
                    if (s.length() > 0) {
                        json = "[" + s.substring(0, s.length() - 1) + "]";
                    } else {
                        json = "[]";
                    }
                }
            }
        } catch (Exception e) {
            log.error("", e);
            json = "[]";
        }
        return json;

    }

    /**
     * 获取记录集中某一个字段的全部内容，以文本方式输出，例： 表 a b c 1 2 3 4 2 6 7 8 9
     * $xx.filter("b='2'").getTextContent(0,",") 会输出 1,4
     *
     * @param colId     int 列id
     * @param splitSign 分隔符
     * @return
     * @category 获取记录集中某一个字段的全部内容
     */
    public String getTextContent(int colId, String splitSign) {
        List<Object> list = getColValues(colId);
        String result = "";
        if (list != null && list.size() > 0) {
            StringBuffer rBuf = new StringBuffer();
            for (Object temp : list) {
                if (temp != null) {
                    rBuf.append(splitSign + temp.toString());
                }
            }
            if (rBuf.length() > 0) {
                result = rBuf.substring(splitSign.length());
            }
        }
        return result;
    }

    ;

    /**
     * 获取记录集中某一个字段的全部内容，以文本方式输出，例： 表 a b c 1 2 3 4 2 6 7 8 9
     * $xx.filter("b='2'").getTextContent("a",",") 会输出 1,4
     *
     * @param colName   String 列名
     * @param splitSign 分隔符
     * @return
     * @category 获取记录集中某一个字段的全部内容
     */
    public String getTextContent(String colName, String splitSign) {
        List<Object> list = getColValues(colName);
        String result = "";
        if (list != null && list.size() > 0) {
            StringBuffer rBuf = new StringBuffer();
            for (Object temp : list) {
                if (temp != null) {
                    rBuf.append(splitSign + temp.toString());
                }
            }
            if (rBuf.length() > 0) {
                result = rBuf.substring(splitSign.length());
            }
        }
        return result;
    }

    ;

    /**
     * 获取记录集中某一个字段的全部内容，以文本方式输出，默认分隔符为逗号，例： 表 a b c 1 2 3 4 2 6 7 8 9
     * $xx.filter("b='2'").getTextContent(0) 会输出 1,4
     *
     * @param colName String 列名 分隔符默认为逗号
     * @return
     * @category 获取记录集中某一个字段的全部内容
     */
    public String getTextContent(int colId) {
        return getTextContent(colId, ",");
    }

    ;

    /**
     * 获取记录集中某一个字段的全部内容，以文本方式输出，默认分隔符为逗号，例： 表 a b c 1 2 3 4 2 6 7 8 9
     * $xx.filter("b='2'").getTextContent("a") 会输出 1,4
     *
     * @param colId int 列id 分隔符默认为逗号
     * @return
     * @category 获取记录集中某一个字段的全部内容
     */
    public String getTextContent(String colName) {
        return getTextContent(colName, ",");
    }

    ;

    /**
     * /** 替换数据保证生成json无误
     *
     * @param v
     * @return
     */
    private String replace(String v) {
        String s = v;
        try {
            if (s != null && s.length() > 0) {// 替换 双引号
                s = v.replaceAll("\r", "");
                s = v.replaceAll("\n", "");
                if (v.indexOf("\"") >= 0) {
                    s = v.replaceAll("\"", "\\\\\"");
                }
            } else {
                s = "";
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return s;
    }

    @Override
    public String getTableHtml(String cssJson) {
        StringBuffer result = new StringBuffer();
        String divStyle = "align=\"center\"";
        String tableStyle = "style=\"border-collapse:collapse;\" bordercolor=\"#000000\" cellspacing=\"0\" cellpadding=\"0\" border=\"1\"";
        String thStyle = "bgcolor=\"#E0E0E0\" align=\"left\"";
        String tdStyle = "align=\"left\"";

        if (cssJson != null && cssJson.length() != 0) {
            try {
                cssJson = cssJson.replace("==", "=");// 替换等号
                JSONObject cssObj = JSONObject.parseObject(cssJson);
                if (cssObj.containsKey("div")) {// 获取div配置
                    String style = cssObj.getString("div");
                    if (style != null && style.length() != 0) {
                        divStyle = style;
                    }
                }
                if (cssObj.containsKey("table")) {// 获取table配置
                    String style = cssObj.getString("table");
                    if (style != null && style.length() != 0) {
                        tableStyle = style;
                    }
                }
                if (cssObj.containsKey("th")) {// 获取th配置
                    String style = cssObj.getString("th");
                    if (style != null && style.length() != 0) {
                        thStyle = style;
                    }
                }
                if (cssObj.containsKey("td")) {//// 获取td配置
                    String style = cssObj.getString("td");
                    if (style != null && style.length() != 0) {
                        tdStyle = style;
                    }
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
        result.append("<div " + divStyle + ">");// 外部的div框
        if (tOutParas != null && tOutParas.size() > 0) {
            result.append("<table " + tableStyle + ">");// table
            result.append("<tr>");// 表头
            List<TOutPara> aliasList = new ArrayList<TOutPara>();
            for (int i = 0, j = tOutParas.size(); i < j; i++) {
                TOutPara para = tOutParas.get(i);
                if (para.getVisible()) {// 显示该字段
                    aliasList.add(para);
                    result.append("<th " + thStyle + ">" + correctXMLString(para.getName()) + "</th>");// 表头
                }
            }
            result.append("</tr>");

            if (aliasList.size() > 0 && this.getRowCount() > 0) {
                for (int i = 0, j = this.getRowCount(); i < j; i++) {
                    TRow dataTemp = this.get(i);
                    result.append("<tr>");// 内容
                    for (TOutPara para : aliasList) {
                        String dataValue = "";
                        if (para.getDataType().equals(DataType.tdsDouble)
                                || para.getDataType().equals(DataType.tdsInteger)
                                || para.getDataType().equals(DataType.tdsLong)) {
                            Double val = dataTemp.getDouble(para.alias());
                            if (val != null) {
                                dataValue = Maths.formatNum(val);
                            } else {
                                dataValue = dataTemp.getString(para.alias());
                            }
                        } else {
                            dataValue = dataTemp.getString(para.alias());
                        }
                        result.append("<td " + tdStyle + ">" + correctXMLString(dataValue) + "</td>");// 列内容
                    }
                    result.append("</tr>");
                }
            }
            result.append("</table>");
        }
        result.append("</div>");
        return result.toString();
    }

    @Override
    public String getTableHtml() {
        return getTableHtml(null);
    }

    /**
     * 检测出中文汉字不能检测中文标点；
     *
     * @param str
     * @return
     * @throws UnsupportedEncodingException
     */
    public boolean isChinese(String str) throws UnsupportedEncodingException {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        return m.find();
    }

    @Override
    public String getTdsInitSql() {
        return tdsInitSql;
    }

    @Override
    public void setTdsInitSql(String tdsInitSql) {
        this.tdsInitSql = tdsInitSql;
    }

    @Override
    public String getTdsCalScript() {
        return tdsCalScript;
    }

    @Override
    public void setTdsCalScript(String tdsCalScript) {
        this.tdsCalScript = tdsCalScript;
    }

    @Override
    public String getTdsAfterEditScript() {
        return tdsAfterEditScript;
    }

    @Override
    public void setTdsAfterEditScript(String tdsAfterEditScript) {
        this.tdsAfterEditScript = tdsAfterEditScript;
    }

    @Override
    public String getTdsCanEditScript() {
        return tdsCanEditScript;
    }

    @Override
    public void setTdsCanEditScript(String tdsCanEditScript) {
        this.tdsCanEditScript = tdsCanEditScript;
    }

    @Override
    public boolean isLoadInitData() {
        return isLoadInitData;
    }

    @Override
    public void setLoadInitData(boolean isLoadInitData) {
        this.isLoadInitData = isLoadInitData;
    }

    @Override
    public void setCTdsName(String cTdsName) {
        this.cTdsName = cTdsName;
    }

    @Override
    public String getCTdsName() {
        return cTdsName;
    }

    @Override
    public void setCTdsAlias(String cTdsAlias) {
        this.cTdsAlias = cTdsAlias;
    }

    @Override
    public String getCTdsAlias() {
        return cTdsAlias;
    }

    @Override
    public LinkedHashMap<String, TOutPara> getRsCols() {
        return rsCols;
    }

    /**
     * 获取数据是否为渲染函数的结果值
     *
     * @return
     */

    public boolean isShowRenderValue() {
        return showRenderValue;
    }

    /**
     * 设置是否返回数据为渲染函数值
     */
    @Override
    public void setShowRenderValue(boolean showRenderValue) {
        this.showRenderValue = showRenderValue;
    }

    @Override
    public Integer getTdsInitType() {
        return tdsInitType;
    }

    @Override
    public void setTdsInitType(Integer tdsInitType) {
        this.tdsInitType = tdsInitType;
    }

    @Override
    public String getBindTdsAlias() {
        return bindTdsAlias;
    }

    @Override
    public void setBindTdsAlias(String bindTdsAlias) {
        this.bindTdsAlias = bindTdsAlias;
    }

    /**
     * 数据源列间公式计算
     */
    @Override
    public Object reportFormulaCal(String datatype, TRow tr, String formula) {
        if (tr != null && StringUtils.isNotEmpty(formula)) {
            try {
                Map<String, Object> valueMap = new HashMap<String, Object>();
                List<String> paramList = AviatorUtils.getParams(formula);
                if (paramList != null) {
                    for (String code : paramList) {
                        Object val = tr.get(code);
                        if (val == null) {
                            val = 0.0;
                        } else {
                            val = TdsTools.castColumnNumber(datatype, val);
                        }
                        valueMap.put(code, val);
                    }
                }
                // Object result = AviatorUtils.exec(formula, valueMap);
                return this.reportFormulaCal(valueMap, formula); // 计算结果
            } catch (Exception e) {
                log.error(this.dsAlias + "列间公式计算错误[" + formula + "]", e);
            }
        }
        return null;
    }

    /**
     * 列间公式计算
     */
    @Override
    public Object reportFormulaCal(TRow tr, String formula) {
        return this.reportFormulaCal(TdsTools.PARAMTYPE_STRING, tr, formula);
    }

    /**
     * 列间公式计算
     */
    @Override
    public Object reportFormulaCal(Map<String, Object> rowMap, String formula) {
        if (rowMap != null && StringUtils.isNotEmpty(formula)) {
            try {
                return TdsTools.colFormulaCal(rowMap, formula);
            } catch (Exception e) {
                log.error(this.dsAlias + "列间公式计算错误[" + formula + "]", e);
            }
        }
        return null;
    }

    @Override
    public void addData(Map<String, Object> data) {
        this.dataList.add(data);
    }

    @Override
    public void clearData() {
        this.dataList.clear();
    }

    /**
     * 返回数据源数据（List<Map<String, Object>>格式）
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getDataList() {
        if (this.isLoadDataList()) {
            return this.dataList;
        } else {
            List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
            JSONArray data = this.getJsonArrayData();
            if (data != null && data.size() > 0) {
                for (int i = 0; i < data.size(); i++) {
                    JSONObject obj = data.getJSONObject(i);
                    if (obj != null) {
                        list.add(obj.getInnerMap());
                    }
                }
            }
            return list;
        }
    }

    /**
     * 是否只返回List<Map<String,Object>> 格式数据
     */
    @Override
    public void setLoadDataList(boolean bln) {
        this.isLoadDataList = bln;
    }

    /**
     * 是否只返回List<Map<String,Object>> 格式数据
     */
    @Override
    public boolean isLoadDataList() {
        return this.isLoadDataList;
    }

    /**
     * 渲染函数解析
     *
     * @param script
     * @param value
     * @param row
     * @param colAlias
     * @param rowIndex
     * @param colIndex
     * @return
     * @throws ScriptException
     */
    @Override
    public Object getRenderValue(String script, Object value, JSONObject row, String colAlias, int rowIndex,
                                 int colIndex) throws ScriptException {
        return this.getDsm().getRenderValue(script, value, row, colAlias, rowIndex, colIndex);
    }

    private TDataSourceManager getDsm() {
        if (this.dsm == null) {
            this.dsm = new TDataSourceManager();
        }
        return this.dsm;
    }

}
