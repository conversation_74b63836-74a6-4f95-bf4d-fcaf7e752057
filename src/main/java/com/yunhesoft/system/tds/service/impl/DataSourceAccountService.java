package com.yunhesoft.system.tds.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.applyConf.entity.dto.ApplyParams;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormMeter;
import com.yunhesoft.system.applyConf.service.IAccountFormService;
import com.yunhesoft.system.applyConf.service.IApplyConfService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.mongodb.service.MongoDBService;
import com.yunhesoft.system.tds.entity.dto.LimsDataDto;
import com.yunhesoft.system.tds.entity.dto.TdsAccountDto;
import com.yunhesoft.system.tds.entity.po.AccountAutoSaveTodo;
import com.yunhesoft.system.tds.entity.po.TdsAccountConf;
import com.yunhesoft.system.tds.entity.po.TdsAccountCountCol;
import com.yunhesoft.system.tds.entity.po.TdsAccountCountConf;
import com.yunhesoft.system.tds.entity.po.TdsAccountMarkinfo;
import com.yunhesoft.system.tds.entity.po.TdsAccountMeter;
import com.yunhesoft.system.tds.entity.po.TdsAccountOutparam;
import com.yunhesoft.system.tds.entity.po.TdsAccountParam;
import com.yunhesoft.system.tds.entity.po.TdsAccountTag;
import com.yunhesoft.system.tds.entity.po.TdsAccountTagVersion;
import com.yunhesoft.system.tds.entity.po.TdsAccountTime;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.entity.vo.LimsDataVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountCountConfVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountMeterVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountOutparamVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountParamVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountTagVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountVo;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;

@Service
public class DataSourceAccountService implements IDataSourceAccountService {

	@Autowired
	EntityService srv;

	@Autowired
	private MongoDBService mongoDBServ;
	
	@Resource
	private ApplicationContext context;//应用事件服务

	private final String accountTabName = "accountTab";
	private final String accountNewTab = "ac";
	private int sortno = 1;
//	@Autowired
//    private ISysConfigService sysConfigSrv;
	@Autowired
	private IApplyConfService applySrv;
	@Autowired
	private IAccountFormService formSrv;

	@Override
	public List<TdsAccountMeter> getAccountMeterList(String tdsAlias, String confCode, String tagName) {

		Where where = Where.create();
		where.eq(TdsAccountMeter::getTmused, 1);
		where.eq(TdsAccountMeter::getTdsalias, tdsAlias);
		where.eq(TdsAccountMeter::getConfCode, confCode);
		if (StringUtils.isNotEmpty(tagName)) {// 根据仪表位号进行查询
//			List<String> tagList = Coms.StrToList(tagNumber, ",");
//			where.in(TdsAccountMeter::getTagnumber, tagList.toArray());
			where.like(TdsAccountMeter::getShowName, tagName);
		}
		Order order = Order.create();
		order.order(TdsAccountMeter::getTmsort);
		List<TdsAccountMeter> rlist = srv.queryList(TdsAccountMeter.class, where, order);

		return rlist;
	}

	private List<TdsAccountMeter> getAccountMeter(String tdsAlias, String confCode) {

		Where where = Where.create();
		where.eq(TdsAccountMeter::getTmused, 1);
		where.eq(TdsAccountMeter::getTdsalias, tdsAlias);
		where.eq(TdsAccountMeter::getConfCode, confCode);
		List<TdsAccountMeter> rlist = srv.queryList(TdsAccountMeter.class, where, null);

		return rlist;
	}

	private List<TdsAccountMeterVo> getAccountMeterVo(String tdsAlias, String confCode) {
		List<TdsAccountMeterVo> rlist = new ArrayList<TdsAccountMeterVo>();

		List<TdsAccountMeter> mlist = getAccountMeter(tdsAlias, confCode);
		for (TdsAccountMeter obj : mlist) {
			TdsAccountMeterVo vo = ObjUtils.copyTo(obj, TdsAccountMeterVo.class);
			rlist.add(vo);
		}
		return rlist;
	}

	public List<TdsAccountMeter> getAccountMeterByTdsalias(String tdsAlias) {

		Where where = Where.create();
		where.eq(TdsAccountMeter::getTmused, 1);
		where.eq(TdsAccountMeter::getTdsalias, tdsAlias);
		Order order = Order.create();
		order.order(TdsAccountMeter::getTmsort);
		List<TdsAccountMeter> rlist = srv.queryList(TdsAccountMeter.class, where, order);

		return rlist;
	}

	@Override
	/**
	 * @category 保存台账设置仪表
	 */
	public Boolean saveAccountMeter(TdsAccountDto param) {
		List<TdsAccountMeterVo> dotList = param.getDotList();
		if ("del".equals(param.getOp())) {
//			List<String> idList = new ArrayList<String>();
//			for (TdsAccountMeterVo obj : dotList) {
//				idList.add(obj.getId());
//			}
			List<TdsAccountMeter> delList = new ArrayList<TdsAccountMeter>();
			for (TdsAccountMeterVo obj : dotList) {
				delList.add(ObjUtils.copyTo(obj, TdsAccountMeter.class));
			}

			return 1 == srv.deleteByIdBatch(delList);
		} else {// save
			List<TdsAccountMeter> updList = new ArrayList<TdsAccountMeter>();
			for (TdsAccountMeterVo obj : dotList) {
				obj.setDecimalDegit(obj.getDecimalDegit() == null ? -1 : obj.getDecimalDegit());// 小数位无效，按默认保存
				updList.add(ObjUtils.copyTo(obj, TdsAccountMeter.class));
			}
			return 1 == srv.updateByIdBatch(updList);
		}
	}

	@Override
	/**
	 * @category 仪表绑定设置显示不显示
	 */
	public Boolean updShowMeter(TdsAccountDto param) {
		Boolean flag = true;
		List<TdsAccountMeterVo> dotList = param.getDotList();
		if ("show".equals(param.getOp())) {
			// 查已有的设置，在新添加的范围中，进行去除
			Where where = Where.create();
			where.eq(TdsAccountMeter::getTmused, 1);
			where.eq(TdsAccountMeter::getTdsalias, param.getTdsAlias());
			where.eq(TdsAccountMeter::getConfCode, param.getConfCode());
			List<TdsAccountMeter> mlist = srv.queryList(TdsAccountMeter.class, where, null);
			List<String> haveList = new ArrayList<String>();
			for (TdsAccountMeter obj : mlist) {
				haveList.add(obj.getTagid());
			}
			int tmsort = mlist.size() + 1;
			// 添加到仪表配置中
			List<TdsAccountMeter> addList = new ArrayList<TdsAccountMeter>();
			for (TdsAccountMeterVo obj : dotList) {
				if (!haveList.contains(obj.getTagid())) {
					TdsAccountMeter meter = new TdsAccountMeter();
					meter.setId(TMUID.getUID());
					meter.setTdsalias(param.getTdsAlias());
					meter.setConfType(param.getConfType());
					meter.setConfCode(param.getConfCode());

					meter.setRq(obj.getRq());
					meter.setVermark(obj.getVermark());
					meter.setUnitCode(obj.getUnitCode());// 核算单元
					meter.setBelongZone(obj.getBelongZone());// 自动拷贝，可调整
					meter.setBelongDev(obj.getBelongDev());// 自动拷贝，可调整
					meter.setSdunit(obj.getSdunit());// 仪表单位
//					meter.setUnitName(obj.getUnitName());//TODO 指标范围获取并保存
					meter.setShowName(obj.getTagname());
					meter.setDecimalDegit(obj.getDecimalDegit());// 小数位数，读取配置 //--默认为-1即读取默认配置（小于0），否则按小数位进行格式化
					meter.setTagid(obj.getTagid());
					meter.setTagnumber(obj.getTagnumber());
					meter.setDatasource(obj.getDatasource());// 采集位号
					meter.setUpLimit(obj.getUpLimit());
					meter.setLowerLimit(obj.getLowerLimit());
					meter.setTagType(obj.getTagType());
					meter.setWidth(obj.getWidth());// 默认自动宽度
					meter.setAlign("right");// 默认右对齐
					meter.setEditMark(0);// 默认不可编辑
					meter.setTmused(1);
					meter.setTmsort(tmsort++);

					addList.add(meter);
				}
			}
			if (StringUtils.isNotEmpty(addList)) {
				flag = 1 == srv.insertBatch(addList);
			}

		} else {
			List<String> slist = new ArrayList<String>();
			for (TdsAccountMeterVo obj : dotList) {
				slist.add(obj.getId());
			}
			Where where = Where.create();
			where.eq(TdsAccountMeterVo::getTmused, 1);
			where.in(TdsAccountMeterVo::getTagid, slist.toArray());
			flag = 1 == srv.delete(TdsAccountMeter.class, where);
		}
		return flag;
	}

	@Override
	public List<TdsAccountOutparamVo> getOutConf(TdsAccountDto param) {

		Where where = Where.create();
		where.eq(TdsAccountOutparam::getTmused, 1);
		where.eq(TdsAccountOutparam::getTdsalias, param.getTdsAlias());
		Order order = Order.create();
		order.order(TdsAccountOutparam::getTmsort);
		order.order(TdsAccountOutparam::getId);
		List<TdsAccountOutparam> list = srv.queryList(TdsAccountOutparam.class, where, order);
		if (StringUtils.isEmpty(list)) {
			return null;
		} else {
			List<TdsAccountOutparamVo> rlist = new ArrayList<TdsAccountOutparamVo>();
			for (TdsAccountOutparam obj : list) {
				rlist.add(ObjUtils.copyTo(obj, TdsAccountOutparamVo.class));
			}
			return rlist;
		}

	}

	private List<TdsAccountOutparamVo> getOutConf(String tdsAlias) {

		Where where = Where.create();
		where.eq(TdsAccountOutparam::getTmused, 1);
		where.eq(TdsAccountOutparam::getTdsalias, tdsAlias);
		Order order = Order.create();
		order.order(TdsAccountOutparam::getTmsort);
		order.order(TdsAccountOutparam::getId);
		List<TdsAccountOutparam> list = srv.queryList(TdsAccountOutparam.class, where, order);
		if (StringUtils.isEmpty(list)) {
			return null;
		} else {
			List<TdsAccountOutparamVo> rlist = new ArrayList<TdsAccountOutparamVo>();
			for (TdsAccountOutparam obj : list) {
				rlist.add(ObjUtils.copyTo(obj, TdsAccountOutparamVo.class));
			}
			return rlist;
		}

	}

	private List<TdsAccountOutparam> getOutList(String tdsAlias) {

		Where where = Where.create();
		where.eq(TdsAccountOutparam::getTmused, 1);
		where.eq(TdsAccountOutparam::getTdsalias, tdsAlias);
		Order order = Order.create();
		order.order(TdsAccountOutparam::getTmsort);
		order.order(TdsAccountOutparam::getId);
		List<TdsAccountOutparam> list = srv.queryList(TdsAccountOutparam.class, where, order);
		return list;
	}

	@Override
	public List<TdsAccountOutparam> outConfSave(TdsAccountDto param) {
		Boolean flag = true;
		// 仪表时间输出设置保存
		List<TdsAccountOutparamVo> olist = param.getOutList();

		List<TdsAccountOutparam> addList = new ArrayList<TdsAccountOutparam>();
		List<TdsAccountOutparam> updList = new ArrayList<TdsAccountOutparam>();
//		List<TdsAccountOutparam> delList = new ArrayList<TdsAccountOutparam>();
//		
//		for (TdsAccountOutparamVo obj : olist) {
//			if(new Integer(1).equals(obj.getRowFlag())) {//添加
//				TdsAccountOutparam newObj = ObjUtils.copyTo(obj, TdsAccountOutparam.class);
//				newObj.setId(TMUID.getUID());
//				newObj.setTdsalias(param.getTdsAlias());
//				newObj.setTmused(1);
//				
//				addList.add(newObj);
//			}else if(new Integer(1).equals(obj.getRowFlag())) {//删除
//				if(StringUtils.isNotEmpty(obj.getId())) {
//					TdsAccountOutparam delObj = ObjUtils.copyTo(obj, TdsAccountOutparam.class);
//					delList.add(delObj);
//				}
//			}else if(new Integer(0).equals(obj.getRowFlag())) {//更新
//				if(StringUtils.isNotEmpty(obj.getId())) {
//					TdsAccountOutparam updObj = ObjUtils.copyTo(obj, TdsAccountOutparam.class);
//					updList.add(updObj);
//				}
//			}
//		}
//		
//		if(StringUtils.isNotEmpty(addList)) {
//			flag = flag && 1 == srv.insertBatch(addList);
//		}
//		if(StringUtils.isNotEmpty(updList)) {
//			flag = flag && 1 == srv.updateBatch(updList);
//		}
//		if(StringUtils.isNotEmpty(delList)) {
//			flag = flag && 1 == srv.deleteByIdBatch(delList);
//		}

		String tdsAlias = param.getTdsAlias();

		// 计算自定义数的最大值，用于个性自定义列的编号
		Integer maxNo = 0;
		Where whereall = Where.create();
		whereall.eq(TdsAccountOutparam::getTdsalias, tdsAlias);
		List<TdsAccountOutparam> alllist = srv.queryList(TdsAccountOutparam.class, whereall, null);
		if (StringUtils.isNotEmpty(alllist)) {
			for (TdsAccountOutparam obj : alllist) {
				if (StringUtils.isNotEmpty(obj.getColType())) {
					String alias = obj.getAlias();
					if (StringUtils.isNotEmpty(alias) && alias.length() > 3) {
						String no = alias.substring(alias.length() - 3, alias.length());
						if (Coms.judgeLong(no)) {
							int ino = Integer.parseInt(no);
							if (ino > maxNo) {
								maxNo = ino;
							}
						}
					}
				}
			}
		}
		maxNo++;

		Where where = Where.create();
		where.eq(TdsAccountOutparam::getTmused, 1);
		where.eq(TdsAccountOutparam::getTdsalias, tdsAlias);
		List<TdsAccountOutparam> list = srv.queryList(TdsAccountOutparam.class, where, null);
		List<String> idList = new ArrayList<String>();
		for (TdsAccountOutparam obj : list) {
			idList.add(obj.getId());
		}
		if (olist == null) {
//			if(StringUtils.isNotEmpty(list)) {
//				flag = flag && 1 == srv.deleteByIdBatch(list);
//			}
		} else {

			for (TdsAccountOutparamVo vo : olist) {
				String id = vo.getId();
				if (StringUtils.isNotEmpty(id) && idList.contains(id)) {// 更新
					TdsAccountOutparam updObj = ObjUtils.copyTo(vo, TdsAccountOutparam.class);
					updList.add(updObj);
				} else {// 添加
					TdsAccountOutparam newObj = ObjUtils.copyTo(vo, TdsAccountOutparam.class);
					newObj.setId(TMUID.getUID());
					if (StringUtils.isNotEmpty(newObj.getColType())) {
						if ("rowConfirm".equals(newObj.getColType())) {
							newObj.setAlias("rowConfirm");
						} else {
							newObj.setAlias(getAliasNo(vo.getColType(), maxNo));
							maxNo++;
						}
					}
					newObj.setTdsalias(param.getTdsAlias());
					newObj.setTmused(1);
					addList.add(newObj);
				}
			}
			if (StringUtils.isNotEmpty(addList)) {
				flag = flag && 1 == srv.insertBatch(addList);
			}
			if (StringUtils.isNotEmpty(updList)) {
				flag = flag && 1 == srv.updateBatch(updList);
			}
		}

		return srv.queryList(TdsAccountOutparam.class, where, null);
	}

	/**
	 * @category 生成别名编号
	 * @param maxNo
	 * @return
	 */
	private String getAliasNo(String col, Integer maxNo) {
		String rv = "00";
		if (maxNo < 100) {
			rv += String.valueOf(maxNo);
		} else {
			rv = String.valueOf(maxNo);
		}
		return col + rv;
	}

	@Override
	public Boolean deleteAccountOut(TdsAccountDto param) {
		String tdsAlias = param.getTdsAlias();
		Where where = Where.create();
		where.eq(TdsAccountOutparam::getTmused, 1);
		where.eq(TdsAccountOutparam::getTdsalias, tdsAlias);
		List<TdsAccountOutparam> list = srv.queryList(TdsAccountOutparam.class, where, null);
		if (StringUtils.isNotEmpty(list)) {
			return srv.deleteByIdBatch(list) == 1;
		}

		return true;
	}

	@Override
	public TdsAccountTime getTimeConf(TdsAccountDto param) {

//		Where where = Where.create();
//		where.eq(TdsAccountTime::getTmused, 1);
//		where.eq(TdsAccountTime::getTdsalias, param.getTdsAlias());
//		List<TdsAccountTime> rlist = srv.queryList(TdsAccountTime.class, where, null);
//		if(StringUtils.isEmpty(rlist)) {
//			return null;
//		}else {
//			return rlist.get(0);
//		}
		return getTimeConf(param.getTdsAlias());
	}

	public TdsAccountTime getTimeConf(String tdsAlias) {

		Where where = Where.create();
		where.eq(TdsAccountTime::getTmused, 1);
		where.eq(TdsAccountTime::getTdsalias, tdsAlias);
		List<TdsAccountTime> rlist = srv.queryList(TdsAccountTime.class, where, null);
		if (StringUtils.isEmpty(rlist)) {
			return null;
		} else {
			return rlist.get(0);
		}
	}

	@Override
	public TdsAccountTime timeConfSave(TdsAccountDto param) {
		// 时间设置保存
		TdsAccountTime obj = param.getAccountTime();
		if (StringUtils.isEmpty(obj.getId())) {
			obj.setId(TMUID.getUID());
			obj.setTdsalias(param.getTdsAlias());
			obj.setTmused(1);
			obj.setTmsort(1);
			srv.insert(obj);
			return obj;
		} else {
			srv.update(obj);
			return obj;
		}
	}

	@Override
	public TdsAccountConf getAccountConf(String tdsAlias) {
		Where where = Where.create();
		where.eq(TdsAccountConf::getTdsalias, tdsAlias);
		List<TdsAccountConf> rlist = srv.queryList(TdsAccountConf.class, where, null);
		if (StringUtils.isNotEmpty(rlist)) {
			return rlist.get(0);
		} else {
			return null;
		}
	}

	@Override
	public TdsAccountConf saveAccountConf(TdsAccountDto param) {
		TdsAccountConf obj = param.getConf();
		if (StringUtils.isEmpty(obj.getId())) {
			obj.setId(TMUID.getUID());
			obj.setTdsalias(param.getTdsAlias());
			srv.insert(obj);

			// 第一次初始化，增加初始化参数，输入参数增加日期、核算对象、班次；绑定参数增加对应绑定；输出参数增加确认列、时间列、动态列；时间配置初始化按班次显示数据
			List<TdsinPara> ilist = new ArrayList<TdsinPara>();
			List<TdsAccountParam> aplist = new ArrayList<TdsAccountParam>();
			List<TdsAccountOutparam> olist = new ArrayList<TdsAccountOutparam>();
			TdsAccountTime atime = new TdsAccountTime();

			/*--输入参数---------------------------------------*/
			TdsinPara inObj = new TdsinPara();
			inObj.setTdsalias(param.getTdsAlias());
			inObj.setDefaultKeyScript("");
			inObj.setDefaultValueScript("");
			inObj.setDisplay(1);
			inObj.setMemo("");

			TdsinPara in_rq = ObjUtils.copyTo(inObj, TdsinPara.class);
			in_rq.setId(TMUID.getUID());
			in_rq.setParaId(1);
			in_rq.setParaAlias("rq");
			in_rq.setParaName("日期");
			in_rq.setComponentType("datefield");
			in_rq.setDataType("tdsDate");
			in_rq.setDefaultKeyScript("getToday()");
			ilist.add(in_rq);

			TdsinPara in_unit = ObjUtils.copyTo(inObj, TdsinPara.class);
			in_unit.setId(TMUID.getUID());
			in_unit.setParaId(2);
			in_unit.setParaAlias("unitCode");
			in_unit.setParaName("核算单元");
			in_unit.setComponentType("textfield");
			in_unit.setDataType("tdsString");
			in_unit.setDisplay(0);
			ilist.add(in_unit);

			TdsinPara in_bc = ObjUtils.copyTo(inObj, TdsinPara.class);
			in_bc.setId(TMUID.getUID());
			in_bc.setParaId(3);
			in_bc.setParaAlias("bc");
			in_bc.setParaName("班次信息");
			in_bc.setComponentType("textfield");
			in_bc.setDataType("tdsString");
			in_bc.setDisplay(0);
			ilist.add(in_bc);

			TdsinPara in_dataid = ObjUtils.copyTo(inObj, TdsinPara.class);
			in_dataid.setId(TMUID.getUID());
			in_dataid.setParaId(4);
			in_dataid.setParaAlias("dataid");
			in_dataid.setParaName("数据标识");
			in_dataid.setComponentType("textfield");
			in_dataid.setDataType("tdsString");
			in_dataid.setDisplay(0);
			ilist.add(in_dataid);

			TdsinPara in_formcode = ObjUtils.copyTo(inObj, TdsinPara.class);
			in_formcode.setId(TMUID.getUID());
			in_formcode.setParaId(5);
			in_formcode.setParaAlias("accountId");
			in_formcode.setParaName("表单标识");
			in_formcode.setComponentType("textfield");
			in_formcode.setDataType("tdsString");
			in_formcode.setDisplay(0);
			ilist.add(in_formcode);
			
			TdsinPara in_ledgerModule = ObjUtils.copyTo(inObj, TdsinPara.class);
			in_ledgerModule.setId(TMUID.getUID());
			in_ledgerModule.setParaId(5);
			in_ledgerModule.setParaAlias("ledgerModuleId");
			in_ledgerModule.setParaName("台账模型ID");
			in_ledgerModule.setComponentType("textfield");
			in_ledgerModule.setDataType("tdsString");
			in_ledgerModule.setDisplay(0);
			ilist.add(in_ledgerModule);
			
			TdsinPara in_ledgerInitType = ObjUtils.copyTo(inObj, TdsinPara.class);
			in_ledgerInitType.setId(TMUID.getUID());
			in_ledgerInitType.setParaId(5);
			in_ledgerInitType.setParaAlias("ledgerInitType");
			in_ledgerInitType.setParaName("台账初始化ID");
			in_ledgerInitType.setComponentType("textfield");
			in_ledgerInitType.setDataType("tdsString");
			in_ledgerInitType.setDisplay(0);
			ilist.add(in_ledgerInitType);

			/*--绑定参数---------------------------------------*/
			TdsAccountParam ap1 = new TdsAccountParam();
			ap1.setId(TMUID.getUID());
			ap1.setTdsalias(param.getTdsAlias());
			ap1.setOldParamId(in_rq.getId());
			ap1.setOldParamAlias(in_rq.getParaAlias());
			ap1.setNewParamId("day");
			aplist.add(ap1);
			TdsAccountParam ap2 = new TdsAccountParam();
			ap2.setId(TMUID.getUID());
			ap2.setTdsalias(param.getTdsAlias());
			ap2.setOldParamId(in_unit.getId());
			ap2.setOldParamAlias(in_unit.getParaAlias());
			ap2.setNewParamId("unit");
			aplist.add(ap2);
			TdsAccountParam ap3 = new TdsAccountParam();
			ap3.setId(TMUID.getUID());
			ap3.setTdsalias(param.getTdsAlias());
			ap3.setOldParamId(in_bc.getId());
			ap3.setOldParamAlias(in_bc.getParaAlias());
			ap3.setNewParamId("bc");
			aplist.add(ap3);
			TdsAccountParam ap4 = new TdsAccountParam();
			ap4.setId(TMUID.getUID());
			ap4.setTdsalias(param.getTdsAlias());
			ap4.setOldParamId(in_dataid.getId());
			ap4.setOldParamAlias(in_dataid.getParaAlias());
			ap4.setNewParamId("dataid");
			aplist.add(ap4);
			TdsAccountParam ap5 = new TdsAccountParam();
			ap5.setId(TMUID.getUID());
			ap5.setTdsalias(param.getTdsAlias());
			ap5.setOldParamId(in_formcode.getId());
			ap5.setOldParamAlias(in_formcode.getParaAlias());
			ap5.setNewParamId("accountId");
			aplist.add(ap5);

			/*--台账输出参数---------------------------------------*/
			TdsAccountOutparam o0 = new TdsAccountOutparam();
			o0.setId(TMUID.getUID());
			o0.setTdsalias(param.getTdsAlias());
			o0.setDynamicMark(0);
			o0.setShowName("采集时间");
			o0.setWidth(120);
			o0.setAlias("");
			o0.setAlign("center");
			o0.setBindDay("");
			o0.setBindMeter("");
			o0.setBindTime("");
			o0.setDayFixed(0);
			o0.setEditFixed(0);
			o0.setEditMark(0);
			o0.setMeterMark(0);
			o0.setTimeMark(0);
			o0.setTmsort(1);
			o0.setTmused(1);
			o0.setVisible(1);
			o0.setDecimalDegit(-1);
			o0.setLowerLimit(-999999d);
			o0.setUpLimit(999999d);
			o0.setBelongZone("");
			o0.setBelongDev("");
			o0.setJobInputTimeMark(1);
			olist.add(o0);

			TdsAccountOutparam o1 = new TdsAccountOutparam();
			o1.setId(TMUID.getUID());
			o1.setTdsalias(param.getTdsAlias());
			o1.setDynamicMark(0);
			o1.setShowName("ID");
			o1.setWidth(120);
			o1.setAlias("");
			o1.setAlign("center");
			o1.setBindDay("");
			o1.setBindMeter("");
			o1.setBindTime("");
			o1.setDayFixed(0);
			o1.setEditFixed(0);
			o1.setEditMark(0);
			o1.setMeterMark(0);
			o1.setTimeMark(1);
			o1.setTmsort(1);
			o1.setTmused(1);
			o1.setVisible(0);
			o1.setDecimalDegit(-1);
			o1.setLowerLimit(-999999d);
			o1.setUpLimit(999999d);
			o1.setBelongZone("");
			o1.setBelongDev("");
			olist.add(o1);

			TdsAccountOutparam o2 = new TdsAccountOutparam();
			o2.setId(TMUID.getUID());
			o2.setTdsalias(param.getTdsAlias());
			o2.setDynamicMark(1);
			o2.setShowName("仪表信息");
			o2.setWidth(0);
			o2.setTmsort(2);
			o2.setAlias("");
			o2.setAlign("right");
			o2.setBindDay("");
			o2.setBindMeter("");
			o2.setBindTime("");
			o2.setDayFixed(0);
			o2.setEditFixed(0);
			o2.setEditMark(0);
			o2.setMeterMark(0);
			o2.setTimeMark(0);
			o2.setTmused(1);
			o2.setVisible(1);
			o2.setDecimalDegit(-1);
			o2.setLowerLimit(-999999d);
			o2.setUpLimit(999999d);
			o2.setBelongZone("");
			o2.setBelongDev("");
			olist.add(o2);

			TdsAccountOutparam o3 = new TdsAccountOutparam();
			o3.setId(TMUID.getUID());
			o3.setTdsalias(param.getTdsAlias());
			o3.setDynamicMark(0);
			o3.setColType("rowConfirm");
			o3.setShowName("确认信息");
			o3.setWidth(120);
			o3.setTmsort(3);
			o3.setAlias("");
			o3.setAlign("center");
			o3.setBindDay("");
			o3.setBindMeter("");
			o3.setBindTime("");
			o3.setDayFixed(0);
			o3.setEditFixed(0);
			o3.setEditMark(0);
			o3.setMeterMark(0);
			o3.setTimeMark(0);
			o3.setTmused(1);
			o3.setVisible(1);
			o3.setDecimalDegit(-1);
			o3.setLowerLimit(-999999d);
			o3.setUpLimit(999999d);
			o3.setBelongZone("");
			o3.setBelongDev("");
			o3.setFixed("right");
			olist.add(o3);

			/*--台账时间配置---------------------------------------*/
			atime.setId(TMUID.getUID());
			atime.setTdsalias(param.getTdsAlias());
			atime.setStartBingDay("bc");
			atime.setStartFixed("0");
			atime.setStartBingTime("00:00");
			atime.setStartRound(1);
			atime.setEndBingDay("bc");
			atime.setEndFixed("0");
			atime.setEndBingTime("00:00");
			atime.setEndRound(0);
			atime.setTimePoint(0);
			atime.setTimeStep("120");
			atime.setTimeFormat("yyyy-MM-dd HH:mm");
			atime.setEditRound(0);
			atime.setShowMode("point");
			atime.setTmused(1);

			srv.insertBatch(ilist);
			srv.insertBatch(aplist);
			srv.insertBatch(olist);
			srv.insert(atime);

		} else {
			srv.updateById(obj);
		}
		return obj;
	}

	@Override
	public List<TdsAccountParamVo> getAccountParam(String tdsAlias) {
		List<TdsAccountParamVo> rlist = new ArrayList<TdsAccountParamVo>();
		// 读取数据源输入参数信息
		Where wherei = Where.create();
		wherei.eq(TdsinPara::getTdsalias, tdsAlias);
		List<TdsinPara> ilist = srv.queryList(TdsinPara.class, wherei, null);
		if (StringUtils.isNotEmpty(ilist)) {
			for (TdsinPara inpara : ilist) {
				TdsAccountParamVo vo = ObjUtils.copyTo(inpara, TdsAccountParamVo.class);
				rlist.add(vo);
			}
		}
		return rlist;
	}

	@Override
	public List<TdsAccountParamVo> getAccountBindParam(String tdsAlias) {
		List<TdsAccountParamVo> rlist = new ArrayList<TdsAccountParamVo>();

		// 读取数据源输入参数信息
		Where wherei = Where.create();
		wherei.eq(TdsinPara::getTdsalias, tdsAlias);
		List<TdsinPara> ilist = srv.queryList(TdsinPara.class, wherei, null);
		// 读取已有对应关系
		List<TdsAccountParam> alist = getAccountParamList(tdsAlias);

		Map<String, TdsAccountParam> amap = new HashMap<String, TdsAccountParam>();
		for (TdsAccountParam aparam : alist) {
			amap.put(aparam.getOldParamId(), aparam);
		}

		// 整合输出
		for (TdsinPara inpara : ilist) {
			TdsAccountParamVo vo = ObjUtils.copyTo(inpara, TdsAccountParamVo.class);
			if (amap.containsKey(vo.getId())) {
				TdsAccountParam aparam = amap.get(vo.getId());
				vo.setId(aparam.getId());
				vo.setOldParamAlias(aparam.getOldParamAlias());
				vo.setOldParamId(aparam.getOldParamId());
				vo.setNewParamId(aparam.getNewParamId());
			} else {
				vo.setOldParamId(vo.getId());
				vo.setId(null);
			}
			rlist.add(vo);
		}

		return rlist;
	}

	private List<TdsAccountParam> getAccountParamList(String tdsAlias) {
		Where where = Where.create();
		where.eq(TdsAccountParam::getTdsalias, tdsAlias);
		List<TdsAccountParam> alist = srv.queryList(TdsAccountParam.class, where, null);
		return alist;
	}

	private Map<String, TdsAccountParam> getAccountParamMap(String tdsAlias) {
		List<TdsAccountParam> alist = getAccountParamList(tdsAlias);

		Map<String, TdsAccountParam> amap = new HashMap<String, TdsAccountParam>();
		for (TdsAccountParam aparam : alist) {
			amap.put(aparam.getNewParamId(), aparam);
		}
		return amap;
	}

	private Map<String, TdsAccountParam> getAccountParamAliasMap(String tdsAlias) {
		List<TdsAccountParam> alist = getAccountParamList(tdsAlias);

		Map<String, TdsAccountParam> amap = new HashMap<String, TdsAccountParam>();
		for (TdsAccountParam aparam : alist) {
			amap.put(aparam.getOldParamAlias(), aparam);
		}
		return amap;
	}

	@Override
	public Boolean saveAccountBindParam(TdsAccountDto param) {
		List<TdsAccountParamVo> list = param.getParamList();

		Map<String, TdsAccountParam> amap = getAccountParamAliasMap(param.getTdsAlias());

		List<TdsAccountParam> addList = new ArrayList<TdsAccountParam>();
		List<TdsAccountParam> updList = new ArrayList<TdsAccountParam>();
		List<TdsAccountParam> delList = new ArrayList<TdsAccountParam>();

		for (TdsAccountParamVo vo : list) {
			if (amap.containsKey(vo.getParaAlias())) {// 有对应数据
				TdsAccountParam obj = amap.get(vo.getParaAlias());
				if (StringUtils.isEmpty(vo.getNewParamId())) {// 无绑定内容，进行删除
					delList.add(obj);
				} else {// 有绑定内容，进行更新
					obj.setNewParamId(vo.getNewParamId());
					obj.setOldParamId(vo.getId());// 输入参数的id
					obj.setOldParamAlias(vo.getParaAlias());// 输入参数的别名
					updList.add(obj);
				}
			} else if (StringUtils.isNotEmpty(vo.getNewParamId())) {// 添加
				TdsAccountParam obj = new TdsAccountParam();
				obj.setId(TMUID.getUID());
				obj.setTdsalias(param.getTdsAlias());
				obj.setOldParamId(vo.getId());
				obj.setOldParamAlias(vo.getParaAlias());
				obj.setNewParamId(vo.getNewParamId());
				addList.add(obj);
			}
		}

		if (StringUtils.isNotEmpty(addList)) {
			srv.insertBatch(addList);
		}
		if (StringUtils.isNotEmpty(updList)) {
			srv.updateBatch(updList);
		}
		if (StringUtils.isNotEmpty(delList)) {
			srv.deleteByIdBatch(delList);
		}

		return null;
	}

	@Override
	public TdsAccountVo getAccountData(String tdsAlias) {
		TdsAccountVo vo = new TdsAccountVo();
		// 初始化数据
		initTagInfo();

		vo.setConf(getAccountConf(tdsAlias));
		vo.setParamList(getTdsAccountParam(tdsAlias));
//		vo.setMeterList(getAccountMeterVo(tdsAlias, confCode, ""));
		vo.setOutList(getOutConf(tdsAlias));
		vo.setTimeConf(getTimeConf(tdsAlias));
		return vo;
	}

	@Override
	public String getConfSModes() {
		ApplyParams param = new ApplyParams();
		param.setApplyAlias("ledger_title_name");
		String smodeJson = applySrv.getApplyConfValue(param);
		return smodeJson;
	}

	/**
	 * @category 初始化台账功能仪表信息
	 */
	public void initTagInfo() {
		// 查询表是否有数据(查版本数据)，无数据，进行一次初始化
		Where where = Where.create();
		where.eq(TdsAccountTagVersion::getTmused, 1);
		Long count = srv.queryCount(TdsAccountTagVersion.class, where);
		if (count == 0) {
			// 获取核算单元
			String sql = "select ID,NAME from COSTUINT";
			List<Map<String, Object>> ulist = srv.queryListMap(sql, null);
			Map<String, String> umap = new HashMap<String, String>();// Map<id, 名称>
			for (Map<String, Object> map : ulist) {
				String id = String.valueOf(map.get("ID"));
				String name = String.valueOf(map.get("NAME"));
				umap.put(id, name);
			}

			// 获取分类数据，用于构建仪表单元、设备
			sql = "select ID, PID, NAME, CTYPE, UNITID from COSTUNITSAMPLECLASS where UNITID is not null and TMUSED=1 order by UNITID, TMSORT";
			List<Map<String, Object>> list = srv.queryListMap(sql, null);
			Map<String, List<Map<String, Object>>> clsMap = new HashMap<String, List<Map<String, Object>>>();// Map<pid,
																												// 分类列表>
			for (Map<String, Object> map : list) {
				String pid = String.valueOf(map.get("PID"));
				if (clsMap.containsKey(pid)) {
					clsMap.get(pid).add(map);
				} else {
					List<Map<String, Object>> _list = new ArrayList<Map<String, Object>>();
					_list.add(map);
					clsMap.put(pid, _list);
				}
			}

			// 只同步控制指标、化验分析(lims)指标, ctype 1成本指标（核算功能内部使用）2控制（平稳率）指标 3 lims指标，这里只同步2和3
			// 实际应用时，恢复过滤TODO
//			sql = "select ID, PID, UNITID, NAME, TAGNUMBER,DATASOURCE,SDUNIT, CTYPE, SOURCEYPE, INDEXRANGEUPPER as uplimit, INDEXRANGELOWER as lowlimit, POINTCOUNTLEDGER as bit,"
//					+ " PROCESSUNITNAME, PRODUCTNAME, SAMPLINGPOINT, ANALYSISNAME, ANALYSISSUBNAME, ISWRITEINPUT, CONTROLTYPE, DEFAULTVAL, COMBINITKEY, COMBINITVAL,ISWRITEBACKINFLUXDB "
//					+ "from COSTUNITSAMPLEDOT where TMUSED=1 and CTYPE in ('2','3') and (ISSHOWLEDGER is null or ISSHOWLEDGER=1) order by UNITID, TMSORT";// and
																																							// ISSHOWLEDGER=1
			sql = "select * from COSTUNITSAMPLEDOT where TMUSED=1 and CTYPE in ('2','3') and (ISSHOWLEDGER is null or ISSHOWLEDGER=1) order by UNITID, TMSORT";
			list = srv.queryListMap(sql, null);
			Map<String, List<Map<String, Object>>> tagMap = new HashMap<String, List<Map<String, Object>>>();// Map<pid,
																												// 仪表列表>
			for (Map<String, Object> map : list) {
				String pid = String.valueOf(map.get("PID"));
				if (tagMap.containsKey(pid)) {
					tagMap.get(pid).add(map);
				} else {
					List<Map<String, Object>> _list = new ArrayList<Map<String, Object>>();
					_list.add(map);
					tagMap.put(pid, _list);
				}
			}

			String tempUnitId = null;

			List<Map<String, Object>> clslist = clsMap.get("root");// 获取分类节点
			if (StringUtils.isNotEmpty(clslist)) {
				Map<String, TdsAccountTagVersion> vmap = new HashMap<String, TdsAccountTagVersion>();
				List<TdsAccountTag> addList = new ArrayList<TdsAccountTag>();

				Map<String, List<String>> clsNameMap = new HashMap<String, List<String>>();
				for (Map<String, Object> map : clslist) {

					if (!String.valueOf(map.get("UNITID")).equals(tempUnitId)) {
						sortno = 1;
						tempUnitId = String.valueOf(map.get("UNITID"));
					}

					List<String> nameList = new ArrayList<String>();
					nameList.add(String.valueOf(map.get("NAME")));
					clsNameMap.put(String.valueOf(map.get("ID")), nameList);
					setTagData(map, clsMap, tagMap, addList, clsNameMap, vmap, umap);
				}

				if (vmap.size() > 0 && addList.size() > 0) {
					List<TdsAccountTagVersion> verList = new ArrayList<TdsAccountTagVersion>();
					for (TdsAccountTagVersion v : vmap.values()) {
						verList.add(v);
					}
					srv.insertBatch(verList, 50);// 插入版本数据
					srv.insertBatch(addList, 200);// 插入仪表数据
				}
			}
		}
	}

	/**
	 * @category 递归获取仪表及分类信息
	 * @param obj
	 * @param cmap
	 * @param dmap
	 * @param rlist
	 * @param clsNameMap
	 */
	private void setTagData(Map<String, Object> map, Map<String, List<Map<String, Object>>> clsMap,
			Map<String, List<Map<String, Object>>> tagMap, List<TdsAccountTag> addList,
			Map<String, List<String>> clsNameMap, Map<String, TdsAccountTagVersion> vmap, Map<String, String> umap) {

		String id = String.valueOf(map.get("ID"));
		List<Map<String, Object>> clist = clsMap.get(id);
		if (StringUtils.isNotEmpty(clist)) {
			for (Map<String, Object> vo : clist) {
				String itemid = String.valueOf(vo.get("ID"));
				String name = String.valueOf(vo.get("NAME"));
				// 获取父名称信息，同时保存子信息
				List<String> nameList = clsNameMap.get(id);
				if (StringUtils.isNotEmpty(nameList)) {
					List<String> tlist = new ArrayList<String>();
					for (String cname : nameList) {
						tlist.add(cname);
					}
					tlist.add(name);
					clsNameMap.put(itemid, tlist);
				} else {
					nameList = new ArrayList<String>();
					nameList.add(name);
					clsNameMap.put(itemid, nameList);
				}

				setTagData(vo, clsMap, tagMap, addList, clsNameMap, vmap, umap);
			}
		}

		List<Map<String, Object>> tagList = tagMap.get(id);
		if (StringUtils.isNotEmpty(tagList)) {
			for (Map<String, Object> tag : tagList) {
				// ID, PID, UNITID, NAME, TAGNUMBER,DATASOURCE,SDUNIT, CTYPE, SOURCEYPE,
				// uplimit, lowlimit,bit
				String pid = String.valueOf(tag.get("PID"));

				// 带入单元、设备名称（分类名）
				List<String> nameList = clsNameMap.get(pid);
				String zone = "", dev = "", tname = "", tagpname = "";
				if (StringUtils.isNotEmpty(nameList)) {
					if (nameList.size() == 1) {
						zone = nameList.get(0);
						tagpname = zone;
					} else if (nameList.size() >= 3) {
						zone = nameList.get(nameList.size() - 3);
						dev = nameList.get(nameList.size() - 2);
						tname = nameList.get(nameList.size() - 1);
						tagpname = tname;
					} else if (nameList.size() >= 2) {
						zone = nameList.get(nameList.size() - 2);
						dev = nameList.get(nameList.size() - 1);
						tagpname = dev;
					}
				}

				String tagid = String.valueOf(tag.get("ID"));
				String tagpid = String.valueOf(tag.get("PID"));
				String unitCode = String.valueOf(tag.get("UNITID"));
				String unitName = umap.get(unitCode) == null ? "" : umap.get(unitCode);
				String NAME = String.valueOf(tag.get("NAME") == null ? "" : tag.get("NAME"));
				String TAGNUMBER = String.valueOf(tag.get("TAGNUMBER") == null ? "" : tag.get("TAGNUMBER"));
				String DATASOURCE = String.valueOf(tag.get("DATASOURCE") == null ? "" : tag.get("DATASOURCE"));
				String SDUNIT = String.valueOf(tag.get("SDUNIT") == null ? "" : tag.get("SDUNIT"));
				String SOURCEYPE = String.valueOf(tag.get("SOURCEYPE") == null ? "" : tag.get("SOURCEYPE"));
				String CTYPE = String.valueOf(tag.get("CTYPE"));// 只用到2和3 ,同步过来的可能有其他字符

				// PROCESSUNIT, PRODUCTNAME, SAMPLINGPOINT, ANALYSISNAME, ITEMNAME
				String PROCESSUNITNAME = String
						.valueOf(tag.get("PROCESSUNITNAME") == null ? "" : tag.get("PROCESSUNITNAME"));
				String PRODUCTNAME = String.valueOf(tag.get("PRODUCTNAME") == null ? "" : tag.get("PRODUCTNAME"));
				String SAMPLINGPOINT = String.valueOf(tag.get("SAMPLINGPOINT") == null ? "" : tag.get("SAMPLINGPOINT"));
				String ANALYSISNAME = String.valueOf(tag.get("ANALYSISNAME") == null ? "" : tag.get("ANALYSISNAME"));
				String ANALYSISSUBNAME = String
						.valueOf(tag.get("ANALYSISSUBNAME") == null ? "" : tag.get("ANALYSISSUBNAME"));
				
				Integer ISWRITEINPUT = tag.get("ISWRITEINPUT") == null ? null : Integer.parseInt(String.valueOf(tag.get("ISWRITEINPUT")));//手工填写 1是
				Integer CONTROLTYPE = tag.get("CONTROLTYPE") == null ? null : Integer.parseInt(String.valueOf(tag.get("CONTROLTYPE")));//组件类型
				String DEFAULTVAL = String.valueOf(tag.get("DEFAULTVAL") == null ? "" : tag.get("DEFAULTVAL"));//默认值
				String COMBINITKEY = String.valueOf(tag.get("COMBINITKEY") == null ? "" : tag.get("COMBINITKEY"));//下拉key
				String COMBINITVAL = String.valueOf(tag.get("COMBINITVAL") == null ? "" : tag.get("COMBINITVAL"));//下拉内容

				Double upLimit = null;// tag.get("uplimit");
				Double lowerLimit = null;// String.valueOf(tag.get("lowlimit"));
				Integer decimalDegit = null;// String.valueOf(tag.get("bit"));

				String upstr = String.valueOf(tag.get("INDEXRANGEUPPER"));
				String lowstr = String.valueOf(tag.get("INDEXRANGELOWER"));
				String bitstr = String.valueOf(tag.get("POINTCOUNTLEDGER"));
				if (Coms.judgeDouble(upstr)) {
					upLimit = Double.parseDouble(upstr);
				}
				if (Coms.judgeDouble(lowstr)) {
					lowerLimit = Double.parseDouble(lowstr);
				}
				if (Coms.judgeDouble(bitstr)) {
					decimalDegit = Integer.parseInt(bitstr);
				}
				String infux = String.valueOf(tag.get("ISWRITEBACKINFLUXDB"));

				TdsAccountTagVersion v = vmap.get(unitCode);
				if (v == null) {
					v = new TdsAccountTagVersion();
					v.setId(TMUID.getUID());
					v.setRq("2020-01-01");
					v.setUnitcode(unitCode);
					v.setTmused(1);
					vmap.put(unitCode, v);
				}

				TdsAccountTag vo = new TdsAccountTag();
				vo.setId(TMUID.getUID());
				vo.setUnitCode(unitCode);
				vo.setUnitName(unitName);
				vo.setBelongZone(zone);
				vo.setBelongDev(dev);
				vo.setBelongTag(tname);
				vo.setVermark(v.getId());
				vo.setTagid(tagid);
				vo.setTagpid(tagpid);
				vo.setTagpname(tagpname);
				vo.setTagname(NAME);
				vo.setTagnumber(TAGNUMBER);
				vo.setDatasource(DATASOURCE);
				vo.setSourceype(SOURCEYPE);
				vo.setCtype(CTYPE);// 仪表类型
				vo.setSdunit(SDUNIT);
				vo.setUpLimit(upLimit);
				vo.setLowerLimit(lowerLimit);
				vo.setDecimalDegit(decimalDegit);
				vo.setRq(v.getRq());
				vo.setWidth(getDefaultWidth(vo));

				vo.setProcessUnitName(PROCESSUNITNAME);// 5个用于limis数据的属性
				vo.setProductName(PRODUCTNAME);
				vo.setSamplingPoint(SAMPLINGPOINT);
				vo.setAnalysisName(ANALYSISNAME);
				vo.setAnalysisSubName(ANALYSISSUBNAME);
				
				vo.setIswriteinput(ISWRITEINPUT);
				vo.setControltype(CONTROLTYPE);
				vo.setDefaultval(DEFAULTVAL);
				vo.setCombinitkey(COMBINITKEY);
				vo.setCombinitval(COMBINITVAL);

				vo.setEditMark(0);// 不可编辑
				vo.setAlign("right");// 右对齐
				vo.setTmused(1);
				vo.setTmsort(this.sortno++);

				if (nameList.size() > 0) {
					for (int i = 0, il = nameList.size(); i < il; i++) {
						if (i == 0) {
							vo.setClass1(nameList.get(i));
						} else if (i == 1) {
							vo.setClass2(nameList.get(i));
						} else if (i == 2) {
							vo.setClass3(nameList.get(i));
						} else if (i == 3) {
							vo.setClass4(nameList.get(i));
						} else if (i == 4) {
							vo.setClass5(nameList.get(i));
						} else if (i == 5) {
							vo.setClass6(nameList.get(i));
						} else if (i == 6) {
							vo.setClass7(nameList.get(i));
						} else if (i == 7) {
							vo.setClass8(nameList.get(i));
						} else if (i == 8) {
							vo.setClass9(nameList.get(i));
						} else if (i == 9) {
							vo.setClass10(nameList.get(i));
						}
					}
				}

				addList.add(vo);
			}
		}
	}

	// 根据上下限计算台账仪表默认宽度
	public int getDefaultWidth(TdsAccountTag vo) {
		Boolean havePot = false;
		Integer bit = vo.getDecimalDegit();
		Double up = vo.getUpLimit();
		Double low = vo.getLowerLimit();

		int len = 0;
		if (up != null) {
			int l = String.valueOf((int) Math.floor(up)).length();
			if (l > len) {
				len = l;
			}
		}
		if (low != null) {
			int l = String.valueOf((int) Math.floor(low)).length();
			if (l > len) {
				len = l;
			}
		}
		if (bit != null) {
			if (bit > 0) {
				len += bit;
				havePot = true;
			}
		}
		if (len > 0) {// 没有上下限，默认0
			Double pxWidth = 15d;// 一个数字大概多少像素 12 -> 15
			len = (int) (len * pxWidth);
			if (havePot) {
				len += 5;// 小数点大概多少像素
			}
//			if(low < 0) {
//				len+=3;//增加负号占位
//			}
		}

		return len;
	}

	private List<TdsAccountParamVo> getTdsAccountParam(String tdsAlias) {
		List<TdsAccountParamVo> rlist = new ArrayList<TdsAccountParamVo>();
		// 读取数据源输入参数信息
		Where wherei = Where.create();
		wherei.eq(TdsinPara::getTdsalias, tdsAlias);
		List<TdsAccountParam> ilist = srv.queryList(TdsAccountParam.class, wherei, null);
		if (StringUtils.isNotEmpty(ilist)) {
			for (TdsAccountParam para : ilist) {
				TdsAccountParamVo vo = ObjUtils.copyTo(para, TdsAccountParamVo.class);
				rlist.add(vo);
			}
		}
		return rlist;
	}

	public Boolean copyAccount(String tdsAlias, String newTdsAlias) {
		TdsAccountConf conf = getAccountConf(tdsAlias);
		if (conf != null) {
			conf.setId(TMUID.getUID());
			conf.setTdsalias(newTdsAlias);
		}

		List<TdsAccountParam> addPlist = null;
		List<TdsAccountParam> plist = getAccountParamList(tdsAlias);
		if (StringUtils.isNotEmpty(plist)) {

			Where wherei = Where.create();
			wherei.eq(TdsinPara::getTdsalias, newTdsAlias);
			List<TdsinPara> ilist = srv.queryList(TdsinPara.class, wherei, null);
			Map<String, TdsinPara> imap = new HashMap<String, TdsinPara>();
			for (TdsinPara tip : ilist) {
				imap.put(tip.getParaAlias(), tip);
			}

			addPlist = new ArrayList<TdsAccountParam>();
			for (TdsAccountParam obj : plist) {
				obj.setId(TMUID.getUID());
				obj.setTdsalias(newTdsAlias);

				TdsinPara inp = imap.get(obj.getOldParamAlias());
				if (inp != null && StringUtils.isNotEmpty(obj.getOldParamAlias())) {
					obj.setOldParamId(inp.getId());
				}

				addPlist.add(obj);
			}
		}

		List<TdsAccountOutparam> addOlist = null;
		List<TdsAccountOutparam> olist = getOutList(tdsAlias);
		if (StringUtils.isNotEmpty(olist)) {
			addOlist = new ArrayList<TdsAccountOutparam>();
			for (TdsAccountOutparam obj : olist) {
				obj.setId(TMUID.getUID());
				obj.setTdsalias(newTdsAlias);
				addOlist.add(obj);
			}
		}

		List<TdsAccountMeter> addMlist = null;
		List<TdsAccountMeter> mlist = getAccountMeterByTdsalias(tdsAlias);
		if (StringUtils.isNotEmpty(mlist)) {
			addMlist = new ArrayList<TdsAccountMeter>();
			for (TdsAccountMeter obj : mlist) {
				obj.setId(TMUID.getUID());
				obj.setTdsalias(newTdsAlias);
				addMlist.add(obj);
			}
		}

		TdsAccountTime timeConf = getTimeConf(tdsAlias);
		if (timeConf != null) {
			timeConf.setId(TMUID.getUID());
			timeConf.setTdsalias(newTdsAlias);
		}

		// 添加
		if (conf != null) {
			srv.insert(conf);
		}
		if (timeConf != null) {
			srv.insert(timeConf);
		}
		if (addPlist != null) {
			srv.insertBatch(addPlist);
		}
		if (addOlist != null) {
			srv.insertBatch(addOlist);
		}
		if (addMlist != null) {
			srv.insertBatch(addMlist);
		}

		return true;
	}

	@Override
	public List<Map> getAccountSaveData(String tabName, String dsAlias, Long startDate, Long endDate) {
		Criteria criteria = new Criteria();
		criteria.and("dsAlias").is(dsAlias);
		criteria.and("tmused").is(1);
		if (startDate != null && endDate != null) {
			criteria.and("sjlong").gte(startDate).lte(endDate);// 大于等于
		}

		Query query = new Query(criteria);
		return mongoDBServ.find(query, Map.class, tabName);
	}

	@Override
	public List<Map> getAccountSaveData(String tabName, String dsAlias, Long startDate, Long endDate,
			List<String> tagIds) {
		Criteria criteria = new Criteria();
//		criteria.and("dsAlias").is(dsAlias);
//		criteria.and("tmused").is(1);
		criteria.and("col").in(tagIds);
		if (startDate != null && endDate != null) {
			criteria.and("sjlong").gte(startDate).lte(endDate);// 大于等于
		}

		Query query = new Query(criteria);
		return mongoDBServ.find(query, Map.class, tabName);
	}

	// findAllAndRemove 增加删除MongoDB方法
	@Override
	public List<Map> getAccountSaveData(String tabName, String dsAlias, Long startDate, Long endDate, String unitCode,
			String bc, String accountId) {
		Criteria criteria = new Criteria();
		criteria.and("dsAlias").is(dsAlias);
		criteria.and("tmused").is(1);
		if (startDate != null && endDate != null) {
			criteria.and("sjlong").gte(startDate).lte(endDate);// 大于等于
		}
		criteria.and("unitCode").is(unitCode);
		if (StringUtils.isNotEmpty(bc)) {
			criteria.and("bc").is(bc);
		}
		if (StringUtils.isNotEmpty(accountId)) {
			criteria.and("accountId").is(accountId);
		}

		Query query = new Query(criteria);
		return mongoDBServ.find(query, Map.class, tabName);
	}

	@Override
	public List<Map> getAccountCountData(String tabName, String dsAlias, String countId, String rq, String shiftCode,
			List<String> colIdList) {
		Criteria criteria = new Criteria();
		criteria.and("dsAlias").is(dsAlias);
		criteria.and("tmused").is(1);
		criteria.and("rq").is(rq);
		criteria.and("row").is(countId);
		criteria.and("col").in(colIdList);
		if (shiftCode != null) {
			criteria.and("bc").is(shiftCode);// 班次
		}

		Query query = new Query(criteria);
		return mongoDBServ.find(query, Map.class, tabName);
	}

	@Override
	public List<Map> getAccountNewData(String tabName, Integer mode, String rq, String unitCode, String bc,
			String accountId) {
		Criteria criteria = new Criteria();
		criteria.and("tmused").is(1);
		criteria.and("rq").is(rq);
//		criteria.and("mode").is(mode);//1pwl 2lims//因数据源导致表名不同，此处暂不过滤，无意义
		if (StringUtils.isNotEmpty(unitCode)) {
			criteria.and("unitCode").is(unitCode);
		}
		if (StringUtils.isNotEmpty(bc)) {
			criteria.and("bc").is(bc);
		}
		if (StringUtils.isNotEmpty(accountId)) {
			criteria.and("accountId").is(accountId);
		}

		Query query = new Query(criteria);
		return mongoDBServ.find(query, Map.class, tabName);
	}

	public List<Map> getAccountTimeConfirmData(String tabName, String dsAlias, Long startDate, Long endDate,
			String accountId, String unitCode) {
		Criteria criteria = new Criteria();
		criteria.and("dsAlias").is(dsAlias);
		criteria.and("col").is("rowConfirm");
		criteria.and("unitCode").is(unitCode);
		criteria.and("tmused").is(1);
		if (StringUtils.isNotEmpty(accountId)) {
			criteria.and("accountId").is(accountId);
		}
		if (startDate != null && endDate != null) {
			criteria.and("sjlong").gte(startDate).lte(endDate);// 大于等于
		}
		Query query = new Query(criteria);
		return mongoDBServ.find(query, Map.class, tabName);
	}

	@Override
	public List<Map> getOldAccountConfirmData(String tabName, String dsAlias, Long startDate, Long endDate,
			String accountId, String unitCode) {
		Criteria criteria = new Criteria();
		criteria.and("dsAlias").is(dsAlias);
		criteria.and("col").is("rowConfirm");
		criteria.and("unitCode").is(unitCode);
		criteria.and("tmused").is(1);
		criteria.and("bc").exists(false);// 没有班次字段
		if (StringUtils.isNotEmpty(accountId)) {
			criteria.and("accountId").is(accountId);
		}
		if (startDate != null && endDate != null) {
			criteria.and("sjlong").gte(startDate).lte(endDate);// 大于等于
		}
		Query query = new Query(criteria);
		return mongoDBServ.find(query, Map.class, tabName);
	}

	private List<Map> getAccountConfirmData(String tabName, String dsAlias, String accountDate, String accountId,
			String unitCode) {
		Criteria criteria = new Criteria();
		criteria.and("dsAlias").is(dsAlias);
		criteria.and("sj").is(accountDate);
		criteria.and("col").is("rowConfirm");
		criteria.and("unitCode").is(unitCode);
		criteria.and("tmused").is(1);
		if (StringUtils.isNotEmpty(accountId)) {
			criteria.and("accountId").is(accountId);
		}
		Query query = new Query(criteria);
		return mongoDBServ.find(query, Map.class, tabName);
	}

	@Override
	public Map<String, String> rowConfirm(TdsAccountDto param) {

		Boolean flag = true;
		String tdsAlias = param.getTdsAlias();
		String adate = param.getAccountRowDate();
		String formId = param.getFormId();
		String dataId = param.getDataId();
		String infoObj = param.getAccountId();
		String accountId = null;
		String unitCode = null;
		String bc = null;
		String sbsj = null;
		String xbsj = null;
		String rq = null;
		String sendUnitCode = null;
		String formDataId = null;
		String activeId = null;
		if (StringUtils.isNotEmpty(infoObj)) {
			List<String> slist = Coms.StrToList(infoObj, ";");
			Map<String, String> _map = new HashMap<String, String>();
			for (String s : slist) {
				List<String> slist2 = Coms.StrToList(s, "@");
				String v = slist2.size() > 1 ? slist2.get(1) : null;
				if ("null".equals(v)) {
					v = null;
				}
				if("sj".equals(slist2.get(0))) {
					String[] ss = v.split(",");
					_map.put("sbsj", ss[0]);
					_map.put("xbsj", ss[1]);
				}else {
					_map.put(slist2.get(0), v);
				}
			}
			accountId = _map.get("accountId");
			unitCode = _map.get("unitCode");
			bc = _map.get("bc");
			sbsj = _map.get("sbsj");
			xbsj = _map.get("xbsj");
			rq = _map.get("rq");
			sendUnitCode = _map.get("sendUnitCode");
			activeId = _map.get("activeId");
			formDataId = _map.get("formDataId");
		}

		String value = "/";
		String over = "";
		String color = "";
		String err = "";
		TdsAccountConf conf = getAccountConf(tdsAlias);// 配置信息
		SysUser user = SysUserHolder.getCurrentUser();
		Date currTime = DateTimeUtils.getNowDate();
		String nowStr = DateTimeUtils.getNowDateTimeStr();

		String sendrq = sbsj != null && sbsj.length() >= 10 ? sbsj.substring(0, 10) : rq;
		// 新数据确认
		JSONArray confirmArr = null;
		List<Map> newdata = getAccountNewData(accountNewTab + "_confirm_" + tdsAlias, 1, sendrq, unitCode, bc,
				accountId);
		
		//外部接口调用 TODO
//		TdsAccountTime timeConf = getTimeConf(tdsAlias);//时间配置
//		if("mobile".equals(timeConf.getShowMode())) {//如果外部模式，判断活动模式，如果巡检模式，自动转换成时间点模式
//			Map<String, String> confMap = applySrv.getUnitAccountConf(sendUnitCode);
//			if(confMap!=null && "0".equals(confMap.get("mode"))) {
//				timeConf.setShowMode("tz");
//			}
//			//调用外部数据确认事件
//			AcctobjInputVo vo = new AcctobjInputVo();//外部对象
//			vo.setRowFlag(1);
//			vo.setInputTime(DateTimeUtils.parseDateTime(adate));
//			vo.setBaId(sendUnitCode);
//			vo.setAcctobjId(unitCode);
////			vo.setBaName(sendUnitName);
//			vo.setBcdm(bc);
////			vo.setBcmc(shiftName);
//			vo.setSbsj(DateTimeUtils.parseDateTime(sbsj));
//			vo.setXbsj(DateTimeUtils.parseDateTime(xbsj));
//			vo.setTaskId(activeId);
//			
//			List<AcctobjInputVo> clist = new ArrayList<AcctobjInputVo>();
//			clist.add(vo);
//			
//			AccountSaveDto obj = new AccountSaveDto();
//			obj.setType("mobile".equals(timeConf.getShowMode())?"collectionPoint":"routingInspection");//"mobile".equals(timeConf.getShowMode())?"collectionPoint":"routingInspection"
//			obj.setOperType("confirm");
//			obj.setFormDataId(formDataId);
//			obj.setCollectionPointInputData(clist);
//			AccountSaveEvent event = new AccountSaveEvent(obj);
//			context.publishEvent(event);
//		}
		
		
		if (StringUtils.isNotEmpty(newdata)) {
			List confirm = (List) newdata.get(0).get("info");
			if (confirm != null) {
				confirmArr = new JSONArray(confirm);
			} else {
				confirmArr = new JSONArray();
			}
			Boolean op = false;
			for (int i = 0, il = confirmArr.size(); i < il; i++) {
				JSONObject obj = confirmArr.getJSONObject(i);
				if (adate.equals(obj.getString("sj"))) {
					obj.put("val", nowStr);
					obj.put("updTime", currTime);
					obj.put("updUserId", user.getId());
					obj.put("updUserName", user.getRealName());
					if (true == obj.getBooleanValue("rowConfirmOver")) {
						over = "1";
						color = "red";
					} else {
						over = "0";
						color = "#1B9A49";
					}
					value = obj.getString("creUserName") + "(" + obj.getString("valstr") + ")";
					op = true;
					break;
				}
			}
			if (!op) {
				JSONObject aobj = new JSONObject();
				aobj.put("col", "rowConfirm");// 列名
				aobj.put("sj", adate);

				try {
					aobj.put("sjlong", DateTimeUtils.parseDateTime(adate).getTime());
				} catch (Exception e) {
					aobj.put("sjlong", 0);
				}
				aobj.put("valstr", nowStr);
				aobj.put("creTime", currTime);
				aobj.put("creUserId", user.getId());
				aobj.put("creUserName", user.getRealName());
				aobj.put("updTime", null);
				aobj.put("updUserId", null);

				Integer bound = conf.getConfirmRound();
				if (bound == null || bound <= 0) {
					aobj.put("rowConfirmOver", false);// 确认超时

				} else {
					// 计算确认时间是否在行时间范围内
					Date sd = DateTimeUtils.doMinute(DateTimeUtils.parseDateTime(adate), -bound);
					Date ed = DateTimeUtils.doMinute(DateTimeUtils.parseDateTime(adate), bound);
					if (DateTimeUtils.bjDate(sd, ed, currTime)) {
						aobj.put("rowConfirmOver", false);// 确认超时
					} else {
						aobj.put("rowConfirmOver", true);// 确认超时
					}
				}

				confirmArr.add(aobj);

				JSONObject tobj = new JSONObject(newdata.get(0));
				tobj.put("info", confirmArr);
//				JSONArray alist = new JSONArray();
//				alist.add(tobj);
				mongoDBServ.updateById(accountNewTab + "_confirm_" + tdsAlias, tobj);

				if (true == aobj.getBooleanValue("rowConfirmOver")) {
					over = "1";
					color = "red";
				} else {
					over = "0";
					color = "#1B9A49";
				}
				value = aobj.getString("creUserName") + "(" + aobj.getString("valstr") + ")";
			}
		} else {//兼容历史数据处理

			List<Map> list = getAccountConfirmData(accountTabName + "_" + tdsAlias, tdsAlias, adate, accountId,
					unitCode);

			if (list != null && list.size() > 0) {
				JSONArray updateData = new JSONArray();
				JSONObject aobj = JSONObject.parseObject(JSONObject.toJSONString(list.get(0)));
				aobj.put("val", nowStr);
				aobj.put("updTime", currTime);
				aobj.put("updUserId", user.getId());
				aobj.put("updUserName", user.getRealName());

				updateData.add(aobj);
				if (updateData.size() > 0) {// 修改
					mongoDBServ.updateBatchById(accountTabName + "_" + tdsAlias, updateData);
				}

				if (true == aobj.getBooleanValue("rowConfirmOver")) {
					over = "1";
					color = "red";
				} else {
					over = "0";
					color = "#1B9A49";
				}
				value = aobj.getString("creUserName") + "(" + aobj.getString("valstr") + ")";
				// 理论上只能确认一次
			} else {

				JSONArray insertData = new JSONArray();
				JSONObject aobj = new JSONObject();
				aobj.put("_id", TMUID.getUID());
				aobj.put("dsAlias", tdsAlias);
				aobj.put("formId", formId);
				aobj.put("dataId", dataId);
				aobj.put("col", "rowConfirm");// 列名
				aobj.put("tag", "");
				aobj.put("dateType", "rowConfirm");
				aobj.put("sj", adate);
				aobj.put("sjlong", DateTimeUtils.parseDateTime(adate).getTime());
				aobj.put("val", nowStr);
				aobj.put("valstr", nowStr);
				aobj.put("creTime", currTime);
				aobj.put("creUserId", user.getId());
				aobj.put("creUserName", user.getRealName());
				aobj.put("updTime", null);
				aobj.put("updUserId", null);
				aobj.put("updUserName", null);
				aobj.put("additionVal", null);// 附加值
				aobj.put("edit", false);
				aobj.put("tmused", 1);
				aobj.put("accountId", accountId);
				aobj.put("unitCode", unitCode);
				aobj.put("bc", bc);

				long secDiff = DateTimeUtils.diffSecond(DateTimeUtils.parseDateTime(adate), currTime);
				aobj.put("confirmDiff", secDiff);// 确认差（秒）

				Integer bound = conf.getConfirmRound();
				if (bound == null || bound <= 0) {
					aobj.put("rowConfirmBound", 0);// 确认范围（分钟）
					aobj.put("rowConfirmOver", false);// 确认超时

				} else {
					aobj.put("rowConfirmBound", bound);// 确认范围（分钟）
					// 计算确认时间是否在行时间范围内
					Date sd = DateTimeUtils.doMinute(DateTimeUtils.parseDateTime(adate), -bound);
					Date ed = DateTimeUtils.doMinute(DateTimeUtils.parseDateTime(adate), bound);
					if (DateTimeUtils.bjDate(sd, ed, currTime)) {
						aobj.put("rowConfirmOver", false);// 确认超时
					} else {
						aobj.put("rowConfirmOver", true);// 确认超时
					}
				}

				if (true == aobj.getBooleanValue("rowConfirmOver")) {
					over = "1";
					color = "red";
				} else {
					over = "0";
					color = "#1B9A49";
				}
				value = aobj.getString("creUserName") + "(" + aobj.getString("valstr") + ")";

				insertData.add(aobj);

				if (insertData.size() > 0) {// 添加
					try {
						mongoDBServ.insertBatch(accountTabName + "_" + tdsAlias, insertData);
//					//关系数据库写入确认数据 暂时屏蔽 TODO
//					DataAccountTools dat = new DataAccountTools();
//					dat.writeConfirmData(insertData);
					} catch (Exception e) {
						err = e.getMessage();
					}
				}
			}
		}

		Map<String, String> rmap = new HashMap<String, String>();
		rmap.put("value", value);
		rmap.put("over", over);
		rmap.put("err", err);
		rmap.put("color", color);

		return rmap;
	}

	public Boolean saveAccountTag(TdsAccountDto param) {

		List<TdsAccountTagVo> tagList = param.getTagList();
		// save
		List<TdsAccountTag> updList = new ArrayList<TdsAccountTag>();
		for (TdsAccountTagVo obj : tagList) {
			updList.add(ObjUtils.copyTo(obj, TdsAccountTag.class));
		}
		return 1 == srv.updateByIdBatch(updList);
	}

	@Override
	public List<TdsAccountTagVo> getUnitTagList(TdsAccountDto param) {

		List<TdsAccountTagVo> rlist = new ArrayList<TdsAccountTagVo>();

		String tdsAlias = param.getTdsAlias();// 数据源别名
		String confCode = param.getConfCode();// 顶部核算对象

		Integer mode = param.getMode();// 1核算对象 2机构
//		String unitCode = param.getUnitCode();//考核单元代码
		String orgCode = param.getOrgCode();// 机构代码
		String useStatus = param.getUseStatus();// 使用状态 1显示 2不显示
		String tagName = param.getTagName();// 仪表名称

//		String ucode = new Integer(1).equals(mode)?confCode:unitCode;

		// 获取配置编码，用于查询台账设置仪表信息
		if (new Integer(2).equals(mode)) {
			confCode = orgCode;
		}

		Where wherev = Where.create();
		wherev.eq(TdsAccountTagVersion::getTmused, 1);
		wherev.eq(TdsAccountTagVersion::getUnitcode, confCode);
		Order orderv = Order.create();
		orderv.orderByDesc(TdsAccountTagVersion::getRq);
		List<TdsAccountTagVersion> vlist = srv.queryList(TdsAccountTagVersion.class, wherev, orderv);

		String vid = "";
		String version = "";
		Date nd = DateTimeUtils.getND();
		// 从大到小日期
		if (StringUtils.isNotEmpty(vlist)) {
			for (TdsAccountTagVersion ver : vlist) {
				String t = ver.getRq();
				Date dt = DateTimeUtils.parseD(t, DateTimeUtils.DateFormat_YMD);
				if (DateTimeUtils.bjDate(dt, nd) <= 0) {
					vid = ver.getId();
					version = DateTimeUtils.formatDate(dt, DateTimeUtils.DateFormat_YMD);
					break;
				}
			}
			if ("".equals(version)) {
				if (StringUtils.isNotEmpty(vlist)) {
					vid = vlist.get(0).getId();
					version = vlist.get(0).getRq();
				}
			}

			Where where = Where.create();
			where.eq(TdsAccountTag::getTmused, 1);
			where.eq(TdsAccountTag::getUnitCode, confCode);
			where.eq(TdsAccountTag::getVermark, vid);
			if (StringUtils.isNotEmpty(tagName)) {// 根据仪表位号进行查询
				where.like(TdsAccountTag::getTagname, tagName);
			}
			Order order = Order.create();
			order.order(TdsAccountTag::getId);
			List<TdsAccountTag> taglist = srv.queryList(TdsAccountTag.class, where, order);

			List<TdsAccountMeter> meterList = getAccountMeterList(tdsAlias, confCode, null);

			List<TdsAccountTagVo> dlist = new ArrayList<TdsAccountTagVo>();
			for (TdsAccountTag tag : taglist) {
				TdsAccountTagVo vo = ObjUtils.copyTo(tag, TdsAccountTagVo.class);
				vo.setTagType(tag.getCtype());
				dlist.add(vo);
			}

			if (meterList.size() > 0) {
				List<String> clist = new ArrayList<String>();
				meterList.forEach(item -> {
					clist.add(item.getTagid());
				});
				dlist.forEach(item -> {
					item.setShowMark(clist.contains(item.getTagid()));
				});
				if ("1".equals(useStatus)) {// 显示
					for (TdsAccountTagVo item : dlist) {
						if (item.getShowMark()) {
							rlist.add(item);
						}
					}
				} else if ("2".equals(useStatus)) {// 不显示
					for (TdsAccountTagVo item : dlist) {
						if (!item.getShowMark()) {
							rlist.add(item);
						}
					}
				} else {
					rlist = dlist;
				}
			} else {
				dlist.forEach(item -> {
					item.setShowMark(true);
				});
				rlist = dlist;
			}
		}

		return rlist;
	}

	/**
	 * @category 获取核算单元所有日期版本
	 * @param unitCode
	 * @return
	 */
	public List<TdsAccountTagVersion> getAccountTagVersionList(String unitCode) {
		Where where = Where.create();
		where.eq(TdsAccountTagVersion::getTmused, 1);
		where.eq(TdsAccountTagVersion::getUnitcode, unitCode);
		Order order = Order.create();
		order.order(TdsAccountTagVersion::getRq);
		List<TdsAccountTagVersion> rlist = srv.queryList(TdsAccountTagVersion.class, where, order);
		return rlist;

	}

	/**
	 * @category 获取多核算单元所有日期版本
	 * @param unitCode
	 * @return
	 */
	public List<TdsAccountTagVersion> getManyAccountTagVersionList(List<String> unitCodeList) {
		Where where = Where.create();
		where.eq(TdsAccountTagVersion::getTmused, 1);
		where.in(TdsAccountTagVersion::getUnitcode, unitCodeList.toArray());
		Order order = Order.create();
		order.order(TdsAccountTagVersion::getUnitcode);
		order.order(TdsAccountTagVersion::getRq);
		List<TdsAccountTagVersion> rlist = srv.queryList(TdsAccountTagVersion.class, where, order);
		return rlist;

	}

	/**
	 * @category 获取指定日期相关的版本
	 * @param unitCode
	 * @param rq
	 * @return
	 */
	@Override
	public TdsAccountTagVersion getAccountTagUseVersion(String unitCode, String rq) {
		List<TdsAccountTagVersion> vlist = getAccountTagVersionList(unitCode);
		if (StringUtils.isEmpty(vlist)) {
			return null;
		} else if (vlist.size() == 1) {
			return vlist.get(0);
		} else {
			TdsAccountTagVersion robj = null;
			for (TdsAccountTagVersion obj : vlist) {
				if (DateTimeUtils.bjDate(DateTimeUtils.parseD(rq, DateTimeUtils.DateFormat_YMD),
						DateTimeUtils.parseD(obj.getRq(), DateTimeUtils.DateFormat_YMD)) >= 0) {
					robj = obj;
				} else {
					break;
				}
			}
			if (robj == null) {
				robj = vlist.get(0);
			}
			return robj;
		}
	}

	/**
	 * @category 获取核算单元默认仪表信息
	 * @param unitCode
	 * @param rq
	 * @return
	 */
	@Override
	public List<TdsAccountMeter> getDefaultAccountUnitTagList(String unitCode, String rq) {
		List<TdsAccountMeter> rlist = new ArrayList<TdsAccountMeter>();
		TdsAccountTagVersion ver = getAccountTagUseVersion(unitCode, rq);
		if (ver != null) {
			Where where = Where.create();
			where.eq(TdsAccountTag::getTmused, 1);
			where.eq(TdsAccountTag::getUnitCode, unitCode);
			where.eq(TdsAccountTag::getVermark, ver.getId());
			Order order = Order.create();
			order.order(TdsAccountTag::getTmsort);
			List<TdsAccountTag> list = srv.queryData(TdsAccountTag.class, where, order, null);
			for (TdsAccountTag obj : list) {
				TdsAccountMeter vo = ObjUtils.copyTo(obj, TdsAccountMeter.class);
				vo.setShowName(obj.getTagname());
				vo.setEditMark(0);
				vo.setAlign("right");
				rlist.add(vo);
			}
		}

		return rlist;
	}

	@Override
	public List<TdsAccountMeter> getDefaultAccountUnitTagList(String unitCode, String rq, String tagType,
			List<String> tagIdList) {
		List<TdsAccountMeter> rlist = new ArrayList<TdsAccountMeter>();
		TdsAccountTagVersion ver = getAccountTagUseVersion(unitCode, rq);
		if (ver != null) {
			List<TdsAccountTag> list = new ArrayList<TdsAccountTag>();

			if (StringUtils.isNotEmpty(tagIdList) && tagIdList.size() > 500) {
				List<String> tids = new ArrayList<String>();
				for (String tagId : tagIdList) {
					tids.add(tagId);
					if (tids.size() >= 500) {
						Where where = Where.create();
						where.eq(TdsAccountTag::getTmused, 1);
						where.eq(TdsAccountTag::getUnitCode, unitCode);
						where.eq(TdsAccountTag::getVermark, ver.getId());
						if (StringUtils.isNotEmpty(tagType) && Coms.judgeLong(tagType)) {
							where.eq(TdsAccountTag::getCtype, Integer.parseInt(tagType));
						}
						where.in(TdsAccountTag::getTagid, tids.toArray());
						list.addAll(srv.queryData(TdsAccountTag.class, where, null, null));
						tids.clear();
					}
				}
				if (tids.size() > 0) {
					Where where = Where.create();
					where.eq(TdsAccountTag::getTmused, 1);
					where.eq(TdsAccountTag::getUnitCode, unitCode);
					where.eq(TdsAccountTag::getVermark, ver.getId());
					if (StringUtils.isNotEmpty(tagType) && Coms.judgeLong(tagType)) {
						where.eq(TdsAccountTag::getCtype, Integer.parseInt(tagType));
					}
					where.in(TdsAccountTag::getTagid, tids.toArray());
					list.addAll(srv.queryData(TdsAccountTag.class, where, null, null));
				}

				Collections.sort(list, (t1, t2) -> t1.getTmsort() - t2.getTmsort());// 重新排序

			} else {
				Where where = Where.create();
				where.eq(TdsAccountTag::getTmused, 1);
				where.eq(TdsAccountTag::getUnitCode, unitCode);
				where.eq(TdsAccountTag::getVermark, ver.getId());
				if (StringUtils.isNotEmpty(tagType) && Coms.judgeLong(tagType)) {
					where.eq(TdsAccountTag::getCtype, Integer.parseInt(tagType));
				}
				if (StringUtils.isNotEmpty(tagIdList)) {
					where.in(TdsAccountTag::getTagid, tagIdList.toArray());
				}
				Order order = Order.create();
				order.order(TdsAccountTag::getTmsort);
				list = srv.queryData(TdsAccountTag.class, where, order, null);
			}

//			Where where = Where.create();
//			where.eq(TdsAccountTag::getTmused, 1);
//			where.eq(TdsAccountTag::getUnitCode, unitCode);
//			where.eq(TdsAccountTag::getVermark, ver.getId());
//			if(StringUtils.isNotEmpty(tagType) && Coms.judgeLong(tagType)) {
//				where.eq(TdsAccountTag::getCtype, Integer.parseInt(tagType));
//			}
//			if(StringUtils.isNotEmpty(tagIdList)) {
//				where.in(TdsAccountTag::getTagid, tagIdList.t);
//			}
////			Order order = Order.create();
////			order.order(TdsAccountTagVersion::getTmsort);
//			List<TdsAccountTag> list = srv.queryList(TdsAccountTag.class, where, null);
			for (TdsAccountTag obj : list) {
				TdsAccountMeter vo = ObjUtils.copyTo(obj, TdsAccountMeter.class);
				vo.setShowName(obj.getTagname());
//				vo.setEditMark(0);
				vo.setAlign("right");
				vo.setTagType(obj.getCtype());
				rlist.add(vo);
			}
		}

		return rlist;
	}

	@Override
	public List<TdsAccountMeter> getDefaultAccountUnitTagList(String unitCode, String rq, String tagType) {
		List<TdsAccountMeter> rlist = new ArrayList<TdsAccountMeter>();
		TdsAccountTagVersion ver = getAccountTagUseVersion(unitCode, rq);
		if (ver != null) {
			Where where = Where.create();
			where.eq(TdsAccountTag::getTmused, 1);
			where.eq(TdsAccountTag::getUnitCode, unitCode);
			where.eq(TdsAccountTag::getVermark, ver.getId());
			if (StringUtils.isNotEmpty(tagType) && Coms.judgeLong(tagType)) {
				where.eq(TdsAccountTag::getCtype, Integer.parseInt(tagType));
			}
			Order order = Order.create();
			order.order(TdsAccountTag::getTmsort);
			List<TdsAccountTag> list = srv.queryData(TdsAccountTag.class, where, order, null);
			for (TdsAccountTag obj : list) {
				TdsAccountMeter vo = ObjUtils.copyTo(obj, TdsAccountMeter.class);
				vo.setShowName(obj.getTagname());
//				vo.setEditMark(0);
				vo.setAlign("right");
				vo.setTagType(obj.getCtype());
				rlist.add(vo);
			}
		}

		return rlist;
	}

	@Override
	public Boolean reGetAccountData(TdsAccountDto param) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<TdsAccountFormMeter> getFormMeterList(String formCode, String unitCode, String rq) {
		return getFormMeterList(formCode, unitCode, rq, null);
	}

	@Override
	public List<TdsAccountFormMeter> getFormMeterList(String formCode, String unitCode, String rq, String tagType) {
		Where where = Where.create();
		where.eq(TdsAccountFormMeter::getTmused, 1);
		where.eq(TdsAccountFormMeter::getUnitCode, unitCode);
		where.eq(TdsAccountFormMeter::getAccountid, formCode);
		where.eq(TdsAccountFormMeter::getTypeCode, 1);
		where.eq(TdsAccountFormMeter::getTagType, "3".equals(tagType) ? 3 : 2);
		List<TdsAccountFormMeter> list = srv.queryList(TdsAccountFormMeter.class, where, null);

		if (StringUtils.isNotEmpty(list)) {
			return list;
		} else {
			// 判断其他相同核算对象自定义台账中是否设置仪表，设置了，去除已设置的仪表
			// 条件中需去除自定义台账同核算对象已设置的仪表
			List<TdsAccountFormMeter> haveMeterList = formSrv.getHaveUnitMeterList(unitCode, formCode, tagType);
			List<String> haveTagList = new ArrayList<String>();
			if (StringUtils.isNotEmpty(haveMeterList)) {
				for (TdsAccountFormMeter meter : haveMeterList) {
					haveTagList.add(meter.getTagid());
				}
			}
			if (StringUtils.isNotEmpty(haveTagList)) {
				List<TdsAccountTag> tagList = formSrv.getUnitTagList(rq, unitCode, formCode, haveTagList, tagType,
						null);
				if (StringUtils.isNotEmpty(tagList)) {

					List<TdsAccountFormMeter> rlist = new ArrayList<TdsAccountFormMeter>();
					for (TdsAccountTag tag : tagList) {
						TdsAccountFormMeter obj = ObjUtils.copyTo(tag, TdsAccountFormMeter.class);
						obj.setShowName(tag.getTagname());
						rlist.add(obj);
					}

					return rlist;
				}
			}
		}

		return null;
	}
	@Override
	public Map<String, String> getTagIdCodeMap(List<String> idList) {

		Map<String, String> map = new HashMap<String, String>();
		if (StringUtils.isEmpty(idList)) {
			return map;
		}

		Where where = Where.create();
		where.in(TdsAccountTag::getTagid, idList.toArray());
		List<TdsAccountTag> list = srv.queryList(TdsAccountTag.class, where, null);
		if (StringUtils.isNotEmpty(list)) {
			for (TdsAccountTag obj : list) {
				map.put(obj.getTagid(), obj.getDatasource());
			}
		}
		return map;
	}
	
	@Override
	public Map<String, TdsAccountTag> getTagIdObjMap(List<String> idList) {
		
		Map<String, TdsAccountTag> map = new HashMap<String, TdsAccountTag>();
		if (StringUtils.isEmpty(idList)) {
			return map;
		}
		
		Where where = Where.create();
		where.in(TdsAccountTag::getTagid, idList.toArray());
		Order order = Order.create().orderByDesc(TdsAccountTag::getRq);
		List<TdsAccountTag> list = srv.queryList(TdsAccountTag.class, where, order);
		if (StringUtils.isNotEmpty(list)) {
			for (TdsAccountTag obj : list) {
				if(!map.containsKey(obj.getTagid())) {
					map.put(obj.getTagid(), obj);
				}
			}
		}
		return map;
	}

	@Override
	public void calculateCount(String dsAlias, List<String> tagIdList, String sbsj, String xbsj, String rq,
			String shiftCode, Map<String, String> tagIdCodeMap) {
		if (rq != null) {

			Date currTime = DateTimeUtils.getNowDate();
			SysUser user = SysUserHolder.getCurrentUser();
			// 检索数据源相关仪表是否设置进行汇总
			List<TdsAccountCountConfVo> confList = getCountConfList(dsAlias, tagIdList);
			// 获取配置计算公式，对对应仪表列进行汇总，根据上下班时间判断，日、月根据范围进行统计
			// MongoDB使用新表保存统计信息，保存日期、仪表id，班次、上下班时间，用于统计，日、月统计无问题，班次数据算法（统计数据中日期数据且下班时间小于当前上班时间的数据+仪表数据中当前上下班数据汇总）

			String maxType = "day";// 目前只有日累计、月累计
			List<String> applyColList = new ArrayList<String>();
			for (TdsAccountCountConfVo conf : confList) {
				String fm = conf.getCountFormula();
				if (fm.indexOf("月累计") != -1) {
					maxType = "mon";
				}
				List<TdsAccountCountCol> colList = conf.getColList();
				if (StringUtils.isNotEmpty(colList)) {
					for (TdsAccountCountCol col : colList) {
						if (!applyColList.contains(col.getColMark())) {
							applyColList.add(col.getColMark());
						}
					}
				}
			}
			// 获取仪表相关数据，根据统计内容进行判断，如果有月，历史日期从1号开始，如果无月有日，只取日期，且时间小于下班时间
			String tjrq = rq;
			if ("mon".equals(maxType)) {
				tjrq = rq.substring(0, 7) + "-01";
			}
			Long rqlong = DateTimeUtils.parseDate(tjrq).getTime();
			Long endlong = xbsj == null ? DateTimeUtils.doDate(DateTimeUtils.parseDate(rq), 1).getTime()
					: DateTimeUtils.parseDateTime(xbsj).getTime();
			List<Map> historyData = getAccountColData(dsAlias, rqlong, endlong, applyColList);
			Map<String, List<Map>> cdatamap = getColDate(historyData);// 整理历史数据

			// 读取历史统计数据，如果存在，进行更新，没有数据进行添加
			JSONArray insertData = new JSONArray();
			JSONArray updateData = new JSONArray();

			Map<String, Map> countMap = new HashMap<String, Map>();
			for (TdsAccountCountConfVo conf : confList) {// 循环统计行
				List<TdsAccountCountCol> clist = conf.getColList();
				if (StringUtils.isNotEmpty(clist)) {// 有统计列信息
					List<String> colIdList = new ArrayList<String>();
					for (TdsAccountCountCol cc : clist) {
						colIdList.add(cc.getColMark());
					}
					List<Map> jgmap = getAccountCountData(this.accountTabName + "_" + dsAlias + "_count", dsAlias,
							conf.getId(), rq, shiftCode, colIdList);
					if (jgmap != null) {
						for (Map map : jgmap) {
							String col = String.valueOf(map.get("col"));// 仪表相当于COLID
							String row = String.valueOf(map.get("row"));// 仪表相当于COLID
							String valstr = String.valueOf(map.get("valstr"));// 结果
							valstr = valstr == null ? "" : valstr;
							countMap.put(row + "_" + col, map);
						}
					}

					for (TdsAccountCountCol cc : clist) {// 循环统计列，进行统计
						List<Map> cmap = cdatamap.get(cc.getColMark());// 列历史数据
						String fm = conf.getCountFormula();

						Map havemap = countMap.get(conf.getId() + "_" + cc.getColMark());
						if (cmap != null && cmap.size() > 0) {
							Double jg = null;
							if (fm.indexOf("日累计") != -1) {
								jg = calCount("day", cmap, rq, xbsj);
							} else if (fm.indexOf("月累计") != -1) {
								jg = calCount("mon", cmap, rq, xbsj);
							}

							if (havemap != null) {
								JSONObject uobj = new JSONObject(havemap);
								uobj.put("val", jg == null ? "0" : jg);
								uobj.put("valstr", jg == null ? "" : Coms.formatAllNumber(jg, null));
								uobj.put("updTime", currTime);
								uobj.put("updUserId", user.getId());
								uobj.put("updUserName", user.getRealName());
								updateData.add(uobj);
							} else {
								JSONObject aobj = new JSONObject();
								aobj.put("_id", TMUID.getUID());
								aobj.put("dsAlias", dsAlias);
								aobj.put("row", conf.getId());
								aobj.put("col", cc.getColMark());// 列名
								aobj.put("tag", tagIdCodeMap == null ? "" : tagIdCodeMap.get(cc.getColMark()));
								aobj.put("rq", rq);
								aobj.put("rqlong", rq == null ? null : DateTimeUtils.parseDate(rq).getTime());
								aobj.put("bc", shiftCode);// 班次
								aobj.put("sbsj",
										StringUtils.isEmpty(sbsj) ? null : DateTimeUtils.parseDate(sbsj).getTime());// 上班时间
								aobj.put("xbsj",
										StringUtils.isEmpty(xbsj) ? null : DateTimeUtils.parseDate(xbsj).getTime());// 下班时间
								aobj.put("sbsjstr", sbsj);// 上班时间
								aobj.put("xbsjstr", xbsj);// 下班时间
								aobj.put("val", jg);
								aobj.put("valstr", jg == null ? "" : String.valueOf(jg));
								aobj.put("creTime", currTime);
								aobj.put("creUserId", user.getId());
								aobj.put("creUserName", user.getRealName());
								aobj.put("updTime", null);
								aobj.put("updUserId", null);
								aobj.put("updUserName", null);
								aobj.put("tmused", 1);
								insertData.add(aobj);
							}

						} else {
							if (havemap != null) {
								JSONObject uobj = new JSONObject(havemap);
								uobj.put("val", "0");
								uobj.put("valstr", "");
								uobj.put("updTime", currTime);
								uobj.put("updUserId", user.getId());
								uobj.put("updUserName", user.getRealName());
								updateData.add(uobj);
							}
						}
					}

				}
			}

			if (insertData.size() > 0) {// 添加
				mongoDBServ.insertBatch(accountTabName + "_" + dsAlias + "_count", insertData);
			}

			if (updateData.size() > 0) {// 修改
				mongoDBServ.updateBatchById(accountTabName + "_" + dsAlias + "_count", updateData);
			}
		}

	}

	// 日累计
	private Double calCount(String calMark, List<Map> cmap, String rq, String xbsj) {
		Double jg = null;
		if ("day".equals(calMark)) {
			Double td = 0d;
			Long rql = DateTimeUtils.parseDate(rq).getTime();
			Long xbsjl = StringUtils.isNotEmpty(xbsj) ? DateTimeUtils.parseDateTime(xbsj).getTime() : 0l;
			for (Map map : cmap) {
				Long rqlong = null;
				Object rqobj = map.get("rqlong");
				if (rqobj != null) {
					rqlong = Long.parseLong(String.valueOf(rqobj));
				}
				Long sjlong = Long.parseLong(String.valueOf(map.get("sjlong")));
				if (rqlong != null) {
					if (StringUtils.isNotEmpty(xbsj)) {
						if (rql.longValue() == rqlong.longValue() && xbsjl.longValue() >= sjlong.longValue()) {
							Double vd = Double.parseDouble(String.valueOf(map.get("valstr")));
							td += vd;
						}
					}
				}
			}
			if (td != 0) {
				jg = td;
			}
		} else if ("mon".equals(calMark)) {
			Double td = 0d;
			Long rql = DateTimeUtils.parseDate(rq.substring(0, 7) + "-01").getTime();
			Long xbsjl = StringUtils.isNotEmpty(xbsj) ? DateTimeUtils.parseDateTime(xbsj).getTime() : 0l;
			for (Map map : cmap) {
				Long rqlong = null;
				Object rqobj = map.get("rqlong");
				if (rqobj != null) {
					rqlong = Long.parseLong(String.valueOf(rqobj));
				}
				Long sjlong = Long.parseLong(String.valueOf(map.get("sjlong")));
				if (rqlong != null) {
					if (StringUtils.isNotEmpty(xbsj)) {
						if (rqlong.longValue() >= rql.longValue() && xbsjl.longValue() >= sjlong.longValue()) {
							Double vd = Double.parseDouble(String.valueOf(map.get("valstr")));
							td += vd;
						}
					}
				}
			}
			if (td != 0) {
				jg = td;
			}
		}
		return jg;
	}

	// 整理仪表历史数据
	private Map<String, List<Map>> getColDate(List<Map> map) {
		Map<String, List<Map>> rmap = new HashMap<String, List<Map>>();
		if (map != null) {
			for (Map _map : map) {
				String valstr = String.valueOf(_map.get("valstr"));
				if (Coms.judgeDouble(valstr)) {
					String col = String.valueOf(_map.get("col"));// 列ID
					List<Map> dmap = rmap.get(col);
					if (dmap != null) {
						dmap.add(_map);
					} else {
						dmap = new ArrayList<Map>();
						dmap.add(_map);
						rmap.put(col, dmap);
					}

//				Long sjlong = Long.parseLong(String.valueOf(_map.get("sjlong")));
//				Double val = Double.parseDouble(valstr);
//				
//				Map<Long, Double> dmap = rmap.get(col);
//				if(dmap!=null) {
//					if(!dmap.containsKey(sjlong)) {//如果有重复的数据(列同一时间，有两个及以上数据)，取第一个
//						dmap.put(sjlong, val);
//					}
//				}else {
//					dmap = new LinkedHashMap<Long, Double>();
//					dmap.put(sjlong, val);
//					rmap.put(col, dmap);
//				}
				}
			}
		}

		return rmap;
	}

	// 读取要统计的仪表数据
	private List<Map> getAccountColData(String dsAlias, Long rqlong, Long endlong, List<String> applyColList) {
		Criteria criteria = new Criteria();
		criteria.and("dsAlias").is(dsAlias);
		criteria.and("tmused").is(1);
		criteria.and("rqlong").gte(rqlong);// 大于等于日期
		criteria.and("sjlong").lte(endlong);// 小于等于上班时间
		criteria.in("tagId").in(applyColList);

		Query query = new Query(criteria);
		return mongoDBServ.find(query, Map.class, accountTabName + "_" + dsAlias);
	}

	/**
	 * @category 获取统计配置
	 * @param dsAlias
	 * @param tagIdList
	 * @return
	 */
	public List<TdsAccountCountConfVo> getCountConfList(String dsAlias, List<String> tagIdList) {
		List<TdsAccountCountConfVo> rlist = new ArrayList<TdsAccountCountConfVo>();

		List<TdsAccountCountCol> colList = new ArrayList<TdsAccountCountCol>();

		if (tagIdList.size() > 500) {
			List<String> tids = new ArrayList<String>();
			for (String tagId : tagIdList) {
				tids.add(tagId);
				if (tids.size() >= 500) {
					Where where = Where.create();
					where.eq(TdsAccountCountCol::getTdsalias, dsAlias);
					where.in(TdsAccountCountCol::getColMark, tids.toArray());
					colList.addAll(srv.queryList(TdsAccountCountCol.class, where, null));
					tids.clear();
				}
			}
			if (tids.size() > 0) {
				Where where = Where.create();
				where.eq(TdsAccountCountCol::getTdsalias, dsAlias);
				where.in(TdsAccountCountCol::getColMark, tids.toArray());
				colList.addAll(srv.queryList(TdsAccountCountCol.class, where, null));
			}
		} else {
			Where where = Where.create();
			where.eq(TdsAccountCountCol::getTdsalias, dsAlias);
			where.in(TdsAccountCountCol::getColMark, tagIdList.toArray());
			colList = srv.queryList(TdsAccountCountCol.class, where, null);
		}

		if (StringUtils.isNotEmpty(colList)) {// 获取所有统计相关列信息，提取统计配置
			List<String> cidList = new ArrayList<String>();
			Map<String, List<TdsAccountCountCol>> tagMap = new HashMap<String, List<TdsAccountCountCol>>();
			for (TdsAccountCountCol obj : colList) {
				if (!cidList.contains(obj.getCountId())) {
					cidList.add(obj.getCountId());
				}
				if (tagMap.containsKey(obj.getCountId())) {
					tagMap.get(obj.getCountId()).add(obj);
				} else {
					List<TdsAccountCountCol> tlist = new ArrayList<TdsAccountCountCol>();
					tlist.add(obj);
					tagMap.put(obj.getCountId(), tlist);
				}
			}
			if (StringUtils.isNotEmpty(cidList)) {// 获取统计配置
				Where wherec = Where.create();
				wherec.eq(TdsAccountCountConf::getTmused, 1);
				wherec.eq(TdsAccountCountConf::getTdsalias, dsAlias);
				wherec.in(TdsAccountCountConf::getId, cidList.toArray());
				Order order = Order.create();
				order.order(TdsAccountCountConf::getTmsort);
				List<TdsAccountCountConf> confList = srv.queryList(TdsAccountCountConf.class, wherec, null);
				if (StringUtils.isNotEmpty(confList)) {// 根据统计配置进行统计并保存

					for (TdsAccountCountConf conf : confList) {
						TdsAccountCountConfVo vo = ObjUtils.copyTo(conf, TdsAccountCountConfVo.class);
						vo.setColList(tagMap.get(conf.getId()));
						rlist.add(vo);
					}
//					
//					
//					JSONArray insertData = new JSONArray();
//					JSONObject aobj = new JSONObject();
//					aobj.put("_id", TMUID.getUID());
//					aobj.put("dsAlias", dsAlias);
//					aobj.put("formId", "");
//					aobj.put("dataId", "");
//					aobj.put("col", "rowConfirm");//列名
//					aobj.put("tag", "");
//					aobj.put("dateType", "rowConfirm");
//					aobj.put("sj", adate);
//					aobj.put("sjlong", DateTimeUtils.parseDateTime(adate).getTime());
//					aobj.put("val", nowStr);
//					aobj.put("valstr", nowStr);
//					aobj.put("creTime", currTime);
//					aobj.put("creUserId", user.getId());
//					aobj.put("creUserName", user.getRealName());
//					aobj.put("updTime", null);
//					aobj.put("updUserId", null);
//					aobj.put("updUserName", null);
//					aobj.put("additionVal", null);//附加值
//					aobj.put("edit", false);
//					aobj.put("tmused", 1);
//					
//					insertData.add(aobj);
//					
//					if (insertData.size() > 0) {// 添加
//						mongoDBServ.insertBatch(accountTabName+"_"+dsAlias, insertData);
//					}
				}
			}
		}
		return rlist;
	}

	/**
	 * @category 获取limis数据
	 * @param dto
	 * @return
	 */
	public HashMap<String, List<LimsDataVo>> getLimsData(LimsDataDto dto) {

		HashMap<String, List<LimsDataVo>> rmap = null;

		String kssj = dto.getKssj();
		String jzsj = dto.getJzsj();
		List<LimsDataVo> limsyb = dto.getLimsyb();

		if (StringUtils.isNotEmpty(kssj) && StringUtils.isNotEmpty(jzsj) && StringUtils.isNotEmpty(limsyb)) {
			rmap = new HashMap<String, List<LimsDataVo>>();

			String sql = "select SAMPLEDDATE, PROCESSUNIT, PRODUCTNAME, SAMPLINGPOINT, ANALYSISNAME, ITEMNAME, FORMATTEDENTRY from PRODLIMSRESULT "
					+ "where (tmused is null or tmused!='t') and SAMPLEDDATE>='" + kssj + "' and SAMPLEDDATE<='" + jzsj
					+ "' order by SAMPLEDDATE";
			// 查询数据
			List<Map<String, Object>> list = srv.queryListMap(sql, null);

			List<String> hlist = new ArrayList<String>();
			Map<String, List<LimsDataVo>> dataMap = new HashMap<String, List<LimsDataVo>>();

			if (StringUtils.isNotEmpty(list)) {
				for (Map<String, Object> map : list) {
					String SAMPLEDDATE = getMapVal(map, "SAMPLEDDATE");// String.valueOf(map.get("SAMPLEDDATE"));
					String PROCESSUNIT = getMapVal(map, "PROCESSUNIT");// String.valueOf(map.get("PROCESSUNIT"));
					String PRODUCTNAME = getMapVal(map, "PRODUCTNAME");// String.valueOf(map.get("PRODUCTNAME"));
					String SAMPLINGPOINT = getMapVal(map, "SAMPLINGPOINT");// String.valueOf(map.get("SAMPLINGPOINT"));
					String ANALYSISNAME = getMapVal(map, "ANALYSISNAME");// String.valueOf(map.get("ANALYSISNAME"));
					String ITEMNAME = getMapVal(map, "ITEMNAME");// String.valueOf(map.get("ITEMNAME"));
					String VAL = getMapVal(map, "FORMATTEDENTRY");// String.valueOf(map.get("FORMATTEDENTRY"));
					String key = (new StringBuffer(PROCESSUNIT).append(".").append(PRODUCTNAME).append(".")
							.append(SAMPLINGPOINT).append(".").append(ANALYSISNAME).append(".").append(ITEMNAME))
									.toString();
					String key2 = new StringBuffer(key).append(".").append(SAMPLEDDATE).toString();
					if (!hlist.contains(key2)) {// 同一时间不能有两个结果
						LimsDataVo obj = new LimsDataVo();
						obj.setProcessUnit(PROCESSUNIT);
						obj.setProductName(PRODUCTNAME);
						obj.setSamplingPoint(SAMPLINGPOINT);
						obj.setAnalysisName(ANALYSISNAME);
						obj.setItemName(ITEMNAME);
						obj.setTime(SAMPLEDDATE);
						obj.setValue(VAL);

						if (dataMap.containsKey(key)) {
							dataMap.get(key).add(obj);
						} else {
							List<LimsDataVo> _list = new ArrayList<LimsDataVo>();
							_list.add(obj);
							dataMap.put(key, _list);
						}

						hlist.add(key2);
					}
				}

				// 可能有标识相同的两个不同tagid的数据，相同值赋给不同的tagid相同的标识
				for (LimsDataVo limis : limsyb) {
					String PROCESSUNIT = limis.getProcessUnit() == null ? "" : limis.getProcessUnit();
					String PRODUCTNAME = limis.getProductName() == null ? "" : limis.getProductName();
					String SAMPLINGPOINT = limis.getSamplingPoint() == null ? "" : limis.getSamplingPoint();
					String ANALYSISNAME = limis.getAnalysisName() == null ? "" : limis.getAnalysisName();
					String ITEMNAME = limis.getItemName() == null ? "" : limis.getItemName();
					String key = (new StringBuffer(PROCESSUNIT).append(".").append(PRODUCTNAME).append(".")
							.append(SAMPLINGPOINT).append(".").append(ANALYSISNAME).append(".").append(ITEMNAME))
									.toString();

					if (dataMap.containsKey(key)) {// 关系对应
						String dotId = limis.getDotId();
						List<LimsDataVo> dlist = dataMap.get(key);// 获取对应结果列表
						if (StringUtils.isNotEmpty(dlist)) {
							for (LimsDataVo data : dlist) {
								data.setDotId(dotId);
								if (rmap.containsKey(dotId)) {// 根据ID保存生成map
									rmap.get(dotId).add(data);
								} else {
									List<LimsDataVo> _list = new ArrayList<LimsDataVo>();
									_list.add(data);
									rmap.put(dotId, _list);
								}
							}
						}
					}
				}
			}
		}
		return rmap;
	}

	private String getMapVal(Map<String, Object> map, String col) {
		Object o = map.get(col);
		if (o == null) {
			return "";
		} else {
			return String.valueOf(o);
		}
	}

//	@Override
	/**
	 * @category 获取LIMS数据
	 * @param dto
	 * @return
	 */
//	public HashMap<String, List<LimsDataVo>> getLimsData(LimsDataDto dto) {
//		HashMap<String, List<LimsDataVo>> rtn = new HashMap<String, List<LimsDataVo>>();
//		List<LimsDataVo> limsyb = dto.getLimsyb();
//		if (limsyb != null && limsyb.size() > 0) {
//			String kssj = dto.getKssj();
//			String jzsj = dto.getJzsj();
//			// 获取给定时间段的LIMS数据：以采样时间为准
//			Where where = Where.create();
//			where.ge(ProdLimsResult::getSampledDate, kssj);
//			where.le(ProdLimsResult::getSampledDate, jzsj);
//			// 排序
//			Order order = Order.create();
//			order.orderByAsc(ProdLimsResult::getSampledDate);
//			List<ProdLimsResult> limsdata = srv.queryList(ProdLimsResult.class, where, order, null);
//			if (limsdata != null) {
//				String key1, key2, dotid, dotmc, dotbm;
//				String zzmc, cp, cyd, xm, fx, delbs, sj, val;
//				HashMap<String, List<ProdLimsResult>> dm = new HashMap<String, List<ProdLimsResult>>();
//				HashMap<String, String> djm = new HashMap<String, String>();
//				for (ProdLimsResult d : limsdata) {
//					delbs = d.getTmused();
//					if (delbs == null) {
//						delbs = "f";
//					}
//					if ("t".equalsIgnoreCase(delbs)) {
//						continue;// 已删除的数据不采集
//					}
//					zzmc = d.getProcessunit();
//					if (zzmc == null) {
//						zzmc = "";
//					}
//					cp = d.getProductName();
//					if (cp == null) {
//						cp = "";
//					}
//					cyd = d.getSamplingPoint();
//					if (cyd == null) {
//						cyd = "";
//					}
//					xm = d.getAnalysisName();
//					if (xm == null) {
//						xm = "";
//					}
//					fx = d.getItemName();
//					if (fx == null) {
//						fx = "";
//					}
//					key1 = (new StringBuffer(zzmc).append(".").append(cp).append(".").append(cyd).append(".").append(xm)
//							.append(".").append(fx)).toString();
//					sj = d.getSampledDate();
//					key2 = (new StringBuffer(key1).append(".").append(sj)).toString();
//					if (!djm.containsKey(key2)) {
//						// 同一时间不能有两个结果
//						djm.put(key2, "1");
//						if (dm.containsKey(key1)) {
//							List<ProdLimsResult> m = dm.get(key1);
//							m.add(d);
//							dm.put(key1, m);
//						} else {
//							List<ProdLimsResult> m = new ArrayList<ProdLimsResult>();
//							m.add(d);
//							dm.put(key1, m);
//						}
//					}
//				}
//				// 给返回数据赋值
//				for (LimsDataVo j : limsyb) {
//					dotid = j.getDotId();
//					if (StringUtils.isEmpty(dotid)) {
//						continue;
//					}
//					dotmc = j.getInstrumentName();
//					if (StringUtils.isEmpty(dotmc)) {
//						continue;
//					}
//					dotbm = j.getTagNumber();
//					if (StringUtils.isEmpty(dotbm)) {
//						continue;
//					}
//					zzmc = j.getProcessUnit();
//					if (zzmc == null) {
//						zzmc = "";
//					}
//					cp = j.getProductName();
//					if (cp == null) {
//						cp = "";
//					}
//					cyd = j.getSamplingPoint();
//					if (cyd == null) {
//						cyd = "";
//					}
//					xm = j.getAnalysisName();
//					if (xm == null) {
//						xm = "";
//					}
//					fx = j.getItemName();
//					if (fx == null) {
//						fx = "";
//					}
////					key1 = (new StringBuffer(zzmc).append(".").append(cp).append(".").append(cyd).append(".").append(xm)
////							.append(".").append(fx)).toString();
//					key1 = dotid;
//					if (dm.containsKey(key1)) {
//						List<ProdLimsResult> xd = dm.get(key1);
//						if (xd != null) {
//							for (ProdLimsResult jg : xd) {
//								delbs = jg.getTmused();
//								if (delbs == null) {
//									delbs = "f";
//								}
//								if ("t".equalsIgnoreCase(delbs)) {
//									continue;// 已删除的数据不采集
//								}
//								sj = jg.getSampledDate();
//								val = jg.getFormattedEntry();
//								if (val == null) {
//									continue;
//								}
//								LimsDataVo vo = new LimsDataVo();
//								vo.setProcessUnit(zzmc);
//								vo.setProductName(cp);
//								vo.setSamplingPoint(cyd);
//								vo.setAnalysisName(xm);
//								vo.setItemName(fx);
//								vo.setDotId(dotid);
//								vo.setInstrumentName(dotmc);
//								vo.setTagNumber(dotbm);
//								vo.setTime(sj);
//								vo.setValue(val);
//								if (rtn.containsKey(key1)) {
//									List<LimsDataVo> l = rtn.get(key1);
//									l.add(vo);
//									rtn.put(key1, l);
//								} else {
//									List<LimsDataVo> l = new ArrayList<LimsDataVo>();
//									l.add(vo);
//									rtn.put(key1, l);
//								}
//							}
//						}
//					}
//				}
//			}
//		}
//
//		return rtn;
//	}

	@Override
	public List<TdsAccountMarkinfo> getTdsAccountAllMarkData(String tdsAlias, String st, String et,
			List<String> colList) {

		List<TdsAccountMarkinfo> rlist = new ArrayList<TdsAccountMarkinfo>();

		if (colList.size() > 500) {

			List<String> clist = new ArrayList<String>();
			for (String tagId : colList) {
				clist.add(tagId);

				if (clist.size() >= 500) {
					Where where = Where.create();
					where.eq(TdsAccountMarkinfo::getTmused, 1);
					where.eq(TdsAccountMarkinfo::getTdsalias, tdsAlias);
					if (StringUtils.isNotEmpty(st))
						where.ge(TdsAccountMarkinfo::getTimepoInteger, st);
					if (StringUtils.isNotEmpty(et))
						where.le(TdsAccountMarkinfo::getTimepoInteger, et);
					if (StringUtils.isNotEmpty(clist)) {
						where.in(TdsAccountMarkinfo::getColalias, clist.toArray());
					}
					List<TdsAccountMarkinfo> tlist = srv.queryList(TdsAccountMarkinfo.class, where, null);
					if (StringUtils.isNotEmpty(tlist)) {
						rlist.addAll(tlist);
					}
					clist.clear();
				}
			}
			if (clist.size() > 0) {
				Where where = Where.create();
				where.eq(TdsAccountMarkinfo::getTmused, 1);
				where.eq(TdsAccountMarkinfo::getTdsalias, tdsAlias);
				if (StringUtils.isNotEmpty(st))
					where.ge(TdsAccountMarkinfo::getTimepoInteger, st);
				if (StringUtils.isNotEmpty(et))
					where.le(TdsAccountMarkinfo::getTimepoInteger, et);
				if (StringUtils.isNotEmpty(clist)) {
					where.in(TdsAccountMarkinfo::getColalias, clist.toArray());
				}
				List<TdsAccountMarkinfo> tlist = srv.queryList(TdsAccountMarkinfo.class, where, null);
				if (StringUtils.isNotEmpty(tlist)) {
					rlist.addAll(tlist);
				}
			}

		} else {
			Where where = Where.create();
			where.eq(TdsAccountMarkinfo::getTmused, 1);
			where.eq(TdsAccountMarkinfo::getTdsalias, tdsAlias);
			if (StringUtils.isNotEmpty(st))
				where.ge(TdsAccountMarkinfo::getTimepoInteger, st);
			if (StringUtils.isNotEmpty(et))
				where.le(TdsAccountMarkinfo::getTimepoInteger, et);
			if (StringUtils.isNotEmpty(colList)) {
				where.in(TdsAccountMarkinfo::getColalias, colList.toArray());
			}
			rlist = srv.queryList(TdsAccountMarkinfo.class, where, null);
		}
		return rlist;
	}

	@Override
	public List<TdsAccountMarkinfo> getAccountMarkData(TdsAccountDto param) {
		String tdsAlias = param.getTdsAlias();
//		String unitCode = param.getUnitCode();
//		String accountId = param.getAccountId();
		String markKey = param.getMarkKey();

		Where where = Where.create();
		where.eq(TdsAccountMarkinfo::getTmused, 1);
		where.eq(TdsAccountMarkinfo::getTdsalias, tdsAlias);
		where.eq(TdsAccountMarkinfo::getMarkKey, markKey);
		List<TdsAccountMarkinfo> rlist = srv.queryList(TdsAccountMarkinfo.class, where, null);
		return rlist;
	}

	@Override
	public Boolean saveAccountMarkData(TdsAccountDto param) {
		Boolean flag = true;

		String tdsAlias = param.getTdsAlias();
		String markKey = param.getMarkKey();
		String markInfo = param.getMarkInfo();

		TdsAccountDto p = new TdsAccountDto();
		p.setTdsAlias(tdsAlias);
		p.setMarkKey(markKey);
		List<TdsAccountMarkinfo> list = getAccountMarkData(p);
		if (StringUtils.isNotEmpty(list)) {// 更新
			TdsAccountMarkinfo obj = list.get(0);
			obj.setMarkInfo(markInfo);

			flag = flag && 1 == srv.updateById(obj);
		} else {// 保存
			String markCondition = param.getMarkCondition();// 其他信息
															// unitCode@ZZW6D9CI03QRS20XE70242;bc@ZZW5W1UL00SIB8GFXP3647;sj@2023-12-25
															// 00:00:00,2023-12-25
															// 06:00:00;rq@2023-12-25;accountId@ZQG5BU1MU0453ESH5U7993
			String unitCode = null, accountId = null, bc = null, sbsj = null, xbsj = null, rq = null;

			if (StringUtils.isNotEmpty(markCondition)) {
				List<String> slist = Coms.StrToList(markCondition, ";");
				Map<String, String> _map = new HashMap<String, String>();
				for (String s : slist) {
					List<String> slist2 = Coms.StrToList(s, "@");
					String v = slist2.size() > 1 ? slist2.get(1) : null;
					if ("null".equals(v)) {
						v = null;
					}
					_map.put(slist2.get(0), v);
				}
				accountId = _map.get("accountId");
				unitCode = _map.get("unitCode");
				unitCode = _map.get("bc");
				rq = _map.get("rq");

				String sj = _map.get("sj");
				if (sj != null) {
					List<String> sjlist = Coms.StrToList(sj, ",");
					if (sjlist.size() == 2) {
						sbsj = sjlist.get(0);
						xbsj = sjlist.get(1);
					}
				}

			}
			String colalias = markKey.split("_")[0];
			String timepoint = markKey.split("_")[1];

			TdsAccountMarkinfo obj = new TdsAccountMarkinfo();
			obj.setId(TMUID.getUID());
			obj.setUnitcode(unitCode);
			obj.setFormid(accountId);
			obj.setClsno(bc);
			obj.setColalias(colalias);
			obj.setMarkInfo(markInfo);
			obj.setMarkKey(markKey);
			obj.setTimepoInteger(timepoint);
			obj.setRq(rq);
			obj.setSbsjstr(sbsj);
			obj.setXbsjstr(xbsj);
			obj.setTdsalias(tdsAlias);
			obj.setTmused(1);

			flag = flag && 1 == srv.insert(obj);
		}

		return flag;
	}

	@Override
	public Map<String, String> getAccountUnconfirmInfo(TdsAccountDto param) {
		Map<String, String> rmap = new HashMap<String, String>();

		String tdsAlias = param.getTdsAlias();
		Map<String, String> pmap = new HashMap<String, String>();
		String para = param.getInParaAlias();
		List<String> s = Coms.StrToList(para, "\\|");
		for (String str : s) {
			String[] ss = str.split("=");
			pmap.put(ss[0], ss.length > 1 ? ss[1] : "");
		}
		String rq = StringUtils.isNotEmpty(pmap.get("rq"))?pmap.get("rq"):"";
		String bc = StringUtils.isNotEmpty(pmap.get("bc"))?pmap.get("bc"):"";
		String unitCode = pmap.get("unitCode");
		String accountId = pmap.get("accountId");
		String[] ss = bc.split(",");
		String shiftCode = ss[0];
		String timeBoundBc1 = ss.length > 1 ? ss[1] : "";
		String timeBoundBc2 = ss.length > 2 ? ss[2] : "";

		TdsAccountConf conf = getAccountConf(tdsAlias);// 配置信息
		TdsAccountTime timeConf = getTimeConf(tdsAlias);// 时间配置

		Date startDate = null;
		String startRq = null;
		String startBind = timeConf.getStartBingDay();
		String startFix = timeConf.getStartFixed();
		Boolean haveStart = new Integer(1).equals(timeConf.getStartRound());
		int istartfix = 0;

		Date endDate = null;
		String endRq = null;
		String endBind = timeConf.getEndBingDay();
		String endFix = timeConf.getEndFixed();
		Boolean haveEnd = new Integer(1).equals(timeConf.getEndRound());
		int iendfix = 0;
		if (Coms.judgeLong(startFix)) {
			istartfix = Integer.parseInt(startFix);
		}
		if (Coms.judgeLong(endFix)) {
			iendfix = Integer.parseInt(endFix);
		}
		// 开始时间获取
		if (StringUtils.isNotEmpty(startBind)) {
			// 绑定台账内部绑定
			if ("day".equals(startBind) || "mon".equals(startBind)) {
				startRq = rq;
				Object o = startRq;
				if (DateTimeUtils.parseDate(o) == null) {
					startRq = null;
				} else {
					startRq = startRq.substring(0, 10);
				}
			} else if ("bc".equals(startBind)) {
				startRq = timeBoundBc1;
				Object o = startRq;
				if (DateTimeUtils.parseDate(o) == null) {
					startRq = null;
				} else {
					startRq = startRq.substring(0, 19);
				}
			}
		}
		if (StringUtils.isEmpty(startRq)) {
			if (istartfix != 0) {
				startRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " " + timeConf.getStartBingTime() + ":00";
				startDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(startRq), istartfix);
			} else {
				startRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " " + timeConf.getStartBingTime() + ":00";
				startDate = DateTimeUtils.parseDateTime(startRq);
			}
		} else {
			if ("bc".equals(startBind)) {
				startDate = DateTimeUtils.parseDateTime(startRq);
				if (conf.getBcDiff() != null) {// 上班时间偏差修正
					int bcdiff = conf.getBcDiff();
					startDate = DateTimeUtils.doMinute(startDate, bcdiff);
				}
			} else {
				if (istartfix != 0) {
					startDate = DateTimeUtils.doDate(
							DateTimeUtils.parseDateTime(startRq + " " + timeConf.getStartBingTime() + ":00"),
							istartfix);
				} else {
					startDate = DateTimeUtils.parseDateTime(startRq + " " + timeConf.getStartBingTime() + ":00");
				}
			}
		}
		// 截止时间获取
		if (StringUtils.isNotEmpty(endBind)) {
			// 绑定台账内部绑定
			if ("day".equals(endBind) || "mon".equals(endBind)) {
				endRq = rq;
				Object o = endRq;
				if (DateTimeUtils.parseDate(o) == null) {
					endRq = null;
				} else {
					endRq = endRq.substring(0, 10);
				}
			} else if ("bc".equals(endBind)) {
				endRq = timeBoundBc2;
				Object o = endRq;
				if (DateTimeUtils.parseDate(o) == null) {
					endRq = null;
				} else {
					endRq = endRq.substring(0, 19);
				}
			}
		}
		if (StringUtils.isEmpty(endRq)) {
			if (iendfix != 0) {
				endRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " " + timeConf.getEndBingTime() + ":00";
				endDate = DateTimeUtils.doDate(DateTimeUtils.parseDateTime(endRq), iendfix);
			} else {
				endRq = DateTimeUtils.getNowDateStr().substring(0, 10) + " " + timeConf.getEndBingTime() + ":00";
				endDate = DateTimeUtils.parseDateTime(endRq);
			}
		} else {
			if ("bc".equals(endBind)) {
				endDate = DateTimeUtils.parseDateTime(endRq);
				if (conf.getBcDiff() != null) {// 下班时间偏差修正
					int bcdiff = conf.getBcDiff();
					endDate = DateTimeUtils.doMinute(endDate, bcdiff);
				}
			} else {
				if (iendfix != 0) {
					endDate = DateTimeUtils.doDate(
							DateTimeUtils.parseDateTime(endRq + " " + timeConf.getEndBingTime() + ":00"), iendfix);
				} else {
					endDate = DateTimeUtils.parseDateTime(endRq + " " + timeConf.getEndBingTime() + ":00");
				}
			}
		}

		List<Date> timeList = new ArrayList<Date>();
		int unConfirm = 0;// 未确认
		int confirmNum = 0;// 共有确认

		if (DateTimeUtils.bjDate(startDate, endDate) == 1) {// 如果时间不对，不获取数据
		} else {

			Date tempDate = startDate;
			String timeStep = timeConf.getTimeStep();
			Integer step = 60;
			if (Coms.judgeLong(timeStep)) {
				step = Integer.parseInt(timeStep);
			}

			if (haveStart) {
				timeList.add(startDate);
				tempDate = DateTimeUtils.doMinute(tempDate, step);
			}

			for (int i = 0, il = 50; i < il && DateTimeUtils.bjDate(endDate, tempDate) > 0; i++) {// 时间对比，增加循环数，避免死循环
				timeList.add((Date) tempDate.clone());
				tempDate = DateTimeUtils.doMinute(tempDate, step);
			}

			if (DateTimeUtils.bjDate(tempDate, endDate) == 1) {
				if (haveEnd) {
					if (!timeList.contains(endDate)) {
						timeList.add(endDate);
					}
				}
			} else if (DateTimeUtils.bjDate(tempDate, endDate) == 0) {
				// 间隔固定
				if (haveEnd) {
					timeList.add(endDate);
				}
			}

			if (StringUtils.isNotEmpty(timeList)) {
				Date sd = null;
				Date ed = null;

				List<String> confirmList = new ArrayList<String>();// 显示时间点列表
				List<String> showTimeList = new ArrayList<String>();// 显示时间点列表
				Date nd = DateTimeUtils.getNowDate();
				for (Date date : timeList) {
//					if(DateTimeUtils.bjDate(nd, date) == 1) {
					showTimeList.add(DateTimeUtils.formatDateTime(date));
//					}
				}
				sd = timeList.get(0);
				ed = timeList.get(timeList.size() - 1);

//				String sendrq = timeBoundBc1.length() >= 10 ? timeBoundBc1.substring(0, 10) : rq;
				// 确认表数据
				List<Map> cdata = getAccountNewData(accountNewTab + "_confirm_" + tdsAlias, 1, rq, unitCode, shiftCode,
						accountId);
				if (StringUtils.isNotEmpty(cdata)) {
					Object cinfo = cdata.get(0).get("info");
					if (cinfo != null) {
						List<Map> tmap = (List<Map>) cinfo;
						for (Map map : tmap) {
							String sj = String.valueOf(map.get("sj"));
							confirmList.add(sj);
						}
					}
				} else {// 历史数据查询（兼容历史数据）
					List<Map> hdata = getAccountTimeConfirmData(accountTabName + "_" + tdsAlias, tdsAlias, sd.getTime(),
							ed.getTime(), accountId, unitCode);
					if (StringUtils.isNotEmpty(hdata)) {
						for (Map map : hdata) {
							String sj = String.valueOf(map.get("sj"));
							if (!confirmList.contains(sj)) {
								confirmList.add(sj);
							}
						}
					}
				}
				for (String sj : showTimeList) {
					confirmNum++;
					if (!confirmList.contains(sj)) {
						unConfirm++;
					}
				}
			}
		}

		rmap.put("confirmNum", String.valueOf(confirmNum));
		rmap.put("unConfirm", String.valueOf(unConfirm));

		return rmap;
	}
	
	@Override
	public Boolean updateTodoMark(String rq, String unitCode, String shiftCode, String accountId, String ds) {
		Boolean flag = true;
		List<AccountAutoSaveTodo> list = getTodoData(rq, unitCode, shiftCode, accountId, ds, 1);
		if(StringUtils.isNotEmpty(list)) {
			for (AccountAutoSaveTodo obj : list) {
				obj.setTodoMark(0);
			}
			flag = 1 == srv.updateBatch(list);
		}
		return flag;
	}
	
	@Override
	public List<AccountAutoSaveTodo> getTodoData(String rq, String unitCode, String shiftCode, String accountId, String ds, Integer mark) {
		List<AccountAutoSaveTodo> rlist = new ArrayList<AccountAutoSaveTodo>();
		Where where = Where.create();
		where.eq(AccountAutoSaveTodo::getTmused, 1);
		where.eq(AccountAutoSaveTodo::getRq, rq);
		if(StringUtils.isNotEmpty(unitCode)) {
			where.eq(AccountAutoSaveTodo::getUnitCode, unitCode);
		}
		if(StringUtils.isNotEmpty(shiftCode)) {
			where.eq(AccountAutoSaveTodo::getShiftCode, shiftCode);
		}
		if(StringUtils.isNotEmpty(accountId)) {
			where.eq(AccountAutoSaveTodo::getCustomCode, accountId);
		}
		if(StringUtils.isNotEmpty(ds)) {
			where.eq(AccountAutoSaveTodo::getTdsAlias, ds);
		}
		if(mark!=null) {
			where.eq(AccountAutoSaveTodo::getTodoMark, mark);
		}
		Order order = Order.create();
		order.orderByAsc(AccountAutoSaveTodo::getUnitCode).orderByAsc(AccountAutoSaveTodo::getShiftCode).orderByAsc(AccountAutoSaveTodo::getCustomCode);
		rlist = srv.queryData(AccountAutoSaveTodo.class, where, order, null);
		
		return rlist;
	}

}
