package com.yunhesoft.system.tds.service.impl;

import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.entity.po.TdsOcrSet;
import com.yunhesoft.system.tds.service.IDataSourceOcrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * @Description: 数据源图片识别参数设置实现类
 * <AUTHOR>
 * @date 2023/4/23
 */
@Service
public class DataSourceOcrServiceImpl implements IDataSourceOcrService {
    @Autowired
    private EntityService dao;
    /**
     * 查询图片识别功能参数
     *
     * @param tdsAlias
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public TdsOcrSet getOcrSet(String tdsAlias) {
        Where where = Where.create();
        where.eq(TdsOcrSet::getTdsAlias,tdsAlias);
        List<TdsOcrSet> tdsOcrSets = dao.queryList(TdsOcrSet.class, where);
        if(StringUtils.isNotEmpty(tdsOcrSets)){
            return tdsOcrSets.get(0);
        }
        TdsOcrSet tdsOcrSet = new TdsOcrSet();
        tdsOcrSet.setId("");
        tdsOcrSet.setOcrMoudle("");
        tdsOcrSet.setOcrOkAfterScript("");
        return tdsOcrSet;
    }

    /**
     * 保存图片识别功能参数
     *
     * @param param
     * @return
     * <AUTHOR>
     * @params
     */
    @Override
    public Boolean saveOcrSet(TdsOcrSet param) {
        int flag = 0;
        if(ObjUtils.notEmpty(param)){
            if(StringUtils.isNotEmpty(param.getId())){
                //修改
                List<TdsOcrSet> list = new ArrayList<>();
                list.add(param);
                flag = dao.updateByIdBatchIncludeNull(list,500);
            }else {
                //新增
                param.setId(TMUID.getUID());
                flag = dao.insert(param);
            }
        }
        return flag>0;
    }
}
