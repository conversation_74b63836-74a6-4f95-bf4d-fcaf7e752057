package com.yunhesoft.system.tds.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.syslog.entity.dto.OperationLogAdd;
import com.yunhesoft.syslog.service.LogManageService;
import com.yunhesoft.system.applyConf.entity.dto.ApplyParams;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountLog;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountOverinfo;
import com.yunhesoft.system.applyConf.entity.vo.AccountSaveDto;
import com.yunhesoft.system.applyConf.entity.vo.AcctobjInputFlVo;
import com.yunhesoft.system.applyConf.entity.vo.AcctobjInputVo;
import com.yunhesoft.system.applyConf.entity.vo.AcctobjInputmxVo;
import com.yunhesoft.system.applyConf.service.IApplyConfService;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.ColumnUtils;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.kernel.utils.excel.ExcelImport;
import com.yunhesoft.system.kernel.utils.mongodb.service.MongoDBService;
import com.yunhesoft.system.tds.entity.dto.TdsAccountDto;
import com.yunhesoft.system.tds.entity.dto.TdsEditDto;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.entity.po.*;
import com.yunhesoft.system.tds.entity.vo.TdsAccountOutparamVo;
import com.yunhesoft.system.tds.entity.vo.TdsSqlVo;
import com.yunhesoft.system.tds.model.IDataSource;
import com.yunhesoft.system.tds.model.TDataSourceManager;
import com.yunhesoft.system.tds.model.TRow;
import com.yunhesoft.system.tds.service.IDataSourceAccountService;
import com.yunhesoft.system.tds.service.IDataSourceEditService;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tds.utils.TdsTools;
import com.yunhesoft.system.tools.microservice.service.IMicroserviceComService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.Types;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数据源修改数据库操作实现类
 *
 * <AUTHOR>
 *
 */
@Service
@Log4j2
public class DataSourceEditServiceImpl implements IDataSourceEditService {

	@Autowired
	private EntityService dao;

	@Autowired
	private IDataSourceService dsServ;

	@Autowired
	private MongoDBService mongoDBServ;

	@Autowired
	private IDataSourceAccountService accountServ; // 台账服务

	@Autowired
	private IApplyConfService applySrv; // 台账配置服务

	@Autowired
	private LogManageService logSrv;

//	private static String TDS_EDITLOG = "TDS:EDITLOG:"; // 数据源修改记录
//
//	@Autowired
//	private MongoDBService mongodbServ;
//
//	@Autowired
//	private RedisUtil redis;

//	@Resource
//	private ApplicationContext context;//应用事件服务

	@Autowired
	private ISysEmployeeInfoService sysEmployeeInfoService;

	@Autowired
	private IMicroserviceComService context;// 微服务模式调用

	@Value("${spring.elecLedger.serverName:TM4-PAAS-JOBLIST}")
	private String serviceName;
	@Value("${spring.elecLedger.path:/tm4main/joblist/input/saveForAccount}")
	private String requestPath;

	private static String PK = "ID"; // 主键列字段名称

	// private String inParaStr;
	private final String accountTabName = "accountTab";
	private final String accountNewTab = "ac";
	private final String infoCol = "_accountInfoCol";// 信息列
	private final String typeCol = "_accountTypeCol";// 类别列
	private final String ulinfoCol = "_accountULInfoCol";// 上下限信息列
	private final String timeCol = "timeMarkCol";// 时间列
	private final String ulobjCol = "accountULInfoColObj";// 对象列
	private final String xCol = "_XID";// 对象列

	private final String jobInputTime="_jobInputTime";

	/**
	 * 更新数据
	 *
	 * @param param
	 * @param tdataSource
	 * @return
	 */
	private String saveData(TdsEditDto param, TdataSource tdataSource) {
		String rtn = "";
		try {
			JSONArray datas = JSONArray.parseArray(param.getData());
			JSONArray inparas = null;
			if (StringUtils.isNotEmpty(param.getInParaObj())) {
				try {
					inparas = JSONArray.parseArray(param.getInParaObj());
				} catch (Exception ex) {
					log.error("", ex);
				}
			}
			Map<String, String> checkMap = this.tdsCheckCol(tdataSource.getTdsalias(), inparas, datas);
			if (checkMap.size() > 0) {// 校验失败
				rtn = this.getCheckInfo(checkMap);
			} else {// 校验成功
				rtn = this.saveData(tdataSource, datas);
			}
		} catch (Exception e) {
			rtn = e.getMessage();
		}
		return rtn;
	}

	/**
	 * 保存校验
	 *
	 * @param datas
	 */
	private String saveCheck(JSONArray datas) {
		if (datas == null || datas.size() == 0) {
			return "添加、修改、删除的记录数量为0";

		}
		List<String> listCols = this.getCols(datas.getJSONObject(0));
		if (StringUtils.isEmpty(listCols)) {
			return "请设置数据源更新字段";
		}
		if (!listCols.contains(PK)) {
			return "数据源输出参数未设置主键列（" + PK + "）";

		}
		return "";

	}

	/**
	 * 保存数据到MongoDB
	 *
	 * @param collectionName
	 * @param datas
	 * @return
	 */
	private String saveData2MongoDB(String collectionName, List<TdsoutPara> outparaList, JSONArray datas,
			TdataSource tdataSource) {
		String rtn = "";
		int useLogicDelete = Optional.ofNullable(tdataSource).map(TdataSource::getUseLogicDelete).orElse(0);
		String logicDeleteColumn = Optional.ofNullable(tdataSource).map(TdataSource::getLogicDeleteColumn).orElse(null);
		try {
			rtn = this.saveCheck(datas);
			if (StringUtils.isNotEmpty(rtn)) {
				return rtn;
			}
			JSONArray insertData = new JSONArray();
			JSONArray updateData = new JSONArray();
			List<String> deleteIds = new ArrayList<String>();
			JSONArray logicDeleteData = new JSONArray();
			Map<String, TdsoutPara> opMap = new HashMap<String, TdsoutPara>();
			if (outparaList != null) {
				for (TdsoutPara op : outparaList) {
					opMap.put(op.getParaAlias().toUpperCase(), op);
				}
			}
			for (int i = 0; i < datas.size(); i++) {
				JSONObject obj = datas.getJSONObject(i);
				Integer flag = obj.getInteger("TDSROW_rowFlag");
				String ID = obj.getString(PK);
				if (flag == null || flag == 0 || flag == 1) {// 添加或修改
					List<String> listCols = this.getCols(obj);
					JSONObject newobj = new JSONObject();
					for (String col : listCols) {
						if (!col.equalsIgnoreCase(PK)) {
							TdsoutPara op = opMap.get(col.toUpperCase());
							if (op != null) {
								newobj.put(op.getParaAlias(), obj.get(col));
							}
						}
					}
					if (StringUtils.isEmpty(ID) || (flag != null && flag == 0)) {// 添加
						if (StringUtils.isEmpty(ID)) {
							newobj.put("_id", TMUID.getUID());
						} else {
							newobj.put("_id", ID);
						}
						// 将新生成的id返回给json对象
						obj.put(PK, newobj.getString("_id"));
						insertData.add(newobj);
					} else {// 修改
						newobj.put("_id", ID);
						updateData.add(newobj);
					}
				} else if (flag == -1) {// 删除

					if (useLogicDelete == 1) {
						// 使用逻辑删除
						if (StringUtils.isNotEmpty(logicDeleteColumn)) {
							JSONObject newobj = new JSONObject();
							newobj.put("_id", ID);
							newobj.put(logicDeleteColumn, 0);
							logicDeleteData.add(newobj);
						}
					} else {
						// 物理删除

						deleteIds.add(ID);
					}
				}
			}

			if (insertData.size() > 0) {// 添加
				mongoDBServ.insertBatch(collectionName, insertData);
			}

			if (updateData.size() > 0) {// 修改
				mongoDBServ.updateBatchById(collectionName, updateData);
			}

			if (deleteIds.size() > 0) {// 删除
				mongoDBServ.deleteBatchById(collectionName, deleteIds);
			}

			if (!logicDeleteData.isEmpty()) {// 逻辑删除
				mongoDBServ.updateBatchById(collectionName, logicDeleteData);
			}

		} catch (Exception e) {
			rtn = e.getMessage();
		}
		return rtn;

	}

	/**
	 * 保存数据
	 *
	 * @param tdataSource
	 * @param datas
	 * @return
	 */
	@Override
	public String saveData(TdataSource tdataSource, JSONArray datas) {
		// 判断是否有列间公式，如果有需要进行列间公式
		List<TdsoutPara> outparaList = dsServ.getTDSOutPara(tdataSource.getTdsalias());
		this.formulaCosCal(outparaList, datas);// 列间公式计算
		if ("TDSMongoDB".equalsIgnoreCase(tdataSource.getTdsclassName())) {// MongoDB
			String collectionName = tdataSource.getDbTableName();

			if (StringUtils.isEmpty(collectionName)) {
				collectionName = tdataSource.getTdsQuerySql();
			}
			collectionName = mongoDBServ.getCollectionName(collectionName);// 适应多租户模式
			return this.saveData2MongoDB(collectionName, outparaList, datas, tdataSource);
		} else if ("TDSAccount".equalsIgnoreCase(tdataSource.getTdsclassName())) {// 台账数据源 MongoDB数据保存
			String collectionName = tdataSource.getTdsalias();
			collectionName = mongoDBServ.getCollectionName(collectionName);// 适应多租户模式
			return this.saveAccountData2MongoDB(collectionName, tdataSource.getTdsalias(), datas);
		} else {// 关系数据库
			return this.saveData(tdataSource.getDbTableName(), datas, tdataSource);
		}
	}

	/**
	 * 列间公式计算
	 *
	 * @param outparaList
	 * @param datas
	 */
	private void formulaCosCal(List<TdsoutPara> outparaList, JSONArray datas) {
		if (StringUtils.isNotEmpty(outparaList) && StringUtils.isNotEmpty(datas)) {
			LinkedHashMap<String, String> formulaCos = new LinkedHashMap<String, String>();// 含有公式的字段
			String tdsAlias = "";
			Map<String, TdsoutPara> colMap = new HashMap<String, TdsoutPara>();
			for (TdsoutPara op : outparaList) {
				colMap.put(op.getParaAlias().toUpperCase(), op);
				if (StringUtils.isNotEmpty(op.getReportFormula())) {
					tdsAlias = op.getTdsalias();
					formulaCos.put(op.getParaAlias(), op.getReportFormula());
				}
			}
			// 开始计算公式列
			if (StringUtils.isNotEmpty(formulaCos)) {
				for (int i = 0; i < datas.size(); i++) {
					JSONObject obj = datas.getJSONObject(i);
					if (obj.containsKey("TDSROW_rowFlag") && obj.getIntValue("TDSROW_rowFlag") == -1) {// 删除不技计算
					} else {// 添加or修改
						for (String alias : formulaCos.keySet()) {
							String formula = formulaCos.get(alias);// op.get(index).getReportFormula();
							try {
								Map<String, Object> valMap = new HashMap<String, Object>();
								for (String k : obj.keySet()) {
									if (colMap.containsKey(k.toUpperCase())) {
										TdsoutPara op = colMap.get(k.toUpperCase());
										Object val = obj.get(k);
										try {
											val = TdsTools.castColumnNumber(op.getDataType(), val);
											valMap.put(op.getParaAlias(), val);
										} catch (Exception e) {
											valMap.put(op.getParaAlias(), obj.get(k));
											log.error("类型转换错误：" + val.toString(), e);
										}
									}
								}
								Object val = TdsTools.colFormulaCal(valMap, formula);// 列间公式计算
								if (obj.containsKey(alias.toUpperCase())) {
									obj.put(alias.toUpperCase(), val);
								} else {
									obj.put(alias, val);// rowMp.put(alias, val
								}
							} catch (Exception e) {
								log.error("数据源列间公式计算错误[" + tdsAlias + "],公式:" + formula, e);
							}
						}
					}
				}
			}
		}
	}

	@Override
	public String saveData(String tableName, JSONArray datas) {
		return saveData(tableName, datas, null);
	}

	/**
	 * 保存数据
	 *
	 * @param tableName
	 * @param datas
	 * @return
	 */
	// @Transactional
	@Override
	public String saveData(String tableName, JSONArray datas, TdataSource tdataSource) {
		String rtn = "";
		int useLogicDelete = Optional.ofNullable(tdataSource).map(TdataSource::getUseLogicDelete).orElse(0);
		String logicDeleteColumn = Optional.ofNullable(tdataSource).map(TdataSource::getLogicDeleteColumn).orElse(null);
		try {
			if (StringUtils.isNotEmpty(datas)) {
				rtn = this.saveCheck(datas);
				if (StringUtils.isNotEmpty(rtn)) {
					return rtn;
				}
				List<String> insertList = new ArrayList<String>();
				List<String> updateList = new ArrayList<String>();
				List<String> deleteList = new ArrayList<String>();
				List<Object[]> insertValues = new ArrayList<Object[]>();
				List<Object[]> updateValues = new ArrayList<Object[]>();
				List<Object[]> deleteValues = new ArrayList<Object[]>();
				List<String> logicDeleteList = new ArrayList<>();
				List<Object[]> logicDeleteValues = new ArrayList<>();
				for (int i = 0; i < datas.size(); i++) {
					JSONObject obj = datas.getJSONObject(i);
					Integer flag = obj.getInteger("TDSROW_rowFlag");
					String ID = obj.getString(PK);
					if (flag == null || flag == 0 || flag == 1 || StringUtils.isEmpty(ID)) {// 添加或修改
						if (StringUtils.isEmpty(ID) || (flag != null && flag == 0)) {// 添加
							// ID为空，或者ID不为空但是指定用传入ID添加（flag=0指定添加）
							List<String> listInsertCols = this.getCols(obj);// 添加列
							if (StringUtils.isEmpty(ID)) {
								obj.put(PK, TMUID.getUID());// 将新生成的id返回给json对象
							}
							List<Object> val = this.getInsertValue(listInsertCols, obj);// 添加值
							if (insertList.size() == 0) {
								insertList.add(this.getInsertSql(tableName, listInsertCols));
							}
							insertValues.add(val.toArray());
							// dao.rawInsert(this.getInsertSql(tableName, listInsertCols), val.toArray());
						} else {// 修改
							List<String> listUpdateCols = this.getCols(obj);// 修改列
							List<Object> val = this.getUpdateValue(listUpdateCols, obj);// 修改值
							if (updateList.size() == 0) {
								updateList.add(this.getUpdateSql(tableName, listUpdateCols));
							}
							updateValues.add(val.toArray());
							// dao.rawUpdate(this.getUpdateSql(tableName, listUpdateCols), val.toArray());
						}
					} else if (flag == -1) {// 删除
						if (useLogicDelete == 1) {
							// 使用逻辑删除
							if (StringUtils.isNotEmpty(logicDeleteColumn)) {
								if (logicDeleteList.isEmpty()) {
									logicDeleteList.add("update " + tableName + " set " + logicDeleteColumn
											+ "=0 where " + PK + "=? ");
								}
								String[] val = { ID };
								logicDeleteValues.add(val);
							}
						} else {
							// 物理删除
							String[] val = { ID };
							deleteValues.add(val);
							if (deleteList.size() == 0) {
								deleteList.add(this.getDeleteSql(tableName));
							}
							// dao.rawDelete(this.getDeleteSql(tableName), ID);
						}
					}
				}
				if (insertList.size() > 0) {
					dao.insertBatch(insertList.get(0), insertValues);
				}
				if (updateList.size() > 0) {
					dao.updateBatch(updateList.get(0), updateValues);
				}
				if (deleteList.size() > 0) {
					dao.deleteBatch(deleteList.get(0), deleteValues);
				}

				// 逻辑删除
				if (!logicDeleteList.isEmpty()) {
					dao.updateBatch(logicDeleteList.get(0), logicDeleteValues);
				}
			}
		} catch (Exception e) {
			rtn = e.getMessage();
			log.error("", e);
		}
		return rtn;
	}

	/**
	 * 从数据库中获取表原始值
	 *
	 * @param tableName
	 * @param ID
	 * @return
	 */
//	private Map<String, Object> getDataFromDb(String tableName, String ID, JSONObject obj) {
//		try {
//			if (obj != null) {
//				StringBuffer sb = new StringBuffer();
//				for (String k : obj.keySet()) {
//					if ("TDSROW_rowFlag".equals(k)) {
//						continue;
//					}
//					sb.append(k);
//					sb.append(",");
//				}
//				String sql = sb.toString();
//				sql = sql.substring(0, sql.length() - 1);
//				sql = "SELECT " + sql + " FROM " + tableName + " WHERE ID=?";
//				List<Object> values = new ArrayList<Object>();
//				values.add(ID);
//				List<LinkedHashMap<String, Object>> list = dao.query(sql, values);
//				if (StringUtils.isNotEmpty(list)) {
//					return list.get(0);
//				}
//			}
//		} catch (Exception e) {
//			log.error("", e);
//		}
//		return null;
//	}

	/**
	 * 添加到redis
	 *
	 * @param tdsAlias
	 * @param ID
	 * @param obj
	 */
//	private void addRedis(String tableName, String ID, JSONObject obj) {
//		String key = TDS_EDITLOG + tableName;
//		redis.setMapValue(key, ID, obj);
//	}

	/**
	 * 从redis获取最近一次数据记录
	 *
	 * @param tableName
	 * @param ID
	 * @return
	 */
//	private Map<String, Object> getDataFromRedis(String tableName, String ID) {
//		Map<String, Object> map = null;
//		try {
//			String key = TDS_EDITLOG + tableName;
//			Object val = redis.getMapValue(key, ID);
//			if (val != null) {
//				map = (Map<String, Object>) val;
//			}
//		} catch (Exception e) {
//			log.error("", e);
//		}
//		return map;
//	}

	/**
	 * 修改记录
	 *
	 * @param oldObj
	 * @param newObj
	 */

	/**
	 * 修改记录
	 *
	 * @param oldObj
	 * @param newObj
	 */
//	private void editLog(String tdsAlias, String tableName, String dataId, Map<String, Object> oldObj,
//			JSONObject newObj) {
//		for (String key : newObj.keySet()) {
//			Object newVal = newObj.get(key);
//			Object oldVal = oldObj.get(key);
//			boolean isModify = false;// 是否修改
//			if (newVal == null || oldVal == null) {
//				if (newVal == null && oldVal == null) {
//					isModify = false;
//				} else {
//					isModify = true;
//				}
//			} else {
//				if (newVal.getClass().getName().equals(oldVal.getClass().getName())) {// 类型相同
//					if (!newVal.equals(oldVal)) {// 数据有变化
//						isModify = true;
//					}
//				} else if (Maths.isNumType(newVal) && Maths.isNumType(oldVal)) {// 数值型比较
//					try {
//						Double ld1 = Double.parseDouble(Maths.formatNum(newVal));
//						Double ld2 = Double.parseDouble(Maths.formatNum(oldVal));
//						if (ld1.compareTo(ld2) != 0) {
//							isModify = true;
//						}
//					} catch (Exception e) {
//					}
//				} else {
//					if (!newVal.equals(oldVal)) {// 数据有变化
//						isModify = true;
//					}
//				}
//			}
//			if (isModify) {// 记录日志
//				TdsEditLog bean = new TdsEditLog();
//				bean.setId(TMUID.getUID());
//				bean.setColname(key);
//				bean.setCreateBy(SysUserHolder.getCurrentUser().getId());
//				bean.setCreateByName(SysUserHolder.getCurrentUser().getRealName());
//				bean.setCreateTime(DateTimeUtils.getNowDateTimeStr());
//				bean.setDataId(dataId);
//				bean.setNewValue(newVal);
//				bean.setOldValue(oldVal);
//				bean.setTableName(tableName);
//				bean.setTdsAlias(tdsAlias);
//				if (mongodbServ != null) {
//					mongodbServ.insert(bean);
//				}
//			}
//		}
//	}

	/**
	 * 保存数据
	 */
	@Override
	public String saveData(TdsEditDto param) {
		String rtn = "";
		String tdsAlias = param.getTdsAlias();
		if (StringUtils.isEmpty(tdsAlias)) {
			return "请输入数据源别名！";
		}
		TdataSource tdataSource = this.getTds(tdsAlias);
		rtn = checkTds(tdataSource);
		if (StringUtils.isNotEmpty(rtn)) {
			return rtn;
		}
		return saveData(param, tdataSource);
	}

	/**
	 * 数据源保存合法性校验
	 *
	 * @param tdataSource
	 * @return
	 */
	private String checkTds(TdataSource tdataSource) {
		String rtn = "";
		if (tdataSource == null) {
			return "数据源不存在";
		}
		if (tdataSource.getUsed() == null || tdataSource.getUsed() != 1) {
			return "数据源未启用（" + tdataSource.getTdsalias() + "）tds_datasource.used=0";
		}
		if ("TDSAccount".equals(tdataSource.getTdsclassName())) {

		} else {
			if (tdataSource.getAllowToSave() == null || tdataSource.getAllowToSave() != 1) {
				return "数据源不允许修改（" + tdataSource.getTdsalias() + "）tds_datasource.AllowToSave=0";
			}
			if (StringUtils.isEmpty(tdataSource.getDbTableName())) {
				return "请设置修改数据库表对象（" + tdataSource.getTdsalias() + "）tds_datasource.DbTableName='' ";
			}
		}
		return rtn;
	}

	/**
	 * 获取更新列
	 *
	 * @param obj
	 * @return
	 */
	private List<String> getCols(JSONObject obj) {
		List<String> list = new ArrayList<String>();
		for (String key : obj.keySet()) {
			if (!key.startsWith("TDSROW_")) {// 过滤标识列
				list.add(key);
			}
		}
		return list;
	}

	/**
	 * 获取添加语句
	 *
	 * @param table
	 * @param Cols
	 * @return
	 */
	private String getInsertSql(String table, List<String> Cols) {
		String sql = "insert into " + table + " (" + PK + "";
		String v = "values (?";
		for (String col : Cols) {
			if (!col.equalsIgnoreCase(PK)) {
				sql += ",";
				if (isOracle()) {
					sql += ColumnUtils.getOracleColumn(col);
				} else {
					sql += col;
				}
				v += ",?";
			}
		}
		sql = sql + ") " + v + ")";
		// System.out.println(sql);
		return sql;
	}

	private boolean isOracle() {
		return dao.isOracle();
	}

	/**
	 * 获取更新语句
	 *
	 * @param table
	 * @param Cols
	 * @return
	 */
	private String getUpdateSql(String table, List<String> Cols) {

		String sql = "update  " + table + " set ";
		String where = "where " + PK + "=?";
		for (String col : Cols) {
			if (!col.equalsIgnoreCase(PK)) {
				if (isOracle()) {
					sql += ColumnUtils.getOracleColumn(col);
				} else {
					sql += col;
				}
				sql += "=? ,";
			}
		}
		sql = sql.substring(0, sql.length() - 1) + " " + where;
		// System.out.println(sql);
		return sql;
	}

	/**
	 * 获取删除语句
	 *
	 * @param table
	 * @return
	 */
	private String getDeleteSql(String table) {
		String sql = "delete from  " + table + " where " + PK + "=? ";
		return sql;
	}

	/**
	 * 获取插入值
	 *
	 * @param Cols
	 * @param value
	 * @return
	 */
	private List<Object> getInsertValue(List<String> Cols, JSONObject value) {
		List<Object> list = new ArrayList<Object>();
		String ID = value.getString(PK);
		if (StringUtils.isNotEmpty(ID)) {
			list.add(ID);
		} else {
			list.add(TMUID.getUID());
		}
		for (String col : Cols) {
			if (!col.equalsIgnoreCase(PK)) {
				list.add(getValue(col, value));
			}
		}
		return list;
	}

	/**
	 * 获取值
	 *
	 * @param col
	 * @param value
	 * @return
	 */
	private Object getValue(String col, JSONObject value) {
		Object obj = value.get(col);
		try {
			if (obj == null || "".equals(obj)) {
				return null;
			} else if (obj instanceof JSONArray) {
				return ((JSONArray) obj).toJSONString();
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return obj;
	}

	/**
	 * 获取更新至
	 *
	 * @param Cols
	 * @param value
	 * @return
	 */
	private List<Object> getUpdateValue(List<String> Cols, JSONObject value) {
		List<Object> list = new ArrayList<Object>();
		for (String col : Cols) {
			if (!col.equalsIgnoreCase(PK)) {
				list.add(getValue(col, value));
			}
		}
		list.add(value.getString(PK));
		return list;
	}

	/**
	 * 获取数据源对象
	 *
	 * @param tdsAlias
	 * @return
	 */
	@Override
	public TdataSource getTds(String tdsAlias) {
		return dsServ.getTDataSource(tdsAlias);
	}

	/**
	 * 批量执行insert语句
	 *
	 * @param listSql
	 * @param listValue
	 * @return
	 */
	@Override
	public int insertBatch(String sql, List<Object[]> listValue, List<Integer> argTypeList) {
		int[] argType = argTypeList.stream().mapToInt(Integer::intValue).toArray();
		return dao.batchSqlUpdate(sql, listValue, argType, 500);
		// dao.batchSqlUpdate(String sql, List<Object[]> batchArgs, int[] argType, 500);
//		int row = 0;
//		for (int i = 0; i < listSql.size(); i++) {
//			int r = dao.rawInsert(listSql.get(i), listValue.get(i).toArray());
//			row = row + r;
//		}
//		return row;
	}

	/**
	 * 查询sql语句
	 *
	 * @param sql
	 * @param values
	 * @return
	 */
	@Override
	public List<LinkedHashMap<String, Object>> querySql(String sql, List<Object> values) {
		return dao.query(sql, values);
	}

	/**
	 * 导入数据
	 *
	 * @param tdsInfo 数据源信息
	 * @param data    数据
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@Override
	public Map<String, String> importData(String tdsInfo, List<?> listData) {
		String info = "";
		Map<String, String> map = new HashMap<String, String>();
		try {
			TDataSourceManager tsm = new TDataSourceManager();
			JSONObject tds = JSON.parseObject(tdsInfo);
			String tdsAlias = tds.getString("name");// 数据源别名
			if (StringUtils.isEmpty(tdsAlias)) {
				map.put("info", "请输入数据源别名！");
				return map;
			}
			TdataSource tdataSource = tsm.getTdataSource(tdsAlias);// this.getTds(tdsAlias);
			info = checkTds(tdataSource);
			if (StringUtils.isNotEmpty(info)) {
				map.put("info", info);
				return map;
			}

			// 表头大于1时，需处理
			int excelHeaderRows = 1;
// ============多表头导入有问题，先屏蔽 by x.zhong 2023.7.11	======================
//			if (tdataSource != null && tdataSource.getExcelHeaderRows() != null
//					&& tdataSource.getExcelHeaderRows() > 0) {
//				excelHeaderRows = tdataSource.getExcelHeaderRows();
//			}
//================================================================================

			// 从输出参数中获取必填项
			List<TdsoutPara> outParaList = tsm.getOutParaList(tdsAlias); // dsServ.getTDSOutPara(tdsAlias);
			List<String> requiredHeaderList = new ArrayList<>();
			if (StringUtils.isNotEmpty(outParaList)) {
				// 输出参数
				for (TdsoutPara outPara : outParaList) {
					if(outPara.getParaAlias().toUpperCase().equals(PK)){//主键不判断
						continue;
					}
					Integer isRequired = outPara.getIsRequired();
					if (isRequired != null && isRequired == 1) {
						String paraName = outPara.getParaName();
//						if (paraName.contains("_")) {
//							int lastIndex = paraName.lastIndexOf("_");
//							paraName = paraName.substring(lastIndex + 1);
//						}
						requiredHeaderList.add(paraName);
					}
				}
			}

			List<Object> listDataResult = new ArrayList<>();
			if (StringUtils.isNotEmpty(listData)) {
				for (Object obj : listData) {
					if (obj == null) {
						continue;
					}
					Map<String, String> objMap = (Map<String, String>) obj;
					if (objMap.size() == 0) {
						continue;
					}
					Map<String, String> objMapResult = new HashMap<>();
					if (excelHeaderRows > 1) {
						// 表头大于1行时，需处理多级表头，取下划线分割的最后一段文字
						Set<String> mapkeys = objMap.keySet();
						for (String key : mapkeys) {
							String keyresult = key;
							if (key.contains("_")) {
								int lastIndex = key.lastIndexOf("_");
								keyresult = key.substring(lastIndex + 1);
							}
							objMapResult.put(keyresult, objMap.get(key));
						}
					} else {
						objMapResult = objMap;

					}
					if (requiredHeaderList.size() > 0) {
						boolean emptyRow = false;
						for (String header : requiredHeaderList) {
							String value = objMapResult.get(header) == null ? null
									: String.valueOf(objMapResult.get(header));
							if (StringUtils.isEmpty(value)) {
								// 必填项有一个为空就无效
								emptyRow = true;
								break;
							}
						}
						if (emptyRow) {
							continue;
						}
					}
					listDataResult.add(objMapResult);
				}
			}

			JSONArray jsonDatas = getImportJson(tds, listDataResult);

			Map<String, String> inparaMap = new HashMap<String, String>();
			JSONObject query = tds.getJSONObject("query");
			if (query != null) {
				for (String k : query.keySet()) {
					inparaMap.put(k.toUpperCase(), query.getString(k));
				}
			}
			Map<String, String> checkMap = this.tdsCheckCol(tdsAlias, inparaMap, jsonDatas);
			if (checkMap.size() > 0) {// 校验失败
				info = this.getCheckInfo(checkMap);
			} else {// 校验成功
				// info = saveData(tdataSource.getTdsalias(), tdataSource.getDbTableName(),
				// jsonDatas);
				info = this.saveData(tdataSource, jsonDatas);
			}
			map.put("count", jsonDatas.size() + "");
		} catch (Exception e) {
			info = e.getMessage();

			log.error("数据源导入", e);
		}
		map.put("info", info);
		return map;
	}

	private String getCheckInfo(Map<String, String> checkMap) {
		String s = "";
		for (String k : checkMap.keySet()) {
			String str = checkMap.get(k);
			s += "[" + k + "]匹配失败：" + str.substring(0, str.length() - 1) + ";";
		}
		return s.substring(0, s.length() - 1);

	}

	/**
	 * 数据校验并赋值
	 *
	 * @param tdsAlias
	 * @param jsonDatas
	 * @return
	 */
	private Map<String, String> tdsCheckCol(String tdsAlias, Map<String, String> inparaMap, JSONArray jsonDatas) {
		Map<String, String> map = new HashMap<String, String>();
		try {
			if (StringUtils.isNotEmpty(jsonDatas)) {
				List<TdsoutPara> outList = dsServ.getTDSOutPara(tdsAlias);
				List<TdsoutPara> checkList = new ArrayList<TdsoutPara>();
				if (StringUtils.isNotEmpty(outList)) {
					for (TdsoutPara e : outList) {
						if (StringUtils.isNotEmpty(e.getCallFun())) {
							checkList.add(e);
						}
					}
				}
				if (StringUtils.isNotEmpty(checkList)) {
					Map<String, String> checkMap = new HashMap<String, String>();

					for (int i = 0; i < jsonDatas.size(); i++) {
						JSONObject data = jsonDatas.getJSONObject(i);
						for (TdsoutPara out : checkList) {
							String value = null;
							String sql = out.getCallFun();
							TdsSqlVo sqlVo = this.loadSql(sql, inparaMap, data);
							String key = out.getParaAlias();

							for (Object k : sqlVo.getValues()) {
								if (k != null) {
									key += ":" + k.toString();
								}
							}
							if (checkMap.containsKey(key)) {// map中获取
								value = checkMap.get(key);
							} else {// 数据库获取
								List<LinkedHashMap<String, Object>> cdatas = dao.query(sqlVo.getSql(),
										sqlVo.getValues());
								if (StringUtils.isNotEmpty(cdatas)) {// 有数据
									// 取第一条
									LinkedHashMap<String, Object> dm = cdatas.get(0);
									for (String mk : dm.keySet()) {
										if (dm.get(mk) != null) {
											value = dm.get(mk).toString();
										}
										break;
									}
									checkMap.put(key, value);
								}
							}
							if (value == null) {// 校验失败
								if (StringUtils.isNotEmpty(sqlVo.getValues())) {
									if (!map.containsKey(out.getParaName())) {
										map.put(out.getParaName(), "");
									}
									String s = map.get(out.getParaName());
									s += sqlVo.getValues().toString() + ",";
									map.put(out.getParaName(), s);
								}
							} else {// 校验成功， 赋值
								if (data.containsKey(out.getParaAlias())) {
									data.put(out.getParaAlias(), value);
								} else {
									data.put(out.getParaAlias().toUpperCase(), value);
								}
							}
						}
					}

				}
			}

		} catch (Exception e) {
			log.error("数据源导入校验", e);
		}
		return map;

	}

	private Map<String, String> tdsCheckCol(String tdsAlias, JSONArray inpara, JSONArray jsonDatas) {
		Map<String, String> inparaMap = new HashMap<String, String>();
		if (inpara != null) {
			for (int i = 0; i < inpara.size(); i++) {
				JSONObject data = inpara.getJSONObject(i);
				inparaMap.put(data.getString("name").toUpperCase(), data.getString("value"));
			}
		}
		return this.tdsCheckCol(tdsAlias, inparaMap, jsonDatas);
	}

	/**
	 * 解析sql语句
	 *
	 * @param sql
	 * @param data
	 * @return
	 */
	private TdsSqlVo loadSql(String sql, Map<String, String> inParaMap, JSONObject data) {
		// 调用标准Sql文
		TdsSqlVo vo = new TdsSqlVo();
		try {

			Pattern p1 = Pattern.compile("[@][_a-zA-Z][_a-zA-Z0-9]*");
			Matcher m1 = p1.matcher(sql);
			String nsql = m1.replaceAll("?");
			m1 = p1.matcher(sql);

			List<Object> paramsList = new ArrayList<Object>();
			while (m1.find()) {
				String str = m1.group();
				String code = str.substring(1);
				Object value = data.get(code);
				if (value == null) {
					value = data.get(code.toUpperCase());
				}
				if (value == null && StringUtils.isNotEmpty(inParaMap)) {
					value = inParaMap.get(code.toUpperCase());
				}
				paramsList.add(value);

			}
			vo.setSql(nsql);
			vo.setValues(paramsList);
		} catch (Exception e) {
			log.error("", e);
		}
		return vo;
	}

	/**
	 * 获取数据源数据的导入map值
	 *
	 * @param tds 数据源数据对象
	 * @return
	 */
	private static Map<String, Map<String, Object>> getImportMap(JSONObject colprop) {
		Map<String, Map<String, Object>> map = new HashMap<String, Map<String, Object>>();
		if (colprop != null) {
			for (String key : colprop.keySet()) {
				JSONObject col = colprop.getJSONObject(key);
				String alias = col.getString("alias");
				String comType = col.getString("comType");
				if ("checkbox".equals(comType)) {// 复选框
					Map<String, Object> mapData = new HashMap<String, Object>();
					mapData.put("是", 1);
					mapData.put("否", 0);
					mapData.put("", 0);
					map.put(alias, mapData);
					continue;
				} else if ("combo".equals(comType) || "lovcombo".equals(comType)) {// 下拉框
					List<String> keyList = ExcelExport.arrayStr2List(col.getString("defaultKeyScript"));
					List<String> valueList = ExcelExport.arrayStr2List(col.getString("defaultValueScript"));
					if (StringUtils.isNotEmpty(keyList) && StringUtils.isNotEmpty(valueList)
							&& keyList.size() == valueList.size()) {
						Map<String, Object> mapData = new HashMap<String, Object>();
						for (int i = 0; i < keyList.size(); i++) {
							mapData.put(valueList.get(i), keyList.get(i));
						}
						map.put(alias, mapData);
					}
				}

			}
		}
		return map;
	}

	/**
	 * 获得数据源导入的默认值
	 *
	 * @param tds
	 * @return
	 */
	private Map<String, Object> getInsertDefautValue(JSONObject tds) {
		Map<String, Object> map = new HashMap<String, Object>();
		JSONObject query = tds.getJSONObject("query");// 检索条件
		if (query == null || query.keySet() == null) {
			return map;
		}
		String tdsAlias = tds.getString("name");// 数据源别名
		List<TdsinPara> list = dsServ.getTDSInPara(tdsAlias);
		if (StringUtils.isNotEmpty(list)) {
			for (TdsinPara inPara : list) {
				if (inPara.getInsertEdit() != null && inPara.getInsertEdit() == 1) {
					String alias = inPara.getParaAlias();
					if (query.containsKey(alias)) {
						map.put(alias.toUpperCase(), query.get(alias));
					} else {// 解决大小列不一致的问题
						for (String key : query.keySet()) {
							if (alias.equalsIgnoreCase(key)) {
								map.put(alias.toUpperCase(), query.get(key));
								break;
							}
						}
					}
				}
			}
		}
		return map;

	}

	/**
	 * 导入的数据转换为json对象
	 *
	 * @param tds  数据源对象
	 * @param data 导入的数据
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private JSONArray getImportJson(JSONObject tds, List<?> data) {
		JSONArray datas = new JSONArray();
		JSONObject cols = tds.getJSONObject("props").getJSONObject("colprop");// 列字段
		Map<String, String> mapHeader = getImportHeader(cols);
		if (StringUtils.isNotEmpty(mapHeader)) {
			boolean canInsert = false; // 是否允许导入数据时添加记录
			if (tds.getBoolean("canInsert")) {
				canInsert = true;
			}
			Map<String, Map<String, Object>> valueMap = getImportMap(cols);
			Map<String, Object> insertDefautlvalueMap = getInsertDefautValue(tds);// 插入的默认值
			for (Object obj : data) {
				if (obj != null) {
					Map<String, String> map = (Map<String, String>) obj;
					JSONObject jobj = new JSONObject();
					boolean isInsert = false;// 是否为添加数据
					for (String header : mapHeader.keySet()) {
						String colname = mapHeader.get(header);// 字段名
						if (StringUtils.isNotEmpty(colname)) {
							Object value = map.get(header);
							Object keyValue = null;
							if (StringUtils.isNotEmpty(valueMap) && value != null) {// 显示值对应的key
								Map<String, Object> mapData = valueMap.get(colname);
								if (mapData != null && mapData.get(value) != null) {
									keyValue = mapData.get(value);
								}
							}
							if (keyValue != null) {
								jobj.put(colname.toUpperCase(), keyValue);
							} else {
								jobj.put(colname.toUpperCase(), value);
							}
						}
					}

					//主键赋值，兼容大、小写
					if (map.containsKey(PK.toUpperCase())) {
						jobj.put(PK.toUpperCase(), map.get(PK.toUpperCase()));
					} else if (map.containsKey(PK.toLowerCase())){
						jobj.put(PK.toUpperCase(), map.get(PK.toLowerCase()));
					}

					if (StringUtils.isEmpty(jobj.getString(PK.toUpperCase()))) {
						jobj.put(PK.toUpperCase(), "");
						isInsert = true;
					}
					if (canInsert && isInsert && StringUtils.isNotEmpty(insertDefautlvalueMap)) {
						for (String key : insertDefautlvalueMap.keySet()) {// 添加默认值
							Object val = jobj.get(key);
							if (val == null || val.toString().length() == 0) {// Excel导入的是空值才添加
								jobj.put(key, insertDefautlvalueMap.get(key));
							}
						}
					}
					if (canInsert) {// 允许插入记录
						datas.add(jobj);
					} else {// 不允许插入记录
						if (!isInsert) {
							datas.add(jobj);
						}
					}
				}
			}
		}
		return datas;
	}

	/**
	 * 获取导入的Excel标题对应的字段
	 *
	 * @param tds
	 * @param cols
	 * @return
	 */
	private Map<String, String> getImportHeader(JSONObject cols) {
		Map<String, String> map = new HashMap<String, String>();
		for (String key : cols.keySet()) {
			JSONObject col = cols.getJSONObject(key);
			if (ExcelExport.isTdsHiddenCol(col)) {
				continue;
			}
			if (!ExcelImport.isTdsImportColumn(col.getString("comType"))) {
				continue;
			}
			String alias = col.getString("alias");
			if (PK.equalsIgnoreCase(alias)) {
				map.put(PK, PK);// 主键
			} else {
				String header = col.getString("header");
//				if (StringUtils.isNotEmpty(header) && header.contains("_")) {
//					int lastIndex = header.lastIndexOf("_");
//					header = header.substring(lastIndex + 1);
//				}
				map.put(header, alias);
			}
		}
		map.put(PK, PK);// 主键
		return map;
	}

	/**
	 * 获得数据源表头行数
	 *
	 * @param tdsInfo
	 * @return
	 */
	@Override
	public int getTdsHeaderRow(String tdsInfo) {
		int row = 1;
		JSONObject tds = JSON.parseObject(tdsInfo);
		int headerLevelCount = ExcelExport.tdsSpanHeaderLevel(tds); // 合并表头层级
		if (headerLevelCount > 0) {
			row = headerLevelCount;
		}
		return row;
	}

	@Override
	public String reExtractData(TdsQueryDto param) {
		Map<String, Object> map = _reExtractData(param, null);
		return map.get("error").toString();
	}

	/**
	 * 数据源编辑功能重新提取数据
	 *
	 * @param param
	 * @return
	 */
	@Override
	public Map<String, Object> reExtractData(TdsQueryDto param, TdataSource tds) {
		return _reExtractData(param, tds);
	}

	/**
	 * 数据源编辑功能重新提取数据
	 *
	 * @param param
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private Map<String, Object> _reExtractData(TdsQueryDto param, TdataSource tds) {
		Map<String, Object> rtnMap = new HashMap<String, Object>();
		TDataSourceManager tsm = new TDataSourceManager();
		String strRtn = "";
		param.setPageSize(0);
		String tdsAlias = param.getTdsAlias();// 数据源别名
		JSONArray json = dsServ.getTDSData(param);

		rtnMap.put("data", json);

		if (tds == null) {
			tds = tsm.getTdataSource(param.getTdsAlias());// TdataSource tds = dsServ.getTDataSource(tdsAlias);
		}

		List<TdsoutPara> listOutPara = tsm.getOutParaList(tdsAlias);// dsServ.getTDSOutPara(tdsAlias);
		String bindTdsAlias = "";// 绑定的数据源别名
		Integer initType = 2;// 绑定类型
		String tableName = "";
		if (tds != null) {
			bindTdsAlias = tds.getBindTdsAlias();
			initType = tds.getTdsInitType() == null ? 2 : tds.getTdsInitType();
			tableName = tds.getDbTableName();
		}
		if (initType == 2) {// sql语句不支持重新提取功能
			strRtn = "自定义初始化语句不支持重新获取数据功能，请将初始化类型选择为数据源";
		} else {
			if (bindTdsAlias == null || bindTdsAlias.length() <= 0) {
				strRtn = "未绑定初始化数据源，不能提取到数据！";
			} else {
				List<TdsEditTdsBindParam> listEditBindInParam = dsServ.getEditTdsBindParamData(tdsAlias, bindTdsAlias,
						1, false);
				List<TdsEditTdsBindParam> listEditBindOutParam = dsServ.getEditTdsBindParamData(tdsAlias, bindTdsAlias,
						2, false);
				if (StringUtils.isNotEmpty(listEditBindOutParam)) {// 没有绑定输出参数
					List<String> bindKeyList = new ArrayList<String>();
					List<String> targetKeyList = new ArrayList<String>();
					for (int i = 0; i < listEditBindOutParam.size(); i++) {
						TdsEditTdsBindParam temp = listEditBindOutParam.get(i);
						int isKey = temp.getIsKey() == null ? 0 : temp.getIsKey();
						if (isKey == 1) {
							bindKeyList.add(temp.getBindtdsparamalias());
							targetKeyList.add(temp.getTargettdsparamalias());
						}
					}
					if (StringUtils.isEmpty(targetKeyList)) {
						strRtn = "未设置数据唯一列属性，不能提取数据，请在绑定参数中设置";
					} else {
						String inParaStr = param.getInParaAlias();
						LinkedHashMap<String, TRow> bindTdsData = getBindTdsData(tdsAlias, bindTdsAlias,
								listEditBindInParam, bindKeyList, inParaStr);
						try {
							if (json != null && json.size() > 0) {
								for (int i = 0; i < json.size(); i++) {
									JSONObject jsonData = json.getJSONObject(i);
									JSONArray data = jsonData.getJSONArray("data");// 获取数据
									if (bindTdsData == null || bindTdsData.size() <= 0) {
										strRtn = "没有可以提取的数据";
									} else {
										if (data != null && data.size() > 0) {
											LinkedHashMap<String, JSONObject> tdsDataMap = getTdsDataMap(data,
													targetKeyList);
											// boolean bln = true;
											Map<String, Object> saveMap = saveReExtractData(tdsDataMap, bindTdsData,
													targetKeyList, listEditBindOutParam, tableName, listOutPara,
													bindKeyList);

											int r = (Integer) saveMap.get("result");

											int insertRow = 0;// 添加记录数
											if (saveMap.containsKey("insertrow")) {
												insertRow = (Integer) saveMap.get("insertrow");// 添加记录数
											}
											// Integer updateRow = saveMap.get("updaterow");// 修改记录数
											if (insertRow > 0) {// 有添加数据时需要重新加载数据
												rtnMap.remove("data");
											} else {
												Object updateData = saveMap.get("updateDataMap");
												if (updateData != null) {
													Map<String, Map<String, Object>> updateDataMap = (Map<String, Map<String, Object>>) updateData;
													for (int n = 0; n < data.size(); n++) {
														JSONObject obj = data.getJSONObject(n);
														String key = obj.getString(PK);
														Map<String, Object> val = updateDataMap.get(key);
														if (val != null) {
															obj.putAll(val);
														}
													}
													rtnMap.put("data", json);
												}
											}
											if (r == 0) {
												strRtn = "数据重新提取失败，请联系管理员";
											}
										} else {
											strRtn = "数据未初始化，不需要重新提取";
										}
									}
								}
							}
						} catch (Exception e) {
							log.error("", e);
							log.info("数据源结果json转换出错：" + param.getTdsAlias());
							log.info(json);
						}
					}
				} else {
					strRtn = "未设置绑定参数，不能重新提取数据";
				}

			}
		}
		rtnMap.put("error", strRtn);
		return rtnMap;
	}

	@SuppressWarnings("unchecked")
	private Map<String, Object> saveReExtractData(LinkedHashMap<String, JSONObject> tdsDataMap,
			LinkedHashMap<String, TRow> bindTdsData, List<String> targetKeyList,
			List<TdsEditTdsBindParam> listEditBindOutParam, String tableName, List<TdsoutPara> listOutPara,
			List<String> bindKeyList) {

		Map<String, Object> rtnMap = new HashMap<String, Object>();

		LinkedHashMap<String, TdsoutPara> mapOutPara = new LinkedHashMap<String, TdsoutPara>();
		Map<String, Integer> mapTypes = new HashMap<String, Integer>();

		if (StringUtils.isNotEmpty(listOutPara)) {
			for (TdsoutPara temp : listOutPara) {
				String datatype = "";
				if (temp.getDataType() != null) {
					datatype = temp.getDataType().toString();
				}
				mapTypes.put(temp.getParaAlias(), TdsTools.getArgType(datatype));
				mapOutPara.put(temp.getParaAlias(), temp);
			}
		}
		List<LinkedHashMap<String, Object>> listInsertEnd = new ArrayList<LinkedHashMap<String, Object>>();
		String updateSql = "";
		List<Object[]> batchArgsList = new ArrayList<Object[]>();
		Map<String, Map<String, Object>> updateDataMap = new HashMap<String, Map<String, Object>>();
		updateSql = this.updateReExtractData(listEditBindOutParam, tableName, mapOutPara);
		int columnCount = getUpdateColumnCount(listEditBindOutParam, mapOutPara) + 1;
		int[] argTypes = getArgType(listEditBindOutParam, mapOutPara, columnCount);
		for (Entry<String, TRow> entry : bindTdsData.entrySet()) {
			int dataType = 1;// 1：添加，2：修改
			JSONObject dataObj = new JSONObject();
			if (tdsDataMap == null || tdsDataMap.size() <= 0) {// 如果未初始化过数据，进入插入操作
				dataType = 1;
			} else {
				if (tdsDataMap.containsKey(entry.getKey())) {// 数据已存在
					dataType = 2;
					dataObj = tdsDataMap.get(entry.getKey());
				} else {// 数据不存在，没有添加过，进行插入数据操作
					dataType = 1;
				}
			}
			if (dataType == 1) {
				List<LinkedHashMap<String, Object>> listInsert = new ArrayList<LinkedHashMap<String, Object>>();
				listInsert = insertReExtractData(listEditBindOutParam, tableName, entry.getValue(), listOutPara);
				listInsertEnd.addAll(listInsert);
			} else {// 重新提取数据进行更新操作
				HashMap<String, Object> argMap = getBatchArgs(entry.getValue(), listEditBindOutParam, tableName,
						mapOutPara, dataObj, columnCount);
				Object[] batchArgsObj = (Object[]) argMap.get("args");
				Map<String, Object> dmap = (Map<String, Object>) argMap.get("data");
				updateDataMap.put(dmap.get(PK).toString(), dmap);
				batchArgsList.add(batchArgsObj);
			}
		}
		// 添加数据
		List<String> listSql = new ArrayList<String>();
		List<Object[]> listValue = new ArrayList<Object[]>();
		List<Integer> listTypes = null;
		boolean blnAdd = true;
		// 获取绑定的输出参数
		HashMap<String, TdsEditTdsBindParam> mapEditBindOutParam = new HashMap<String, TdsEditTdsBindParam>();
		if (StringUtils.isNotEmpty(listEditBindOutParam)) {
			for (TdsEditTdsBindParam temp : listEditBindOutParam) {
				mapEditBindOutParam.put(temp.getTargettdsparamalias(), temp);
			}
		}
		if (listInsertEnd != null && listInsertEnd.size() > 0) {
			for (LinkedHashMap<String, Object> record : listInsertEnd) {
				Map<String, Object> map = TdsTools.getInitInsert(tableName, record, mapTypes, mapEditBindOutParam);
				if (StringUtils.isNotEmpty(map)) {
					String s = map.get("sql").toString();
					List<Object> v = (List<Object>) map.get("value");
					listSql.add(s);
					listValue.add(v.toArray());
					if (listTypes == null) {
						listTypes = (List<Integer>) map.get("type");
					}
				}
			}
			int row = 0;
			if (listSql.size() > 0 && listTypes != null) {
				row = insertBatch(listSql.get(0), listValue, listTypes);
				rtnMap.put("insertrow", row);
			}
			if (row <= 0) {
				blnAdd = false;
			}
		}
		boolean blnUp = true;
		if (StringUtils.isNotEmpty(batchArgsList)) {
			int row = dao.batchSqlUpdate(updateSql, batchArgsList, argTypes, 500);
			rtnMap.put("updaterow", row);
			if (row <= 0) {
				blnUp = false;
			}
		}
		// 修改数据
		boolean result = true;
		if (blnAdd && blnUp) {
			result = true;
		} else {
			result = false;
		}
		rtnMap.put("result", result ? 1 : 0);
		if (StringUtils.isNotEmpty(updateDataMap)) {
			rtnMap.put("updateDataMap", updateDataMap);
		}
		return rtnMap;
	}

	/**
	 * 重新提取数据的更新语句
	 *
	 * @param listEditBindOutParam
	 * @param tableName
	 * @return
	 */
	private String updateReExtractData(List<TdsEditTdsBindParam> listEditBindOutParam, String tableName,
			LinkedHashMap<String, TdsoutPara> mapOutPara) {
		String result = " update " + tableName + " set ";
		HashMap<String, String> map = new HashMap<String, String>();
		int isfirst = 0;
		for (int i = 0; i < listEditBindOutParam.size(); i++) {
			TdsEditTdsBindParam temp = listEditBindOutParam.get(i);
			String colAlias = temp.getTargettdsparamalias();
			if (mapOutPara.containsKey(colAlias)) {
//				String comType = mapOutPara.get(colAlias).getComType() == null ? "": mapOutPara.get(colAlias).getComType();
				if (temp.getIsInitData() != null && temp.getIsInitData() == 0) {

				} else {
					if (!"ID".equals(colAlias)) {
						if (map.containsKey(colAlias.toLowerCase())) {

						} else {
							if (isfirst == 0) {
								result += colAlias + "=?";
								isfirst = 1;
							} else {
								result += "," + colAlias + "=?";
							}
							map.put(colAlias.toLowerCase(), colAlias);
							String colAliasOld = colAlias + "_OLD";
							if (temp.getIsSaveOld() != null && temp.getIsSaveOld() == 1) {
								result += "," + colAliasOld + "=?";
								map.put(colAliasOld.toLowerCase(), colAliasOld);
							}
						}
					}
				}
			}
		}
		result += " where ID=?";
		return result;
	}

	/**
	 * 重新提取数据 的值
	 *
	 * @param tr
	 * @param listEditBindOutParam
	 * @param tableName
	 * @param mapOutPara
	 * @param targetKeyList
	 * @param bindKeyList
	 * @return
	 */
	private HashMap<String, Object> getBatchArgs(TRow tr, List<TdsEditTdsBindParam> listEditBindOutParam,
			String tableName, LinkedHashMap<String, TdsoutPara> mapOutPara, JSONObject dataObj, int columnCount) {
		HashMap<String, Object> returnMap = new HashMap<String, Object>();

		HashMap<String, String> map = new HashMap<String, String>();
		HashMap<String, Object> dataMap = new HashMap<String, Object>();

		// HashMap<String, String> return = new HashMap<String, String>();

		Object[] result = new Object[columnCount];
		int k = 0;
		String dataId = dataObj.getString(PK);
		for (int i = 0; i < listEditBindOutParam.size(); i++) {
			TdsEditTdsBindParam temp = listEditBindOutParam.get(i);
			String colAlias = temp.getTargettdsparamalias();
			String bindColAlias = temp.getBindtdsparamalias();
			if (bindColAlias == null || bindColAlias.length() <= 0) {
				bindColAlias = colAlias;
			}
			if (temp.getIsInitData() == null || temp.getIsInitData() == 1) {
				if (mapOutPara.containsKey(colAlias)) {
//					String comType = mapOutPara.get(colAlias).getComType() == null ? "": mapOutPara.get(colAlias).getComType();
//					if(comType.length()<=0) {//未设置组件类型的不重新提取数据
//
//					}else {
					if (!"ID".equals(colAlias)) {
						if (map.containsKey(colAlias.toLowerCase())) {

						} else {
							String colAliasOld = colAlias + "_OLD";
							if (temp.getIsSaveOld() != null && temp.getIsSaveOld() == 1) {// 保留历史数据
								String OldData = "";// 历史值
								if (dataObj.get(colAliasOld) != null) {
									OldData = dataObj.get(colAliasOld).toString();
								}
								String nowData = "";// 初始化后的值
								if (dataObj.get(colAlias) != null) {
									nowData = dataObj.get(colAlias).toString();
								}
								if (OldData.equals(nowData)) {// 未修改过数据，更新
									result[k] = tr.getByAlias(bindColAlias);
								} else {// 修改过数据，保留修改的结果
									result[k] = dataObj.get(colAlias);
								}
								map.put(colAlias.toLowerCase(), colAlias);
								dataMap.put(colAlias, result[k]);
								k = k + 1;
								result[k] = tr.getByAlias(bindColAlias);
								map.put(colAliasOld.toLowerCase(), colAliasOld);
								dataMap.put(colAliasOld, result[k]);
							} else {
								result[k] = tr.getByAlias(bindColAlias);
								map.put(colAlias.toLowerCase(), colAlias);
								dataMap.put(colAlias, result[k]);
							}
							k = k + 1;
						}
					}
//					}
				}
			}
		}
		result[k] = dataId;
		dataMap.put(PK, dataId);

		returnMap.put("data", dataMap);
		returnMap.put("args", result);

		return returnMap;
	}

	/**
	 * 返回字段的类型
	 *
	 * @param listEditBindOutParam
	 * @param mapOutPara
	 * @return
	 */
	@Override
	public int[] getArgType(List<TdsEditTdsBindParam> listEditBindOutParam,
			LinkedHashMap<String, TdsoutPara> mapOutPara, int columnCount) {
		int[] result = new int[columnCount];
		HashMap<String, String> map = new HashMap<String, String>();
		int k = 0;
		for (int i = 0; i < listEditBindOutParam.size(); i++) {
			TdsEditTdsBindParam temp = listEditBindOutParam.get(i);
			String colAlias = temp.getTargettdsparamalias();
			if (mapOutPara.containsKey(colAlias)) {
				TdsoutPara obj = new TdsoutPara();
				obj = mapOutPara.get(colAlias);
//				String comType = obj.getComType() == null ? "" : obj.getComType();
				String dataType = obj.getDataType() == null ? "tdsString" : obj.getDataType();
				if (temp.getIsInitData() != null && temp.getIsInitData() == 0) {

				} else {
					if (!"ID".equals(colAlias)) {
						if (map.containsKey(colAlias.toLowerCase())) {

						} else {
							map.put(colAlias.toLowerCase(), colAlias);
							String colAliasOld = colAlias + "_OLD";
							result[k] = TdsTools.getArgType(dataType);
//							if ("tdsString".equals(dataType)) {
//								result[k] = Types.VARCHAR;
//							} else {
//								result[k] = Types.FLOAT;
//							}
							if (temp.getIsSaveOld() != null && temp.getIsSaveOld() == 1) {

								if (mapOutPara.containsKey(colAliasOld.toLowerCase())) {

								} else {
									k = k + 1;
									result[k] = TdsTools.getArgType(dataType);
									// if ("tdsString".equals(dataType)) {
									// result[k] = Types.VARCHAR;
									// } else {
									// result[k] = Types.FLOAT;
									// }
									map.put(colAliasOld.toLowerCase(), colAliasOld);
								}
							}

						}
						k = k + 1;
					}
				}
			}
		}
		// where 条件中的ID 字段
		result[k] = Types.VARCHAR;
		for (int m = 0; m < result.length; m++) {
			if (result[m] == 0) {
				result[m] = Types.VARCHAR;
			}
		}
		return result;
	}

	/**
	 * 返回需要更新的字段数量
	 *
	 * @param listEditBindOutParam
	 * @param mapOutPara
	 * @return
	 */
	@Override
	public int getUpdateColumnCount(List<TdsEditTdsBindParam> listEditBindOutParam,
			LinkedHashMap<String, TdsoutPara> mapOutPara) {
		int result = 0;
		HashMap<String, String> map = new HashMap<String, String>();
		for (int i = 0; i < listEditBindOutParam.size(); i++) {
			TdsEditTdsBindParam temp = listEditBindOutParam.get(i);
			String colAlias = temp.getTargettdsparamalias();
			if (mapOutPara.containsKey(colAlias)) {
				// TdsoutPara obj = mapOutPara.get(colAlias);
//				String comType = obj.getComType() == null ? "" : obj.getComType();
				if (temp.getIsInitData() != null && temp.getIsInitData() == 0) {

				} else {
					if (!"ID".equals(colAlias)) {
						if (map.containsKey(colAlias.toLowerCase())) {

						} else {
							map.put(colAlias.toLowerCase(), colAlias);
							String colAliasOld = colAlias + "_OLD";
							if (temp.getIsSaveOld() != null && temp.getIsSaveOld() == 1) {
								if (mapOutPara.containsKey(colAliasOld.toLowerCase())
										&& mapOutPara.get(colAliasOld.toLowerCase()).getVisible() == 1) {

								} else {
									result = result + 1;
									map.put(colAliasOld.toLowerCase(), colAliasOld);
								}
							}
						}
						result = result + 1;
					}
				}
			}
		}
		return result;
	}

	/**
	 * 生成插入的数据
	 *
	 * @param listEditBindOutParam
	 * @param tableName
	 * @param tr
	 * @param listOutPara
	 * @return
	 */
	private List<LinkedHashMap<String, Object>> insertReExtractData(List<TdsEditTdsBindParam> listEditBindOutParam,
			String tableName, TRow tr, List<TdsoutPara> listOutPara) {
		List<LinkedHashMap<String, Object>> listData = new ArrayList<LinkedHashMap<String, Object>>();
		LinkedHashMap<String, TdsEditTdsBindParam> mapBindOutParam = new LinkedHashMap<String, TdsEditTdsBindParam>();
		if (StringUtils.isNotEmpty(listEditBindOutParam)) {
			for (TdsEditTdsBindParam temp : listEditBindOutParam) {
				mapBindOutParam.put(temp.getTargettdsparamalias(), temp);
			}
		}
//		List<String> listColumnName = new ArrayList<String>();
		if (StringUtils.isNotEmpty(listOutPara)) {// 有输出参数
			LinkedHashMap<String, Object> map = new LinkedHashMap<String, Object>();
			for (int j = 0; j < listOutPara.size(); j++) {
				TdsoutPara temp = listOutPara.get(j);
				String colAlias = temp.getParaAlias();
				if (!"ID".equals(colAlias)) {// 数据唯一主键，不需要初始化，系统自动生成
					Object value = new Object();
					if (mapBindOutParam != null && mapBindOutParam.containsKey(colAlias)) {// 获取到了绑定参数对照的字段别名
						value = tr.getByAlias(mapBindOutParam.get(colAlias).getBindtdsparamalias());
//						int isSaveOld = mapBindOutParam.get(colAlias).getIsSaveOld() == null ? 0
//								: mapBindOutParam.get(colAlias).getIsSaveOld();
//						if (isSaveOld == 1) {
//							listColumnName.add(colAlias + "_OLD");
//							map.put(colAlias + "_OLD", value);
//						}
					} else {// 未获取到对照的别名，直接用输出参数的别 名
						value = tr.getByAlias(colAlias);
					}
					map.put(colAlias, value);
				}
			}
			listData.add(map);
//			}
		}
		return listData;
	}

	/**
	 * 检索数据源，做成 Map，用来判断数据是否存在
	 *
	 * @param data
	 * @param keyList
	 * @return
	 */
	private LinkedHashMap<String, JSONObject> getTdsDataMap(JSONArray data, List<String> keyList) {
		LinkedHashMap<String, JSONObject> mapRtn = new LinkedHashMap<String, JSONObject>();
		if (data != null && data.size() > 0) {
			for (int i = 0; i < data.size(); i++) {
				JSONObject jsonObj = data.getJSONObject(i);
				String key = "";
				for (int j = 0; j < keyList.size(); j++) {
					String paramAlias = keyList.get(j);
					String value = jsonObj.getString(paramAlias);
					if (value == null) {
						key = "";
						j = keyList.size();
					} else {
						if (key.length() <= 0) {
							key = value;
						} else {
							key = key + "_" + value;
						}
					}
				}
				if (key.length() > 0) {
					mapRtn.put(key, jsonObj);
				}
			}
		}
		return mapRtn;
	}

	/**
	 *
	 * @param tdsAlias            源数据源别名
	 * @param bindTdsAlias        绑定数据源别名
	 * @param listEditBindInParam 绑定的输入参数
	 * @param keyList
	 * @return
	 */
	private LinkedHashMap<String, TRow> getBindTdsData(String tdsAlias, String bindTdsAlias,
			List<TdsEditTdsBindParam> listEditBindInParam, List<String> keyList, String inParaStr) {
		List<TdsinPara> listBindInPara = new ArrayList<TdsinPara>();
		listBindInPara = SpringUtils.getBean(IDataSourceService.class).getListTdsInPara(bindTdsAlias);
		// 获取绑定的输入参数（设置了对照的参数）
//		List<TdsEditTdsBindParam> listEditBindInParam = SpringUtils.getBean(IDataSourceService.class).getEditTdsBindParamData(tdsAlias, bindTdsAlias, 1, false);
		HashMap<String, TdsEditTdsBindParam> mapEditBindInParam = new HashMap<String, TdsEditTdsBindParam>();
		if (StringUtils.isNotEmpty(listEditBindInParam)) {
			for (TdsEditTdsBindParam temp : listEditBindInParam) {
				mapEditBindInParam.put(temp.getBindtdsparamalias(), temp);
			}
		}
		// 获取主数据的输入参数及值
		HashMap<String, Object> mapInParam = new HashMap<String, Object>();
		JSONArray inParaArr = JSONArray.parseArray(inParaStr);
		if (inParaArr != null && inParaArr.size() > 0) {
			for (int j = 0; j < inParaArr.size(); j++) {
				JSONObject dataObj = inParaArr.getJSONObject(j); // JSONObject.fromObject(dataArray.get(j));
				String paraAlias = dataObj.getString("name");// 参数
				Object oValue = dataObj.get("value"); // 参数值
				mapInParam.put(paraAlias, oValue);
			}
		}
		LinkedHashMap<String, TRow> mapRtn = new LinkedHashMap<String, TRow>();
		IDataSource ids = null;
		try {
			TDataSourceManager dsm = new TDataSourceManager();
			ids = dsm.getDataSource(bindTdsAlias, false);
			if (StringUtils.isNotEmpty(listBindInPara)) {// 如果数据源有输入参数
				for (TdsinPara temp : listBindInPara) {
					if (mapEditBindInParam.containsKey(temp.getParaAlias())) {// 存在绑定关系
						String mainInParamAlias = mapEditBindInParam.get(temp.getParaAlias()).getTargettdsparamalias();
						if (mapInParam.containsKey(mainInParamAlias)) {// 在主数据的输入参数中找到了对应的参数
							Object value = mapInParam.get(mainInParamAlias);
							ids.setInParaByAlias(temp.getParaAlias(), value);
						}
					}
				}
			}
			ids.load();
			if (ids != null && ids.getRowCount() > 0) {
				for (int i = 0; i < ids.getRowCount(); i++) {
					TRow tr = ids.get(i);
					String key = "";
					for (int j = 0; j < keyList.size(); j++) {
						String paramAlias = keyList.get(j);
						Object value = new Object();
						value = tr.getByAlias(paramAlias);
						if (value == null) {
							key = "";
							j = keyList.size();
						} else {
							String valueStr = value.toString();
							if (key.length() <= 0) {
								key = valueStr;
							} else {
								key = key + "_" + valueStr;
							}
						}
					}
					if (key.length() > 0) {
						mapRtn.put(key, tr);
					}
				}
			}
		} catch (Exception e) {
			log.error(this.hashCode() + " 执行初始化数据时，加载初始化的数据源时发生错误:", e);
//			setErrorInfo("执行初始化数据时，加载初始化的数据源时发生错误[" + bindTdsAlias + "]:" + e.getMessage());
		}
		return mapRtn;
	}

	/**
	 * 保存台账数据到MongoDB
	 *
	 * @param collectionName
	 * @param datas
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	private String saveAccountData2MongoDB(String collectionName, String tdsAlias, JSONArray datas) {
		String rtn = "";
		if (datas == null || datas.size() == 0) {
			return "无保存数据！";
		}
		try {
			rtn = this.saveCheck(datas);
			if (StringUtils.isNotEmpty(rtn)) {
				return rtn;
			}

			TdsAccountTime timeConf = accountServ.getTimeConf(tdsAlias);// 时间配置

			/*
			 * 保存逻辑 1 获取MongoDB数据，整理map，key为仪表位号+时间标识 2 循环数据，无数据为插入，有数据为更新 3
			 * 批量保存，插入生成唯一ID，更新根据原唯一ID进行更新
			 */

			// 以数据源为单位，获取数据 TODO 增加检索条件过滤
			Map<String, Map> valMap = new HashMap<String, Map>();

			List<Date> timeList = getStartEndTime(datas);
			Date start = timeList.get(0);
			Date end = timeList.get(1);

			JSONArray insertData = new JSONArray();
			JSONArray updateData = new JSONArray();
//			List<String> deleteIds = new ArrayList<String>();

			Date currTime = DateTimeUtils.getNowDate();
			String currTimeStr = DateTimeUtils.formatDateTime(currTime);
			SysUser user = SysUserHolder.getCurrentUser();
			//外委人员
			List<EmployeeVo> userPartTimePost = sysEmployeeInfoService.getUserPartTimePost(user.getId());
			String orgId = "";
			if (StringUtils.isNotEmpty(userPartTimePost)) {
				EmployeeVo partTimePost = userPartTimePost.stream().filter(item -> item.getOrgcode().equals(user.getOrgId())).findFirst().orElse(null);
				if (partTimePost != null) {
					orgId = partTimePost.getOrgcode();
				}else{
					orgId = user.getOrgId();
				}
			} else {
				orgId = user.getOrgId();
			}
			// --台账输出列为对应仪表位号或时间，不另外整理输出参数信息,根据输入参数的时间排序
			// 输出参数重新整理并获取,根据要保存的数据，获取对应的实时数据，如果有变化进行保存，无变化不保存（如果保存的数据为空字符----认为无效，更新此数据为查不到的数据）
			// 获取动态输出参数， 根据输出参数和要保存的时间获取数据进行对比
//			List<TOutPara> outList = getAccountOutList(tdsAlias);

			List<String> customColList = getCustomCol(tdsAlias); // new ArrayList<String>();//获取自定义输出非仪表列，用于保存
//			List<String> customWriteColList = getCustomWriteCol(customColList); // 获取可手工录入的自定义输出非仪表列，用于保存

			// 根据仪表ID获取仪表位号进行保存
			Map<String, String> tagIdCodeMap = new HashMap<String, String>();
			List<String> tagList = new ArrayList<String>();
			Map<String, String> infomap = new HashMap<String, String>();
			if (StringUtils.isNotEmpty(datas)) {
				JSONObject obj = datas.getJSONObject(0);// 行数据
				String type = obj.getString(this.typeCol);
				if ("1".equals(type) || "2".equals(type)) {// 统计/方案上下限行不处理
				} else {
					List<String> listCols = this.getCols(obj);
					for (String col : listCols) {
						if (!col.equalsIgnoreCase(PK) && !col.equalsIgnoreCase(typeCol)
								&& !col.equalsIgnoreCase(ulinfoCol) && !col.equalsIgnoreCase(timeCol)
								&& !col.equalsIgnoreCase(xCol) && !col.equalsIgnoreCase(infoCol)
								&& !col.equalsIgnoreCase("rowConfirm") && !tagList.contains(col)
								&& !col.equalsIgnoreCase(ulobjCol)) {
							tagList.add(col);
						} else if (col.equalsIgnoreCase(infoCol)) {
							// "_accountInfoCol":
							// "unitCode@ZQF7SXAB507E41WFMV1259;bc@ZQH44HFO4051TSN0XB0976;sj@2024-04-10
							// 09:00:00,2024-04-10 20:30:00;rq@2024-04-10;accountId@ZQGFXA1AB068A05MA50867",
							String info = obj.getString(col);
							if (StringUtils.isNotEmpty(info)) {
								List<String> tlist = Coms.StrToList(info, ";");
								for (String s : tlist) {
									List<String> tlist2 = Coms.StrToList(s, "@");
									if (tlist2.size() > 1) {
										String key = tlist2.get(0);
										String val = tlist2.get(1);
										if ("sj".equals(key)) {
											List<String> sxb = Coms.StrToList(val, ",");
											if (sxb.size() > 1) {
												infomap.put("sbsj", sxb.get(0));
												infomap.put("xbsj", sxb.get(1));
											}
										} else if ("rq".equals(key)) {
											infomap.put("rq", val);
										} else if ("unitCode".equals(key)) {
											infomap.put("unitCode", val);
										} else if ("sendUnitCode".equals(key)) {
											infomap.put("sendUnitCode", val);
										} else if ("bc".equals(key)) {
											infomap.put("bc", val);
										} else if ("accountId".equals(key)) {
											infomap.put("accountId", "null".equalsIgnoreCase(val) ? "" : val);
										} else if ("formDataId".equals(key)) {
											infomap.put("formDataId", "null".equalsIgnoreCase(val) ? "" : val);
										}
									}
								}
							}
						}
					}
				}
			}
			if (StringUtils.isNotEmpty(tagList)) {
				tagIdCodeMap = accountServ.getTagIdCodeMap(tagList);
			}
			String sbsj = infomap.get("sbsj");
			String xbsj = infomap.get("xbsj");
			String rq = infomap.get("rq");
			String shiftCode = infomap.get("bc");
			String unitCode = infomap.get("unitCode");
			String sendUnitCode = infomap.get("sendUnitCode");
			String accountId = infomap.get("accountId");
			String formDataId = infomap.get("formDataId");

			Boolean extOp = false;
			if ("mobile".equals(timeConf.getShowMode())) {// 如果外部模式，判断活动模式，如果巡检模式，自动转换成时间点模式
				Map<String, String> confMap = applySrv.getUnitAccountConf(sendUnitCode, tdsAlias);
				if (confMap != null && "0".equals(confMap.get("mode"))) {
					timeConf.setShowMode("tz");
				} else {
					extOp = true;
				}
			}

			List<Map> mdata = null;
			List<Map> newdata = null;
			if (!extOp) {// 非移动端数据，使用MongoDB
				if (start != null && end != null && StringUtils.isNotEmpty(tagList)) {
					newdata = accountServ.getAccountNewData(this.accountNewTab + "_" + tdsAlias, 1, rq, unitCode,
							shiftCode, accountId);
					if (StringUtils.isNotEmpty(newdata)) {
						List<Map> tmap = new ArrayList<Map>();
						Object info = newdata.get(0).get("info");
						if (info != null) {
							tmap.addAll((List<Map>) info);
						}
						if (StringUtils.isNotEmpty(tmap)) {
							mdata = tmap;
						}
					}

					if (newdata == null) {
						mdata = accountServ.getAccountSaveData(this.accountTabName + "_" + tdsAlias, tdsAlias,
								start.getTime(), end.getTime(), unitCode, shiftCode, accountId);
					}
					if (mdata == null || mdata.size() == 0) {
						mdata = accountServ.getAccountSaveData(this.accountTabName + "_" + tdsAlias, tdsAlias,
								start.getTime(), end.getTime(), tagList);
					}
				}
				if (mdata != null) {
					for (Map map : mdata) {
//					String tag = String.valueOf(map.get("tag"));
						String col = String.valueOf(map.get("col"));
						String sj = String.valueOf(map.get("sj"));
						if ("rowConfirm".equals(col)) {
							continue;
						}
						valMap.put(col + "_" + sj, map);
					}
				}
			}

//			Map<String, String> vmap = new HashMap<String, String>();
//			if (datas.size() > 0) {
//				List<String> tagList = new ArrayList<String>();
//				List<Date> dateList = new ArrayList<Date>();
//				for (int i = 0; i < datas.size(); i++) {
//					JSONObject obj = datas.getJSONObject(i);// 行数据
//					List<String> listCols = this.getCols(obj);
//					for (String col : listCols) {
//						if (!col.equalsIgnoreCase(PK) && !col.equalsIgnoreCase("rowConfirm") && !tagList.contains(col)) {
//							tagList.add(col);
//						}
//						String datastr = obj.getString(PK);
//						Date dt = DateTimeUtils.parseDateTime(datastr);
//						if (!dateList.contains(dt)) {
//							dateList.add(dt);
//						}
//					}
//				}
//
//				int istep = 60;
//				if (dateList.size() > 1) {
//					List<Integer> stepList = new ArrayList<Integer>();
//					stepList.add(60);// 默认
//					dateList.sort((o1, o2) -> o1.compareTo(o2));
//					for (int i = 0, il = dateList.size(); i < il; i++) {
//						if (i > 0) {
//							long lstep = DateTimeUtils.diffDate(dateList.get(i), dateList.get(i - 1),
//									DateTimeUtils.MINITE);
//							if (lstep <= 0) {
//								continue;
//							}
//							int _step = Integer.parseInt(String.valueOf(lstep));
//							if (!stepList.contains(_step)) {
//								stepList.add(_step);
//							}
//						}
//					}
//					if (stepList.size() > 1) {
//						int is = stepList.get(0);
//						for (int i = 1, il = stepList.size(); i < il; i++) {
//							is = commonDivisor(is, stepList.get(i));
//						}
//						istep = is;
//					}
//				}
//
//				if (StringUtils.isNotEmpty(tagList) && StringUtils.isNotEmpty(dateList) && dateList.size() > 0) {
//					String st = DateTimeUtils.formatDateTime(dateList.get(0));
//					String et = DateTimeUtils.formatDateTime(dateList.get(dateList.size() - 1));
//					JSONObject jsonObject = rtdbSrv.queryRtdbData(tagList, st, et, istep * 60);// 接口这个间隔是秒
//
//					if (jsonObject != null) {
//						JSONArray meters = JSONArray.parseArray(jsonObject.getString("result"));
//						if (meters != null && meters.size() > 0) {
//							for (int i = 0, il = meters.size(); i < il; i++) {
//								JSONObject meterObj = JSONObject.parseObject(meters.getString(i));
//								String tagCode = meterObj.getString("tagCode");
//
//								JSONArray array = JSONArray.parseArray(meterObj.getString("datas"));
//								if (array != null && array.size() > 0) {
//									for (int j = 0, jl = array.size(); j < jl; j++) {
//										JSONObject obj = JSONObject.parseObject(array.getString(j));
//										String value = obj.getString("value");
//										String dstr = obj.getString("datetime") == null ? ""
//												: obj.getString("datetime").substring(0, 19);
//										vmap.put(dstr + "_" + tagCode, value);
//									}
//								}
//							}
//						}
//					}
//				}
//
//			}

			// 保存时，需记录哪个仪表有数据变化，数据源别名及、时间，调用方法，进行汇总处理
			List<String> tagIdList = new ArrayList<String>();// 变动仪表id统计

			if (extOp || "tz".equals(timeConf.getShowMode()) || "synchronize".equals(timeConf.getShowMode())) {// 外部操作（移动数据同步）
				// 根据id查核算对象名称，根据班次代码查班次名称
				List<Object> param = new ArrayList<Object>();
				String sendUnitName = "";
				String shiftName = "";
				if (extOp || "synchronize".equals(timeConf.getShowMode())) {
					String sql = "select name from costuint where id=?";
					param.add(unitCode);
					List<Map<String, Object>> list = dao.queryListMap(sql, param.toArray());
					if (StringUtils.isNotEmpty(list)) {
						sendUnitName = String.valueOf(list.get(0).get("name"));
					}
					sql = "select SHIFTNAME from SHIFT_MODEL_CLASS where ID=?";
					param.clear();
					param.add(shiftCode);
					list = dao.queryListMap(sql, param.toArray());
					if (StringUtils.isNotEmpty(list)) {
						shiftName = String.valueOf(list.get(0).get("SHIFTNAME"));
					}
				}

				Map<String, TdsAccountTag> getTagIdObjMap = accountServ.getTagIdObjMap(tagList);//采集点信息
				List<AcctobjInputVo> clist = new ArrayList<AcctobjInputVo>();// 相当于行数据（时间点）

				List<String> listCols = new ArrayList<String>();
				if (datas.size() > 0) {
					List<String> tlistCols = this.getCols(datas.getJSONObject(0));// 顺序是乱的，给移动端处理，需要重新排序
					//接口获取移动端采集点信息，进行对照
					ApplyParams p = new ApplyParams();
					p.setRq(rq);
					p.setUnitCode(unitCode);
					p.setItemCode(applySrv.transferUnitCode(unitCode));
					p.setBc(shiftCode);
					p.setSbsj(sbsj);
					p.setXbsj(xbsj);
					p.setTeamId(orgId);
					List<TdsAccountMeter> mlist = applySrv.getExtPotList(p);
					for (TdsAccountMeter tag : mlist) {
						if (tlistCols.contains(tag.getTagid())) {
							listCols.add(tag.getTagid());
						}
					}
				}
				//历史数据
				Map<String, Map<Date, String>> olddatamap = new HashMap<String, Map<Date,String>>();
				getMobileData(olddatamap, sendUnitCode, sbsj, shiftCode);

				List<TdsAccountLog> uploglist = new ArrayList<TdsAccountLog>();
				List<TdsAccountOverinfo> overlist = new ArrayList<TdsAccountOverinfo>();
				//saveChangeLog(1, msgsb.toString(), memo);

				//超限数据
				List<TdsAccountTag> overList = new ArrayList<TdsAccountTag>();

				for (int i = 0; i < datas.size(); i++) {
					JSONObject obj = datas.getJSONObject(i);// 行数据
					String type = obj.getString(this.typeCol);
					if ("1".equals(type) || "2".equals(type)) {// 统计行不处理
						continue;
					}
					Integer flag = obj.getInteger("TDSROW_rowFlag");// 0添加 1修改 -1删除
					String ID = obj.getString(PK);
					String jobInputTime = obj.getString(this.jobInputTime);
					String infoCol = obj.getString(this.infoCol);
					String taskId = getInfoTaskId(infoCol);
					taskId = StringUtils.isEmpty(taskId) ? sendUnitCode : taskId;

					if (flag != 0 && flag != 1 && flag != -1) {// 添加或修改
						continue;
					}
					Map<String, Map<String, String>> infoMap = getInfoMap(obj.getString(this.ulinfoCol));

					AcctobjInputVo vo = new AcctobjInputVo();// 行数据对象
					vo.setRowFlag(flag);
					vo.setInputTime(DateTimeUtils.parseDateTime(ID));
					vo.setBaId(sendUnitCode);
					vo.setBaName(sendUnitName);
					vo.setBcdm(shiftCode);
					vo.setBcmc(shiftName);
					vo.setSbsj(DateTimeUtils.parseDateTime(sbsj));
					vo.setXbsj(DateTimeUtils.parseDateTime(xbsj));
					vo.setTaskId(taskId);
					vo.setTmused(1);
					vo.setJobInputTime(DateTimeUtils.parseDateTime(jobInputTime));
					vo.setTeamId(orgId);

					List<AcctobjInputFlVo> flList = new ArrayList<AcctobjInputFlVo>();// 按分类汇总数据
					vo.setFlData(flList);

					Map<String, AcctobjInputVo> umap = new LinkedHashMap<String, AcctobjInputVo>();
					Map<String, AcctobjInputFlVo> tempmap = new LinkedHashMap<String, AcctobjInputFlVo>();

//					List<String> tlistCols = this.getCols(obj);//顺序是乱的，给移动端处理，需要重新排序
					for (String col : listCols) {// 列循环
						if (!col.equalsIgnoreCase(PK) && !col.startsWith("currUser") && !col.startsWith("currOrg")
								&& !col.startsWith("rowConfirm") && !col.startsWith(infoCol) && !col.startsWith(typeCol)
								&& !col.startsWith(ulinfoCol) && !col.startsWith(ulobjCol) && !col.startsWith(timeCol)
								&& !col.startsWith(xCol) && !col.startsWith("rq") && !col.startsWith("unitCode")
								&& !col.startsWith("bc") && !col.startsWith("accountId") && !col.startsWith("_mark")) {

							TdsAccountTag atag = getTagIdObjMap.get(col);
							if (atag != null) {
								String ucode = atag.getUnitCode();
								String tagpid = atag.getTagpid();
								Map<String, String> imap = infoMap.get(col);
								String comType = null, txt = null, ops = null;
								if (imap != null) {
									int icom = -1;
									if (Coms.judgeLong(imap.get("com"))) {
										icom = Integer.parseInt(imap.get("com"));
									} else {
										Integer tic = atag.getControltype();
										if (tic != null) {
											icom = tic;
										}
									}
									Object ct = ControlTypeConverter.convertToString(icom);
									txt = imap.get("txt");
									String ks = imap.get("ks");
									String vs = imap.get("vs");

									comType = ct == null ? null : String.valueOf(ct);
									if (StringUtils.isNotEmpty(ks) && StringUtils.isNotEmpty(vs)) {
										// [{"text":"选项1","value":"选项1"},{"text":"选项2","value":"选项2"},{"text":"选项3","value":"选项3"}]
										List<String> klist = Coms.StrToList(ks, ",");
										List<String> vlist = Coms.StrToList(vs, ",");
										StringBuffer sb = new StringBuffer();
										for (int j = 0; j < klist.size(); j++) {
											String key = klist.get(j).replace("\"", "\\\"");
											String val = vlist.get(j).replace("\"", "\\\"");
											sb.append(",{\"text\":\"");
											sb.append(val);
											sb.append("\",\"value\":\"");
											sb.append(key);
											sb.append("\"}");

										}
										if (sb.length() > 0) {
											ops = "[" + sb.substring(1) + "]";
										}
									}
								}

								//txt为本次发送值，与历史数据进行对比
								Map<Date, String> _map = olddatamap.get(col);
								if(_map!=null) {
									String _info = null;
									String oldval = _map.get(vo.getInputTime());
									if(StringUtils.isNotEmpty(oldval)) {//原值不为空
										if(!oldval.equals(txt)) {
											_info = "修改采集点【"+atag.getTagname()+"】数据由【"+oldval+"】改为【"+txt+"】";
										}
									}else {//原值为空
										if(StringUtils.isNotEmpty(txt)) {
											_info = "修改采集点【"+atag.getTagname()+"】数据由【】改为【"+txt+"】";
										}
									}
									if(StringUtils.isNotEmpty(_info)) {
										TdsAccountLog log = new TdsAccountLog();
										log.setId(TMUID.getUID());
										log.setSbsjstr(sbsj);
										log.setXbsjstr(xbsj);
										log.setUnitCode(sendUnitCode);
										log.setUnitName(sendUnitName);
										log.setShiftCode(shiftCode);
										log.setShiftName(shiftName);
										log.setTagId(col);
										log.setTagName(atag.getTagname());
										log.setSjstr(ID);
										log.setUpdUserName(user.getRealName());
										log.setMemo(_info);
										uploglist.add(log);

										//有数据变化后再判断超限，防止重复
										if(StringUtils.isNotEmpty(txt) && !(atag.getUpLimit()==null && atag.getLowerLimit()==null)) {
											if(Coms.judgeDouble(txt)) {
												Boolean overmark = false;
												Double dtxt = Double.parseDouble(txt);
												if(atag.getUpLimit()==null && atag.getLowerLimit()!=null) {
													if(dtxt < atag.getLowerLimit()) {
														overmark = true;
													}
												}else if(atag.getUpLimit()!=null && atag.getLowerLimit()==null) {
													if(dtxt > atag.getUpLimit()) {
														overmark = true;
													}
												}else{
													if(dtxt < atag.getLowerLimit() || dtxt > atag.getUpLimit()) {
														overmark = true;
													}
												}
												if(overmark) {//超限标识
													TdsAccountOverinfo over = new TdsAccountOverinfo();
													over.setId(TMUID.getUID());
													over.setSbsjstr(sbsj);
													over.setXbsjstr(xbsj);
													over.setUnitCode(sendUnitCode);
													over.setUnitName(sendUnitName);
													over.setShiftCode(shiftCode);
													over.setShiftName(shiftName);
													over.setTagId(col);
													over.setTagName(atag.getTagname());
													over.setSjstr(ID);
													over.setUpdUserName(user.getRealName());
													over.setVal(txt);
													over.setUpLimit(atag.getUpLimit());
													over.setLowLimit(atag.getLowerLimit());
													overlist.add(over);
												}
											}
										}
									}
								}

								if (umap.containsKey(ucode)) {
									if (tempmap.containsKey(tagpid)) {
										AcctobjInputFlVo flvo = tempmap.get(tagpid);
										AcctobjInputmxVo mx = new AcctobjInputmxVo();
										mx.setCollectPoint(atag.getTagnumber());
										mx.setCollectPointId(col);
										mx.setCollectPointVal(obj.getString(col));
										mx.setTagNo(atag.getDatasource());
										mx.setSn(atag.getTmsort());
										mx.setInputCompType(comType);
										mx.setCollectPointText(txt);
										mx.setInputOptions(ops);
										mx.setTmused(1);
										mx.setIsWriteBackInfluxdb(atag.getIsWriteBackInfluxdb());
										mx.setJobInputTime(DateTimeUtils.parseDateTime(jobInputTime));
										flvo.getMxData().add(mx);
									} else {
										AcctobjInputVo uobj = umap.get(ucode);

										AcctobjInputFlVo flvo = ObjUtils.copyTo(uobj, AcctobjInputFlVo.class);
										flvo.setFlId(tagpid);
										flvo.setFlName(atag.getTagpname());
										List<AcctobjInputmxVo> mxData = new ArrayList<AcctobjInputmxVo>();
										flvo.setMxData(mxData);

										AcctobjInputmxVo mx = new AcctobjInputmxVo();
										mx.setCollectPoint(atag.getTagnumber());
										mx.setCollectPointId(col);
										mx.setCollectPointVal(obj.getString(col));
										mx.setTagNo(atag.getDatasource());
										mx.setSn(atag.getTmsort());
										mx.setInputCompType(comType);
										mx.setCollectPointText(txt);
										mx.setInputOptions(ops);
										mx.setTmused(1);
										mx.setIsWriteBackInfluxdb(atag.getIsWriteBackInfluxdb());
										mx.setJobInputTime(DateTimeUtils.parseDateTime(jobInputTime));
										mxData.add(mx);
										tempmap.put(tagpid, flvo);
										uobj.getFlData().add(flvo);
									}
								} else {
									AcctobjInputVo uobj = ObjUtils.copyTo(vo, AcctobjInputVo.class);
									uobj.setAcctobjId(ucode);
									uobj.setAcctobjName(atag.getUnitName());

									// 区域从分类获取，定义为采集点所在的分类
//									if(tempmap.containsKey(tagpid)) {
//										AcctobjInputFlVo flvo = tempmap.get(tagpid);
//										AcctobjInputmxVo mx = new AcctobjInputmxVo();
//										mx.setCollectPoint(atag.getTagnumber());
//										mx.setCollectPointId(col);
//										mx.setCollectPointVal(obj.getString(col));
//										mx.setTagNo(atag.getDatasource());
//										mx.setSn(atag.getTmsort());
//										mx.setInputCompType("textfield");
//										flvo.getMxData().add(mx);
//									}else {
									AcctobjInputFlVo flvo = ObjUtils.copyTo(uobj, AcctobjInputFlVo.class);
									flvo.setFlId(tagpid);
									flvo.setFlName(atag.getTagpname());
									List<AcctobjInputmxVo> mxData = new ArrayList<AcctobjInputmxVo>();
									flvo.setMxData(mxData);

									AcctobjInputmxVo mx = new AcctobjInputmxVo();
									mx.setCollectPoint(atag.getTagnumber());
									mx.setCollectPointId(col);
									mx.setCollectPointVal(obj.getString(col));
									mx.setTagNo(atag.getDatasource());
									mx.setSn(atag.getTmsort());
									mx.setInputCompType(comType);
									mx.setCollectPointText(txt);
									mx.setInputOptions(ops);
									mx.setTmused(1);
									mx.setIsWriteBackInfluxdb(atag.getIsWriteBackInfluxdb());
									mx.setJobInputTime(DateTimeUtils.parseDateTime(jobInputTime));
									mxData.add(mx);

									tempmap.put(tagpid, flvo);
//									}
									uobj.getFlData().add(flvo);
									umap.put(ucode, uobj);
								}
							}
						}
					}

					for (AcctobjInputVo ai : umap.values()) {// 多设备情况处理
						clist.add(ai);
					}
				}
				listCols = null;
				if (StringUtils.isNotEmpty(clist)) {// 生成事件发布
					String tid = "";
					String sql = "select PID  from JOBLIST_ACTIVITYEXAMPLE where ID=?";
					param.clear();
					param.add(clist.get(0).getTaskId());
					List<Map<String, Object>> tlist = dao.queryListMap(sql, param.toArray());
					if (StringUtils.isNotEmpty(tlist)) {
						tid = String.valueOf(tlist.get(0).get("PID"));
					}

//					//重新排序
//					for (AcctobjInputVo input : clist) {
//						List<AcctobjInputFlVo> fllist = input.getFlData();
//						for (AcctobjInputFlVo fl : fllist) {
//							List<AcctobjInputmxVo> mxlist = fl.getMxData();
//							mxlist = mxlist.stream().sorted(Comparator.comparing(AcctobjInputmxVo::getSn)).collect(Collectors.toList());
//							fl.setMxData(mxlist);
//						}
//					}

					try {
						// 微服务通信调用岗位工作清单采集点数据同步接口
						AccountSaveDto obj = new AccountSaveDto();
						obj.setType("collectionPoint");// extOp?"collectionPoint":"routingInspection"
						obj.setCollectionPointInputData(clist);
						obj.setOperType("");
						obj.setFormDataId(formDataId);
						obj.setTaskId(tid);

						context.postExchange(serviceName, requestPath, obj);
					} catch (Exception e) {
					}
				}

				if(StringUtils.isNotEmpty(uploglist)) {//修改日志记录
					dao.insertBatch(uploglist, 100);
				}
				if(StringUtils.isNotEmpty(overlist)) {//超限记录
					dao.insertBatch(overlist, 100);
					//发消息 TODO
//					overlist.size();
				}
			}

			if (!extOp) {// 内部操作MongoDB
				for (int i = 0; i < datas.size(); i++) {
					JSONObject obj = datas.getJSONObject(i);// 行数据
					String type = obj.getString(this.typeCol);
					if ("1".equals(type) || "2".equals(type)) {// 统计行不处理
						continue;
					}
					Integer flag = obj.getInteger("TDSROW_rowFlag");// 0添加 1修改 -1删除
					String ID = obj.getString(PK);
					if (flag == null || flag == 0 || flag == 1) {// 添加或修改
						List<String> listCols = this.getCols(obj);
						Boolean rowUpdMark = false;// 行更新状态
						for (String col : listCols) {// 列循环
							if (!col.equalsIgnoreCase(PK)) {
								String key = col + "_" + ID;
								if (valMap.containsKey(key)) {// 更新
//								Boolean updMark = false;//更新标识，除自定义列，仪表数据与获取数据相同，不进行保存
//								String val = vmap.get(ID+"_"+col) == null? "" : vmap.get(ID+"_"+col);//接口数据
//								String saveval = obj.get(col)==null?"":String.valueOf(obj.get(col));//保存数据
//								updMark = (StringUtils.isEmpty(val) && saveval.length() >0)
//										|| (!"".equals(val) && !"".equals(saveval) && !val.equals(saveval));//个性列、接口没数保存有数、接口和保存都有数且不一样 以上条件满足一项即保存

//								if(updMark && !customColList.contains(col)) {//目前个性字段是自动填充的，不能修改，后期如果可以修改，修改此处限制 TODO
									String vs = obj.get(col) == null ? "" : String.valueOf(obj.get(col));
									JSONObject uobj = new JSONObject(valMap.get(key));

									String oldval = uobj.getString("valstr");
									StringBuffer msgsb = new StringBuffer();
									msgsb.append("用户【");
									msgsb.append(user.getRealName());
									msgsb.append("】在");
									msgsb.append(currTimeStr);
									msgsb.append("将仪表【" + tagIdCodeMap.get(col) + "】");
									msgsb.append(uobj.getString("sj"));
									msgsb.append("的数据变更为");
									msgsb.append(oldval);
									msgsb.append("->");
									msgsb.append(vs);
									String memo = null;

									if (newdata == null) {
										uobj.put("val", obj.get(col));
										uobj.put("valstr", vs);
//									uobj.put("edit", true);
										uobj.put("updTime", currTime);
										uobj.put("updUserId", user.getId());
										uobj.put("updUserName", user.getRealName());
										uobj.put("edit", true);
									} else {
										uobj.put("valstr", vs);
										uobj.put("updTime", currTime);
										uobj.put("updUserId", user.getId());
									}
									if ("".equals(vs)) {// 数据保存为空，进行清除
										// uobj.put("tmused", 0);
										memo = "数据清空";
									} else {
									}

									if (!vs.equals(oldval)) {
										updateData.add(uobj);
										if (memo == null) {
											saveChangeLog(1, msgsb.toString(), memo);
										} else {
											saveChangeLog(1, msgsb.toString(), memo);
										}
										if (!tagIdList.contains(col)) {
											tagIdList.add(col);
										}
									}

									rowUpdMark = true;
//								}
								} else if (obj.get(col) != null && !"".equals(String.valueOf(obj.get(col)))) {// 添加
									Boolean saveMark = false;// 是否保存标识，除自定义列，仪表数据与获取数据相同，不进行保存
//
									if (!col.startsWith("currUser") && !col.startsWith("currOrg")
											&& !col.startsWith("rowConfirm") && !col.startsWith(infoCol)
											&& !col.startsWith(typeCol) && !col.startsWith(ulinfoCol)
											&& !col.startsWith(ulobjCol) && !col.startsWith(timeCol)
											&& !col.startsWith(xCol)) {
										saveMark = true;
									}
//								String val = vmap.get(ID + "_" + col) == null ? "" : vmap.get(ID + "_" + col);// 接口数据
//								String saveval = obj.get(col) == null ? "" : String.valueOf(obj.get(col));// 保存数据
//								saveMark = !customWriteColList.contains(col);
//										|| (StringUtils.isEmpty(val) && saveval.length() > 0)
//										|| (!"".equals(val) && !"".equals(saveval) && !val.equals(saveval));// 个性填写列、接口没数保存有数、接口和保存都有数且不一样
//																											// 以上条件满足一项即保存
//
									if (saveMark) {
										JSONObject aobj = new JSONObject();

										if (newdata == null) {
											aobj.put("_id", TMUID.getUID());
											aobj.put("dsAlias", tdsAlias);
											aobj.put("formId", null);
											aobj.put("dataId", null);
											aobj.put("col", col);// 列名
											if (customColList.contains(col)) {
												aobj.put("tag", "");
												aobj.put("dateType", col.substring(0, col.length() - 3));
												aobj.put("tagId", null);
											} else {
												aobj.put("tag", tagIdCodeMap.get(col));
												aobj.put("dateType", "tag");
												aobj.put("tagId", col);
											}
											aobj.put("rq", rq);
											aobj.put("rqlong",
													rq == null ? null : DateTimeUtils.parseDate(rq).getTime());
											aobj.put("sj", ID);
											aobj.put("sjlong", DateTimeUtils.parseDateTime(ID).getTime());
											aobj.put("val", obj.get(col));
											aobj.put("valstr",
													obj.get(col) == null ? "" : String.valueOf(obj.get(col)));
											aobj.put("creTime", currTime);
											aobj.put("creUserId", user.getId());
											aobj.put("creUserName", user.getRealName());
											aobj.put("updTime", null);
											aobj.put("updUserId", null);
											aobj.put("updUserName", null);
											aobj.put("additionVal", null);// 附加值

											if (ID.indexOf(",") != -1) {
												aobj.put("timetype", "bound");
											} else {
												aobj.put("timetype", "point");
											}
											if (customColList.contains(col)) {// 个性数据第一次保存不设置保存标识
												aobj.put("edit", false);
											} else {// 手工保存过
												aobj.put("edit", true);
											}

											aobj.put("tmused", 1);
											aobj.put("confirmDiff", 0);// 确认差（秒）
											aobj.put("rowConfirmBound", null);// 确认范围（分钟）
											aobj.put("rowConfirmOver", false);// 确认超时

											aobj.put("unitCode", unitCode);// 核算单元
											aobj.put("bc", shiftCode);// 班次
											aobj.put("accountId", accountId);// accountId
										} else {
											aobj.put("col", col);// 列名
											aobj.put("sj", ID);
											aobj.put("sjlong", DateTimeUtils.parseDateTime(ID).getTime());
											aobj.put("valstr",
													obj.get(col) == null ? "" : String.valueOf(obj.get(col)));
											aobj.put("creTime", currTime);
											aobj.put("updTime", null);
											aobj.put("updUserId", null);
											aobj.put("edit", false);
										}
										insertData.add(aobj);

										StringBuffer msgsb = new StringBuffer();
										msgsb.append("用户【");
										msgsb.append(user.getRealName());
										msgsb.append("】在");
										msgsb.append(currTimeStr);
										msgsb.append("将仪表【" + tagIdCodeMap.get(col) + "】");
										msgsb.append(aobj.getString("sj"));
										msgsb.append("的数据保存为");
										msgsb.append(aobj.getString("valstr"));
										String memo = null;
										saveChangeLog(0, msgsb.toString(), memo);

										if (!tagIdList.contains(col)) {
											tagIdList.add(col);
										}
									} else {
										rowUpdMark = false;
									}
								}
							}
						}

//					if (rowUpdMark) {// 如果存在行更新情况，判断是否有个性列，进行自动赋值
//						for (String col : customColList) {// 个性列
//							String key = col + "_" + ID;// 仪表+时间
//							// 个性变量取值
//							String val = "", valcode = "";
//							if (col.startsWith("currUser")) {// 当前人员
//								val = user.getRealName();
//								valcode = user.getId();
//							} else if (col.startsWith("currOrg")) {// 当前机构
//								val = user.getOrgId();
//								valcode = user.getOrgName();
//							} else {// 非自动填充列不进行输入
//								continue;
//							}
//
//							if (valMap.containsKey(key)) {// 更新
//								JSONObject uobj = new JSONObject(valMap.get(key));
//								uobj.put("val", val);
//								uobj.put("valstr", val);
//								uobj.put("additionVal", valcode);// 附加值
////									uobj.put("edit", true);
//								uobj.put("updTime", currTime);
//								uobj.put("updUserId", user.getId());
//								uobj.put("updUserName", user.getRealName());
//								updateData.add(uobj);
//							} else {
//								JSONObject aobj = new JSONObject();
//								aobj.put("_id", TMUID.getUID());
//								aobj.put("dsAlias", tdsAlias);
//								aobj.put("formId", null);
//								aobj.put("dataId", null);
//								aobj.put("col", col);// 列名
//								aobj.put("tag", "");
//								aobj.put("tagId", null);
//								aobj.put("dateType", col.substring(0, col.length() - 3));
//								aobj.put("rq", rq);
//								aobj.put("rqlong", rq==null?null:DateTimeUtils.parseDate(rq).getTime());
//								aobj.put("sj", ID);
//								aobj.put("sjlong", DateTimeUtils.parseDateTime(ID).getTime());
//								aobj.put("val", val);
//								aobj.put("valstr", val);
//								aobj.put("additionVal", valcode);// 附加值
//								aobj.put("creTime", currTime);
//								aobj.put("creUserId", user.getId());
//								aobj.put("creUserName", user.getRealName());
//								aobj.put("updTime", null);
//								aobj.put("updUserId", null);
//								aobj.put("updUserName", null);
//								aobj.put("edit", false);
//								aobj.put("tmused", 1);
//								aobj.put("confirmDiff", 0);// 确认差（秒）
//								aobj.put("rowConfirmBound", null);// 确认范围（分钟）
//								aobj.put("rowConfirmOver", false);// 确认超时
//								if (ID.indexOf(",") != -1) {
//									aobj.put("timetype", "bound");
//								} else {
//									aobj.put("timetype", "point");
//								}
//								insertData.add(aobj);
//							}
//						}
//					}
					}

//				if (flag == null || flag == 0 || flag == 1) {// 添加或修改
//					List<String> listCols = this.getCols(obj);
//					JSONObject newobj = new JSONObject();
//					for (String col : listCols) {
//						if (!col.equalsIgnoreCase(PK)) {
//							String key = obj.getString(col)+"_"+ID;
//							if(valMap.containsKey(key)) {//更新
//
//							}else {//添加
//								newobj.put(op.getParaAlias(), obj.get(col));
//							}
//						}
//					}
//					if (StringUtils.isEmpty(ID) || flag == null || flag == 0) {// 添加
//						if (StringUtils.isEmpty(ID)) {
//							newobj.put("_id", TMUID.getUID());
//						} else {
//							newobj.put("_id", ID);
//						}
//						// 将新生成的id返回给json对象
//						obj.put(PK, newobj.getString("_id"));
//						insertData.add(newobj);
//					} else {// 修改
//						newobj.put("_id", ID);
//						updateData.add(newobj);
//					}
//				}else if (flag == -1) {// 无删除操作
//					deleteIds.add(ID);
//				}
				}
			}

//			//获取仪表采集点设置，判断仪表数据是否回写到R3DB 暂时屏蔽 TODO
//			List<String> r3List = getTagR3MarkList(tagIdList);
//			List<JSONObject> writeList1 = new ArrayList<JSONObject>();//添加
//			List<JSONObject> writeList2 = new ArrayList<JSONObject>();//修改

			if (insertData.size() > 0) {// 添加
				if (StringUtils.isNotEmpty(newdata)) {// 有新数据，针对新数据进行操作
					JSONObject tobj = new JSONObject(newdata.get(0));
					JSONArray arr = tobj.getJSONArray("info");
					arr.addAll(insertData);
					tobj.put("info", arr);
//					JSONArray alist = new JSONArray();
//					alist.add(tobj);
					mongoDBServ.updateById(accountNewTab + "_" + tdsAlias, tobj);
				} else {// 没有新数据，看是否有历史数据
					mongoDBServ.insertBatch(accountTabName + "_" + tdsAlias, insertData);
				}
			}

			if (updateData.size() > 0) {// 修改
				if (StringUtils.isNotEmpty(newdata)) {// 有新数据，针对新数据进行操作
					// 整理更新数据
					Map<String, JSONObject> tmap = new HashMap<String, JSONObject>();
//					for (Object object : updateData) {
//						JSONObject o = (JSONObject)object;
//						tmap.put(o.getString("col")+"_"+o.getString("sj"), o);
//					}
					for (int i = 0, il = updateData.size(); i < il; i++) {
						JSONObject o = updateData.getJSONObject(i);
						tmap.put(o.getString("col") + "_" + o.getString("sj"), o);
					}
					// 删除已有的更新的数据
					JSONObject tobj = new JSONObject(newdata.get(0));
					JSONArray arr = tobj.getJSONArray("info");
					for (Iterator iter = arr.iterator(); iter.hasNext();) {
						JSONObject o = new JSONObject((Map) iter.next());
						String key = o.getString("col") + "_" + o.getString("sj");
						if (tmap.containsKey(key)) {
							iter.remove();
						}
					}
					// 添加要更新的数据
					arr.addAll(updateData);
					tobj.put("info", arr);
//					JSONArray alist = new JSONArray();
//					alist.add(tobj);
					mongoDBServ.updateById(accountNewTab + "_" + tdsAlias, tobj);
				} else {// 没有新数据，处理历史数据
					mongoDBServ.updateBatchById(accountTabName + "_" + tdsAlias, updateData);
				}
			}

			if (StringUtils.isNotEmpty(tagIdList) && (insertData.size() > 0 || updateData.size() > 0)) {
				accountServ.calculateCount(tdsAlias, tagIdList, sbsj, xbsj, rq, shiftCode, tagIdCodeMap);
			}

//			if (deleteIds.size() > 0) {// 删除
//				mongoDBServ.deleteBatchById(collectionName, deleteIds);
//			}

//			if (StringUtils.isNotEmpty(writeList1) || StringUtils.isNotEmpty(writeList2)) {//回写//暂时屏蔽 TODO
//				//TODO
//			}
//			writeList1 = null;
//			writeList2 = null;
		} catch (Exception e) {
			rtn = e.getMessage();
		}
		return rtn;

	}

	private void getMobileData(Map<String, Map<Date, String>> rmap, String unitCode, String sbsj, String bc) {
		StringBuffer whereUnit = new StringBuffer();
		String uid = unitCode;
		if(uid.indexOf(",")==-1) {
			whereUnit.append("='");
			whereUnit.append(unitCode);
			whereUnit.append("'");
		}else {
			whereUnit.append("in(");
			List<String> ulist = Coms.StrToList(unitCode, ",");
			for (String u : ulist) {
				if(u.length() > 3) {
					whereUnit.append(",");
				}
				whereUnit.append("'");
				whereUnit.append(u);
				whereUnit.append("'");
			}
			whereUnit.append(")");
		}
		List<Object> param = new ArrayList<Object>();
		Date sbsjD = StringUtils.isEmpty(sbsj) ? null : DateTimeUtils.parseDateTime(sbsj);

		String sql = "select COLLECT_POINT_ID, IPT_ID, COLLECT_POINT_TEXT, COLLECT_POINT_VAL,INPUT_COMP_TYPE,INPUT_OPTIONS,INPUT_TIME " +
				"from ACCTOBJ_INPUTMX where TMUSED=1 and exists (select ID from ACCTOBJ_INPUT where ACCTOBJ_ID "+whereUnit.toString()+" and bcdm=? and sbsj=? and ID=ACCTOBJ_INPUTMX.IPT_ID)";
		if(sbsjD!=null) {//班次模式查询
			param.add(bc);
			param.add(sbsjD);
		}
//		else {
//			if(StringUtils.isNotEmpty(timeBoundDay)) {
//				param.add(DateTimeUtils.parseDate(timeBoundDay));
//				param.add(DateTimeUtils.doSecond(DateTimeUtils.doDate(DateTimeUtils.parseDate(timeBoundDay), 1), -1));
//			}else {
//				Date nd = DateTimeUtils.getNowDate();
//				param.add(nd);
//				param.add(nd);
//			}
//			sql = "select COLLECT_POINT_ID, IPT_ID, COLLECT_POINT_TEXT, COLLECT_POINT_VAL,INPUT_COMP_TYPE,INPUT_OPTIONS,INPUT_TIME " +
//					"from ACCTOBJ_INPUTMX where TMUSED=1 and exists (select ID from ACCTOBJ_INPUT where BA_ID ='"+unitCode+"' and INPUT_TIME between ? and ? and ID=ACCTOBJ_INPUTMX.IPT_ID)";
//		}

		List<Map<String, Object>> list = dao.queryListMap(sql, param.toArray());
		if(StringUtils.isNotEmpty(list)) {
			for (Map<String, Object> map : list) {
				String tagId = getMapString(map, "COLLECT_POINT_ID");
//				String mainId = getMapString(map, "IPT_ID");
//				String showTxt = getMapString(map, "COLLECT_POINT_TEXT");
				String val = getMapString(map, "COLLECT_POINT_VAL");
//				String comType = getMapString(map, "INPUT_COMP_TYPE");
//				String comOptions = getMapString(map, "INPUT_OPTIONS");
				Date inputTime = getMapDate(map, "INPUT_TIME");
				String it = DateTimeUtils.formatDateTime(inputTime);
//				Date bjdate = DateTimeUtils.parseDateTime(it);
				String dt = DateTimeUtils.formatDateTime(inputTime);

				//赋值
				if(StringUtils.isNotEmpty(dt)) {
					Date d = DateTimeUtils.parseDateTime(dt);
					if(rmap.containsKey(tagId)) {
						rmap.get(tagId).put(d, val);
					}else {
						Map<Date, String> _map = new LinkedHashMap<Date, String>();
						_map.put(d, val);
						rmap.put(tagId, _map);
					}
				}

			}
		}
	}
	private Integer getMapInt(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if(obj == null) {
			return null;
		}else if(Coms.judgeLong(obj)) {
			String so = String.valueOf(obj);
			return Integer.parseInt(so);
		}

		return null;
	}
	private String getMapString(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if(obj == null) {
			return null;
		}else {
			return String.valueOf(obj);
		}
	}
	private Date getMapDate(Map<String, Object> map, String key) {
		Object obj = map.get(key);
		if(obj == null) {
			return null;
		}else if(obj instanceof java.util.Date){
			return (Date)obj;
		}

		return null;
	}

	// 解析行数据对象，获取各采集点信息
	private Map<String, Map<String, String>> getInfoMap(String infostr) {
		Map<String, Map<String, String>> rmap = new HashMap<String, Map<String, String>>();
		if (StringUtils.isNotEmpty(infostr)) {
			JSONObject rowobj = JSONObject.parseObject(infostr);
			JSONObject aobj = rowobj.getJSONObject("data");
			Set<String> keys = aobj.keySet();
			for (String key : keys) {
				Map<String, String> tmap = aobj.getObject(key, Map.class);
				rmap.put(key, tmap);
			}
		}
		return rmap;
	}

	private String getInfoTaskId(String info) {
		if (StringUtils.isNotEmpty(info)) {
			List<String> tlist = Coms.StrToList(info, ";");
			for (String s : tlist) {
				List<String> tlist2 = Coms.StrToList(s, "@");
				if (tlist2.size() > 1) {
					String key = tlist2.get(0);
					String val = tlist2.get(1);
					if ("activeId".equals(key)) {
						return "null".equalsIgnoreCase(val) ? "" : val;
					}
				}
			}
		}
		return null;
	}

	// 获取有回写标识仪表
	private List<String> getTagR3MarkList(List<String> tagIdList) {

		return null;
	}

	/**
	 * @category 添加操作日志
	 * @param operation
	 * @param memo
	 */
	private void saveChangeLog(Integer op, String operation, String memo) {
		OperationLogAdd dto = new OperationLogAdd();
		dto.setId(TMUID.getUID());
		dto.setModuleCode("accountTds");
		dto.setModuleName("台账数据源");
		dto.setMenuName("");// 菜单名称
		if (op != null) {// 操作类型 0：添加；1修改；-1:删除;2:查询
			dto.setOperType(op);
		} else {
			dto.setOperType(1);
		}
//		dto.setLogLevel("INFO");// 日志级别 DEBUG,ERROR，INFO 默认INFO
		dto.setClassName(this.getClass().getName());// 执行的类名
		dto.setExecMethod("saveAccountData2MongoDB");// 执行方法
		dto.setOperation(operation);// 操作描述
		dto.setMemo(memo);//// 操作备注
		logSrv.addOperLog(dto);
	}

	/**
	 * @category 获取台账个性化录入的列
	 * @param tdsAlias
	 * @return
	 */
	private List<String> getCustomCol(String tdsAlias) {
		List<String> rlist = new ArrayList<String>();

		TdsAccountDto param = new TdsAccountDto();
		param.setTdsAlias(tdsAlias);
		List<TdsAccountOutparamVo> list = accountServ.getOutConf(param);
		if (StringUtils.isNotEmpty(list)) {
			for (TdsAccountOutparamVo obj : list) {
				if (StringUtils.isNotEmpty(obj.getColType()) && StringUtils.isNotEmpty(obj.getAlias())) {
					rlist.add(obj.getAlias());
				}
			}
		}
		return rlist;
	}

	private List<String> getCustomWriteCol(List<String> list) {
		List<String> rlist = new ArrayList<String>();
		if (StringUtils.isNotEmpty(list)) {
			for (String col : list) {
				if (!col.startsWith("currUser") && !col.startsWith("currOrg") && !col.startsWith("rowConfirm")) {
					rlist.add(col);
				}
			}
		}
		return rlist;
	}

//	private List<TOutPara> getAccountOutList(String tdsAlias) {
//		String confCode = null;
//		String version = null;
//		Integer mode = 1;// 仪表查询范围 1核算对象 2机构
//
//		TdsAccountConf conf = accountServ.getAccountConf(tdsAlias);// 配置信息
//		List<TdsAccountMeter> mlist;
////		TdsAccountTime timeConf = accountServ.getTimeConf(tdsAlias);//时间配置
//		TdsAccountDto param = new TdsAccountDto();
//		param.setTdsAlias(tdsAlias);
//		List<TdsAccountOutparamVo> plist = accountServ.getOutConf(param);// 输出配置
//
//		List<TdsAccountParamVo> bindparamList = accountServ.getAccountBindParam(tdsAlias);
//		Map<String, TdsAccountParam> amap = new HashMap<String, TdsAccountParam>();
//		for (TdsAccountParam aparam : bindparamList) {
//			amap.put(aparam.getOldParamAlias(), aparam);
//		}
//		// 获取主数据的输入参数及值，确定获取仪表信息范围
////		if (StringUtils.isNotEmpty(this.inParaStr)) {
////			Boolean isunit = false;
////			TdsAccountParam bp = amap.get(temp.getParaAlias());
////			if(bp!=null && temp.getValue()!=null) {
////				String val = String.valueOf(temp.getValue());// && String.valueOf(temp.getValue()).length() > 0
////				if("unit".equals(bp.getNewParamId())) {
////					if(val.length() > 0) {
////						mode = 1;
////						this.confCode = val;
////						isunit = true;
////					}
////				}else if("org".equals(bp.getNewParamId()) && !isunit) {
////					if(val.length() > 0) {
////						mode = 2;
////						this.confCode = val;
////					}
////				}
////			}
////		}
//
//		if (confCode == null) {
//			SysUser user = SysUserHolder.getCurrentUser();
//			mode = 2;
//			confCode = user.getOrgId();// 所属机构
//		}
//
//		return null;
//	}

	private List<Date> getStartEndTime(JSONArray datas) {
		List<Date> timeList = new ArrayList<Date>(2);
		Date start = null;
		Date end = null;
		for (int i = 0; i < datas.size(); i++) {
			JSONObject obj = datas.getJSONObject(i);// 行数据
			String sj = obj.getString(PK);
			String type = obj.getString(this.typeCol);
			if ("1".equals(type)) {// 统计行不处理
				continue;
			}
			Date d = DateTimeUtils.parseDateTime(sj);
			if (start == null) {
				start = d;
			} else {
				if (DateTimeUtils.bjDate(start, d) == 1) {
					start = d;
				}
			}
			if (end == null) {
				end = d;
			} else {
				if (DateTimeUtils.bjDate(d, end) == 1) {
					end = d;
				}
			}
		}

		timeList.add(start);
		timeList.add(end);

		return timeList;
	}

	/**
	 * @category 取最大公约数
	 * @param step 数值1
	 * @param imin 数值2
	 * @return
	 */
	private int commonDivisor(int step, int imin) {
		int min = step < imin ? step : imin;
		int max = step > imin ? step : imin;
		int comDiv = max;
		int jg = 1;

		for (int i = min, il = 0; i > il && comDiv == 0; i--) {
			comDiv = max % min;
			if (comDiv == 0) {
				jg = min;
				break;
			} else {
				max = min;
				min = comDiv;
			}
		}
		return jg;
	}
}
