package com.yunhesoft.system.init;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.syslog.utils.ControllerFunctionMap;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.model.BlobColumnUtils;
import com.yunhesoft.system.menu.service.ISysMenuLibInitService;
import com.yunhesoft.system.settings.entity.SysOpenInterfaceWhite;
import com.yunhesoft.system.settings.entity.dto.SysOpenInterFaceWhiteDto;
import com.yunhesoft.system.settings.service.impl.OpenInterfaceWhiteServiceImpl;
import com.yunhesoft.system.tools.database.service.impl.SysDataBaseServiceImpl;

/**
 * <AUTHOR>
 * @category 系统主框架菜单库初始化服务
 * @since 2021-5-25 16:29:02
 */
@Component
@Order(0)
public class MenuLibInitService implements ApplicationRunner {

    @Autowired
    private ISysMenuLibInitService serv; // 菜单库

    @Autowired
    SysDataBaseServiceImpl ddl;

    @Autowired
    OpenInterfaceWhiteServiceImpl whiteService;

    /**
     * 菜单数据初始化
     */
    private void init() {
        /**
         ** 注意必须顺序初始化数据 1.模块->2.分类->3.菜单->4.按钮 （无按钮权限可以忽略）
         */
        serv.clearData();// 清除数据
        // 1、初始化系统模块
        serv.initModule("系统主框架", "system");
        /** 模块编码：微服务的编码，这里的模块以一个微服务为单位 **/

        if (!MultiTenantUtils.enalbe()) {
            serv.initClass("系统管理", "sysManager");
            serv.initMenuLib("Redis管理", "/system/tools/redisManager");
            serv.initMenuLib("通用日志查询", "/system/log/index");
        }
        // 2、初始化模块下的分类
        serv.initClass("基础设置", "baseset");/** 分类编码该模块下不要重复 **/
        // 3、初始化菜单
        serv.initMenuLib("菜单管理", "/system/menu/index");
        /****** 多租户模式初始化租户菜单维护 ******/
        if (MultiTenantUtils.enalbe()) {
            serv.initMenuLib("租户默认菜单", "/system/menu/tenantMenuConfigDefault");
            serv.initMenuLib("租户菜单管理", "/system/menu/tenantMenuConfig");
        }

        /** 地址不要重复,如果两个菜单使用同一个地址可以用/system/dict/index?m=1,/system/dict/index?m=2 **/
        // if (!MultiTenantUtils.enalbe()) {
        serv.initMenuLib("系统参数", "/system/config/index");
        serv.initMenuLib("数据字典", "/system/dict/index");
        /** 需要传参可以使用 */
        // serv.initMenuLib("数据字典", "/system/dict/index", "name=zhong&color=red");
        // 4、初始化按钮
        serv.initButtonAdd();// 添加按钮
        serv.initButtonEdit();// 修改
        serv.initButtonDel();// 删除
        serv.initButtonSave();// 保存
        serv.initMenuLib("数据源设置", "/system/tds/setTds");
        serv.initMenuLib("自定义页面", "/system/tools/diy/diySet");
        // serv.initMenuLib("自定义页面查询", "/system/tools/diy/index?pageId=1");
        serv.initMenuLib("自定义分析", "/system/tools/chart/chart");
        serv.initMenuLib("自定义链接", "/system/tools/toolbarLink/tblSet");
        serv.initMenuLib("签章管理", "/system/tools/signature/watermarkSet");
        serv.initMenuLib("消息推送", "/system/tools/messagePush/index");
        serv.initMenuLib("日历设置", "/system/tools/calendar/index");
        // }

        // 开始初始化下一个分类
        serv.initClass("组织机构", "org");
        serv.initMenuLib("机构设置", "/system/org/index");
        serv.initButton("自定义岗位权限", "diyPost");// 添加按钮
        serv.initButtonAdd();// 添加按钮
        serv.initButtonEdit();// 修改
        serv.initButtonDel();// 删除

        // serv.initButtonEdit();// 修改
        // serv.initButtonDel();// 删除
        // serv.initButtonSave();// 保存

        serv.initClass("人员岗位", "employee");
        serv.initMenuLib("岗位库管理", "/system/post/index");
        serv.initMenuLib("专业管理", "/system/professional/professionalSetting");
        // serv.initButtonAdd();// 添加按钮
        // serv.initButtonEdit();// 修改
        // serv.initButtonDel();// 删除
        // serv.initButtonSave();// 保存
        serv.initMenuLib("失效人员管理", "/system/employee/employeeInvalid");
        serv.initMenuLib("人员管理", "/system/employee/index");
        serv.initButton("初始化登录", "initLogin");
        serv.initButton("调动人员", "change");
        serv.initButton("删除人员", "delete");
        serv.initButton("人员权限设置", "permSeting");
        serv.initButton("允许协作模式", "assistMode");
        // serv.initSubMenu("岗位设置", "/system/post/index"); // 初始化子页面
        // serv.initSubMenu("角色设置", "/system/role/index"); // 初始化子页面

        serv.initMenuLib("角色管理", "/system/role/index");
        // 带参数模式菜单库
        // serv.initMenuLib("角色管理", "/system/role/index1","a=1&b=2");
        serv.initMenuLib("角色快捷菜单", "/dashboard/menuRoleLayout");

        // 开始初始化下一个分类

        // 开始初始化系统参数
        serv.initConfig("系统名称", "sys_appname", "TM4主框架", "平台系统名称");
        // serv.initConfig("文件上传限制", "file_size", "1", "文件上传大小限制，单位（M）");

        serv.initConfig("工号位数", "staffBitNum", "0", "0为无限制");
        serv.initConfig("是否允许在人员信息编辑窗口进行人员调动", "isAllowEditChange", "1", "1：启用 0或空不启用");
        serv.initConfig("是否只允许将人员放在最后一个机构上", "isonlySelectEndOrg", "", "1：启用 0或空不启用");
        serv.initConfig("获取默认的变动日期", "defaultEmployeeChangeDate", "", "现仅支持0：当前月1日 1或者空为上个月1日");
        serv.initConfig("文件上传大小限制", "file_maxSize", "10MB", "例如：10MB，修改后需重启服务生效");
        serv.initConfig("文件物理地址", "file_path", "D:/tm4UploadFiles", "文件物理地址，带盘符");
        serv.initConfig("文件网络地址", "file_url", "/files", "文件网络相对地址，以反斜杠开头");
        serv.initConfig("允许上传文件扩展名", "file_ext", "mp4,avi,zip,rar", "常用扩展名程序已默认，其他多个扩展名以逗号分隔");
        serv.initConfig("帮助文档地址", "sys_helpDocUrl", "", "帮助文档网络地址");
        // 登陆密码验证相关
        serv.initConfig("默认密码", "sys_default_pass", "<EMAIL>", "用户初始化或者重置默认密码");
        serv.initConfig("登陆是否校验密码", "sys_login_force", "false", "用户登陆时是否强制按照密码规则校验密码");

        serv.initConfig("R3db系统地址", "TM4OperativeModeUrl", "http://************:6670/tm4main",
                "(系统参数)R3db系统地址，TM4工况识别系统地址");
        serv.initConfig("R3db系统服务授权码", "TM4RtdbSerAuth", "994E90184E56C2160833B8BD7D7F25B3", "(系统参数)R3db系统服务授权码");
//		serv.initConfigJson("r3db系统配置", "r3db_config", "{\"server\":\"\", \"auth\":\"\", \"rdbid\":\"\"}", "r3db系统配置");

        serv.initConfig("客服电话", "sys_customerserv_tel", "0419-5366067", "多个逗号分隔");

        serv.initConfig("客服电话", "sys_customerserv_tel", "0419-5366067", "多个逗号分隔");

        serv.initConfig("是否缓存菜单", "menuCache", "true", "多个逗号分隔");

        // r3db服务配置
        serv.initConfigJson("r3db系统服务参数配置", "r3db_cfg",
//				"{\"forminfo\": [{\"key\": \"server\", \"name\": \"r3db系统服务地址\", \"default\": \"http://************:6670/tm4main\"}, {\"key\": \"auth\", \"name\": \"r3db系统服务授权码\", \"default\": \"994E90184E56C2160833B8BD7D7F25B3\"}, {\"key\": \"rdbid\", \"name\": \"r3db系统服务rdbid\", \"default\": \"\"}], \"value\": {\"server\": \"http://************:6670/tm4main\", \"auth\": \"994E90184E56C2160833B8BD7D7F25B3\", \"rdbid\": \"\"}}",
                "{\"forminfo\": [{\"key\": \"server\", \"name\": \"r3db系统服务地址\", \"default\": \"\"}, {\"key\": \"auth\", \"name\": \"r3db系统服务授权码\", \"default\": \"\"}, {\"key\": \"rdbid\", \"name\": \"r3db系统服务rdbid\", \"default\": \"\"}], \"value\": {\"server\": \"\", \"auth\": \"\", \"rdbid\": \"\"}}",
                "r3db系统服务参数配置");

        // 企业微信参数配置(JSON类型),主框架0.118及以上支持
        serv.initConfigJson("企业微信参数配置", "sys_weixin_cfg",
                "{\"forminfo\":[{\"key\":\"WeiXin_loginIp\",\"name\":\"登录认证IP\",\"default\":\"https://open.weixin.qq.com\"},{\"key\":\"WeiXin_logicIp\",\"name\":\"业务调用IP\",\"default\":\"https://qyapi.weixin.qq.com\"},{\"key\":\"WeiXin_Appid\",\"name\":\"企业号ID\",\"default\":\"ww0ef518287bfe5000\"},{\"key\":\"WeiXin_Agentid\",\"name\":\"企业应用ID\",\"default\":\"1000001\"},{\"key\":\"WeiXin_Secret\",\"name\":\"凭证密钥\",\"default\":\"av7tecB3o-GfrI4kaUhNvLMle0rnhrDsZ-YreRr0u00\"},{\"key\":\"WeiXin_Domain\",\"name\":\"系统后台域名\",\"default\":\"http://www.xxxx.com:8088/tm4main\"},{\"key\":\"WeiXin_Domain_Front\",\"name\":\"系统前台域名\",\"default\":\"http://www.xxxx.com:81\"},{\"key\":\"WeiXin_UseSendMsg\",\"name\":\"消息推送\",\"default\":\"true\"},{\"key\":\"WeiXin_LicenseFilePath\",\"name\":\"消息Licen\",\"default\":\"c:\\\\LicenseWinXin.xml\"}],\"value\":{\"WeiXin_loginIp\":\"https://open.weixin.qq.com\",\"WeiXin_logicIp\":\"https://qyapi.weixin.qq.com\",\"WeiXin_Appid\":\"ww0ef518287bfe5000\",\"WeiXin_Agentid\":\"1000001\",\"WeiXin_Secret\":\"av7tecB3o-GfrI4kaUhNvLMle0rnhrDsZ-YreRr0u00\",\"WeiXin_Domain\":\"http://www.xxxx.com:8088/tm4main\",\"WeiXin_Domain_Front\":\"http://www.xxxx.com:81\",\"WeiXin_UseSendMsg\":\"false\",\"WeiXin_LicenseFilePath\":\"c:\\\\LicenseWinXin.xml\"}}",
                "企业微信用到的相关参数配置集合");
        // TM3待办配置
        serv.initConfigJson("获取TM3待办信息参数配置", "todo_tm3",
                "{\"forminfo\": [{\"key\": \"isUsed\", \"name\": \"是否启用获取TM3待办功能\", \"default\": \"false\"}, {\"key\": \"todo_address\", \"name\": \"TM3待办获取接口地址\", \"default\": \"http://127.0.0.1:8080/tm3/todo/getTodoList\"}, {\"key\": \"todo_codes\", \"name\": \"获取待办的模块列表，逗号分隔，空为取全部模块\", \"default\": \"\"}], \"value\": {\"isUsed\": \"false\", \"todo_address\": \"http://127.0.0.1:8080/tm3/todo/getTodoList\", \"todo_codes\": \"\"}}",
                "TM3待办信息参数配置");
        serv.initConfig("机构内自定义岗位", "orgDiyPost", "", "用于设置机构内部自定义岗位的机构类型configServic编码,为空则是不启用。");

        //GMSG消息中心websocket连接地址
        serv.initConfig("GMSG消息中心websocket连接地址", "GMSGWebsocketUri", "ws://127.0.0.1:8080/websocket", "GMSG消息中心websocket连接地址", 1);

        /** 初始化语句，可执行修改字段类型语句,initSqlScript函数参数(语句,说明,作者,日期) */
//		 serv.initSqlScript("alter table sys_customfun1 modify column TMSORT varchar(10)","修改sys_customfun1.TMSORT字段类型", "x.zhong", "2021-10-08");
//		 serv.initSqlScript("alter table sys_customfun1 modify column REMARK varchar(500)","修改sys_customfun1.REMARK字段长度", "x.zhong", "2021-10-09");

        /** 添加数据权限 */
        // 默认数据权限（组织机构）
        serv.initDataPerm("sys_org_gx", "管辖机构");
        // 自定义数据权限
//		serv.initDataPerm("sys_test", "测试", "com.yunhesoft.system.tools.todo.model.SystemTodoApp", "getDataPermData()");

        /** 添加移动端消息跳转链接 */
        // 移动端 - 数据源跳转链接
        serv.initMobileMsgUrl("系统模块_数据源查询", "tds_query", "/pages/tds/index", "", "用于移动端数据源发消息时，跳转链接");
        // 白名单token维护页面
        serv.initClass("外部访问", "OUT_PAGE");
        serv.initMenuLib("访问控制", "/system/user/author/author");
        // 开放接口初始化
        this.openApiInit();

        /** 文件管理 */
        serv.initClass("文件管理", "fileManager");

        serv.initMenuLib("文件管理", "/system/file/fileManage");
        serv.initButton("公共文件上传", "upload");
        serv.initButton("公共文件编辑(修改删除)", "edit");
        serv.initButton("公共文件下载", "download");
        serv.initButton("系统文件管理", "sysfilemanage");

        serv.initMenuLib("档案管理", "/system/file/archivesManage");
        serv.initButton("上传", "upload");
        serv.initButton("编辑(修改删除)", "edit");
        serv.initButton("下载", "download");

        serv.initMenuLib("在线文件管理", "/system/onlineFile/onlineFileManage");
        serv.initButton("在线文件夹创建", "createfolder");
        serv.initButton("在线文件上传", "upload");
        serv.initButton("在线文件删除", "delete");
        serv.initButton("在线文件下载", "download");
        serv.initButton("在线文件管理", "manage");

        serv.initMenuLib("在线文件夹文档管理", "/system/onlineFile/onlineFileManageFolder");
        serv.initButton("在线文件夹创建", "createfolder");
        serv.initButton("在线文件上传", "upload");
        serv.initButton("在线文件删除", "delete");
        serv.initButton("在线文件下载", "download");
        serv.initButton("在线文件管理", "manage");

        /** 台账管理 */
        serv.initClass("台账管理", "accountManage");
        serv.initMenuLib("台账录入查询", "/accountManage/index");
        serv.initMenuLib("台账自定义设置", "accountManage/accountFormConf");
        serv.initMenuLib("台账参数设置", "/accountManage/applyConf");

        /** **** 执行初始化表单相关操作 ******/
        // if (!MultiTenantUtils.enalbe()) {
        serv.initModule("表单模块", "tm4-form");
        serv.initClass("表单管理", "tm4-flow-form");
        serv.initMenuLib("表单模板", "/system/formtpl/index");
        serv.initMenuLib("word周报导出", "/system/formtpl/wordExport");
        // }

        serv.runInit();

    }

    private void openApiInit() {
        String userName = "tdsWhiteUser";
        // 白名单初始化
        List<SysOpenInterfaceWhite> read = whiteService.read();
//		Map<String, String> collect = read.stream().collect(Collectors.toMap(SysOpenInterfaceWhite::getCtrlApi, SysOpenInterfaceWhite::getId));
        Map<String, String> collect = new HashMap<>();
        for (SysOpenInterfaceWhite e : read) {
            collect.put(e.getCtrlApi(), e.getId());
        }
        List<SysOpenInterFaceWhiteDto> entities = new ArrayList<>();
        String api = "/tds/getTdsQuery,/tds/getTdsFlowData,/tds/getListTdsOutPara,/tdsBtn/getTdsBtnData,/tds/getTDS/**,/tds/getTdsData";
        List<String> apiList = Arrays.asList(api.split(","));
        for (String s : apiList) {
            if (!collect.containsKey(s)) {
                SysOpenInterFaceWhiteDto white = new SysOpenInterFaceWhiteDto();
                white.setModuleCode("system");
                white.setEnabled(1);
                white.setUserName(userName);
                white.setExpireDate(DateTimeUtils.parseDate("2023-10-10 00:00:00"));
                white.setCtrlApi(s);
                white.setTDSROW_rowFlag(0);
                entities.add(white);
            }
        }
        if (StringUtils.isNotEmpty(entities)) {
            whiteService.save(entities);
        }
    }

    /**
     * 判断是否初始化
     *
     * @return
     */
    private boolean isInit() {
        return true;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 初始化租房字段及索引
        initTenant();
        // 收集controller函数及注释信息
        ControllerFunctionMap.collectionControllerFunctionMap();
        // 初始化含有clob字段的表信息
        BlobColumnUtils.initBlobMap();
        if (this.isInit()) {
            this.init();
        }
    }

    /**
     * 初始化租户字段及索引
     */
    public void initTenant() {
        if (MultiTenantUtils.enalbe()) {
            ddl.initDbTableTenantIdField();
        }
    }

    // 默认第三方登录配置信息
    public String getThirdLoginValue() {
        return "{\"forminfo\":[{\"default\":\"https://app.ceic.com/webservice/ssologin\",\"name\":\"第三方url\",\"key\":\"url\"},{\"default\":\"data.employee_num\",\"name\":\"返回数据Key\",\"key\":\"dataKey\"},{\"default\":\"ww0ef518287bfe5000\",\"name\":\"app标识\",\"key\":\"appid\"},{\"default\":\"staffNo\",\"name\":\"认证模式\",\"key\":\"authMode\"}],\"value\":{\"url\":\"\",\"dataKey\":\"\",\"appid\":\"\",\"authMode\":\"\"}}";
    }
}
