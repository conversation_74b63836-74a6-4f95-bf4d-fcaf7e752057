package com.yunhesoft.system.post.service;


import java.util.List;

import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.post.entity.dto.SaveDto;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.post.entity.vo.PostVo;

/**
 * @Description: 自定义机构岗位
 * <AUTHOR>
 * @date 2024/12/13
 */
public interface ISysDiyPost {
    /**
     * 保存自定义的岗位
     * <AUTHOR>
     * @date 2024/12/13
     * @params
     * @return
     *
    */
    Boolean saveDiyPost(SaveDto saveDto);
    /**
     * 查询自定义岗位 根据机构直接查询
     * <AUTHOR>
     * @date 2024/12/13
     * @params
     * @return
     *
    */
    List<PostVo> getDiyPost(String orgCode, String postName, Pagination<?> page);
    /**
     * 获取机构岗位根节点
     * @category 
     * @param orgCode
     * @return
     */
    SysPost getDiyPostRoot(String orgCode,SysOrg org);
    /**
     * 根据任意机构代码向上获取自定义岗位
     *
     * @return
     * <AUTHOR>
     * @date 2024/12/13
     * @params
     */
    List<PostVo> getDiyPostByOrgCode(String orgCode, Pagination<?> page, String postName);

    String isUseOrgDiyPost();
}
