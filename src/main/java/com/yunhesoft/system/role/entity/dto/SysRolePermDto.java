package com.yunhesoft.system.role.entity.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 角色权限查询参数
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "角色权限查询参数", description = "角色权限查询参数")
public class SysRolePermDto {

	/** 角色id */
	@ApiModelProperty(value = "roleid")
	@NotBlank(message = "roleid不能为空")
	private String roleid;

	/** 权限id */
	@ApiModelProperty(value = "permid")
	private String permid;

	/** 权限id列表 */
	@ApiModelProperty(value = "permids")
	private List<String> permids;

}
