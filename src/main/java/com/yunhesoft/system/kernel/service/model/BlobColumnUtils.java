package com.yunhesoft.system.kernel.service.model;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.sql.Clob;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Entity;

import org.springframework.core.env.Environment;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.yunhesoft.core.common.utils.EntityUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.AnnotationClassScanner;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.entity.po.SysBlobStore;
//import com.yunhesoft.system.kernel.service.entity.po.SysBlobStoreTest;

import lombok.extern.log4j.Log4j2;

/**
 * clob 大数据字段处理（数据单独存储在表SYS_BLOB_STORE中）
 * 
 * <AUTHOR>
 *
 */

@Log4j2
public class BlobColumnUtils {

	// 是否启用
	private static boolean enable = false;
	// 数据库关键字列表
	private static Map<String, List<String>> clobMap = new HashMap<String, List<String>>();

	private final static String CREATE_TIME = "CREATE_TIME"; // 创建时间字段
	private final static String CREATE_BY = "CREATE_BY"; // 创建人字段
	private final static String UPDATE_TIME = "UPDATE_TIME"; // 更新时间字段
	private final static String UPDATE_BY = "UPDATE_BY"; // 更新人字段
	private final static String CREATE_BY_ORG = "CREATE_BY_ORG"; // 创建机构编码
	private final static String CREATE_BY_POST = "CREATE_BY_POST"; // 创建岗位编码
	private final static String ID = "ID"; // 创建岗位编码
	private final static int SPLICT_COUNT = 2000; // 字符分割数量

	static {
		// 在配置文件[application-oracle.properties] 中配置
		String s = SpringUtils.getBean(Environment.class).getProperty("app.database.splictclob", "false");
		if ("true".equals(s)) {
			enable = true;
			log.info("app.database.splictclob=true,系统将对clob字段数据存储在[SYS_BLOB_STORE]表中");
		}
		// enable = true;// test
	}

	/**
	 * 是否使用clob 字段 单独存储功能
	 * 
	 * @return
	 */
	public static boolean enable() {
		return enable;
	}

	/**
	 * 初始化clob字段
	 */
	public static void initBlobMap() {
		if (!enable) {
			return;
		}
		List<Class<?>> list = AnnotationClassScanner.getClassesWithAnnotationFromPackage("com.yunhesoft", Entity.class);
		if (list != null) {
			for (Class<?> clazz : list) {
				String table = EntityUtils.tableName(clazz).toUpperCase();
				List<Field> fields = ObjUtils.reflectFieldList(clazz);
				if (fields != null) {
					for (Field field : fields) {
						String fieldName = field.getName().toUpperCase();
						if (ID.equalsIgnoreCase(fieldName)) {
							continue;
						}
						if (fieldName.equalsIgnoreCase(CREATE_TIME) || fieldName.equalsIgnoreCase(CREATE_BY)
								|| fieldName.equalsIgnoreCase(UPDATE_TIME) || fieldName.equalsIgnoreCase(UPDATE_BY)
								|| fieldName.equalsIgnoreCase(CREATE_BY_ORG)
								|| fieldName.equalsIgnoreCase(CREATE_BY_POST)) {
							continue;
						}
						String typeName = field.getType().getName();
						if ("java.lang.String".equalsIgnoreCase(typeName)) {
							Annotation[] annotations = field.getDeclaredAnnotations();
							if (annotations != null) {
								for (Annotation t : annotations) {
									if ("javax.persistence.Lob".equalsIgnoreCase(t.annotationType().getName())) {
										if (clobMap.containsKey(table)) {
											clobMap.get(table).add(fieldName);
										} else {
											List<String> l = new ArrayList<String>();
											l.add(fieldName);
											clobMap.put(table, l);
										}
									}
								}
							}
						}
					}
				}
			}
		}
		log.info("系统中含有clob表字段：" + clobMap.toString());
	}

	private static boolean containsBlob(String table) {
		return clobMap.containsKey(table);
	}

	public static <T> void saveBlob(String type, ExecParam param) {
		saveBlob(type, param.getTableName(), param.getFields(), param.getBatchArgs());
	}

	public static <T> void saveBlob(String type, String tableName, List<Field> fieldList, List<Object[]> batchArgs) {
		try {
			if (!enable) {
				return;
			}
			String table = tableName.toUpperCase();// 表名称
			if (!containsBlob(table)) {// 不含有 clob字段
				return;
			}
			// List<Field> list = param.getFields();
			if (fieldList != null) {
				int index = -1;
				List<Integer> blobIndexList = new ArrayList<Integer>();
				int idIndex = -1;
				List<String> cList = clobMap.get(table);
				for (Field field : fieldList) {
					index++;
					String fieldName = field.getName().toUpperCase();
					if (ID.equalsIgnoreCase(fieldName)) {
						idIndex = index;
					} else {
						if (cList != null && cList.contains(fieldName)) {// 大数据字段
							blobIndexList.add(index);
						}
					}
				}
				if ("delete".equals(type)) {
					List<String> ids = new ArrayList<String>();
					if (idIndex >= 0) {
						for (Object[] obj : batchArgs) {
							String dataId = obj[idIndex].toString();
							ids.add(dataId);
						}
						deleteBlob(table, ids);
					}
				} else {
					if (blobIndexList.size() > 0) {// 有clob字段
						if (idIndex >= 0) {
							Map<String, Map<String, String>> dataValue = new HashMap<String, Map<String, String>>();
							for (Object[] obj : batchArgs) {
								String dataId = obj[idIndex].toString();// model.getArgs().get(idIndex).toString();
								Map<String, String> dValue = new HashMap<String, String>();
								for (Integer i : blobIndexList) {
									Object val = obj[i]; // model.getArgs().get(i);
									String fName = fieldList.get(i).getName().toUpperCase();
									if (val != null) {
										dValue.put(fName, val.toString());
									}
								}
								dataValue.put(dataId, dValue);
								for (Integer i : blobIndexList) {
									obj[i] = null;// model.getArgs().set(i, null);// 清空原记录数据
								}
							}
							saveBlob(type, table, dataValue);// 单独存储大数据类型字段

						}
					}
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}
	}

	/**
	 * 保存大数据字段
	 * 
	 * @param <T>
	 * @param type
	 * @param entity
	 * @param model
	 */
	public static <T> void saveBlob(String type, T entity, SQLModel model) {
		try {
			if (!enable) {
				return;
			}
			String table = EntityUtils.tableName(entity.getClass()).toUpperCase();// 表名称
			if (!containsBlob(table)) {// 不含有 clob字段
				return;
			}
			List<Field> list = model.getFields();
			if (list != null && model.getArgs().size() > 0) {
				List<Object[]> batchArg = new ArrayList<Object[]>();
				batchArg.add(model.getArgs().toArray());
				saveBlob(type, table, list, batchArg);
				List<Object> args = Arrays.asList(batchArg.get(0));
				model.setArgs(args);
			}
		} catch (Exception e) {
			log.error("", e);
		}
	}

	/**
	 * 删除大数据存储
	 * 
	 * @param <T>
	 * @param table
	 * @param dataId
	 * @return
	 */
	private static <T> boolean deleteBlob(String table, List<String> dataIds) {
		Where where = Where.create();
		if (dataIds.size() == 1) {
			where.eq(SysBlobStore::getDataId, dataIds.get(0));
		} else {
			where.in(SysBlobStore::getDataId, dataIds.toArray());
		}

		where.eq(SysBlobStore::getTableName, table.toUpperCase());
		SpringUtils.getBean(EntityService.class).delete(SysBlobStore.class, where);
		return true;
	}

	/**
	 * 保存blob字段类型
	 * 
	 * @param <T>
	 * @param type
	 * @param table
	 * @param dataId
	 * @param dataValue
	 * @return
	 */
	private static <T> boolean saveBlob(String type, String table, Map<String, Map<String, String>> dataValue) {
		// System.out.println("======================");
		try {
			if (StringUtils.isNotEmpty(dataValue)) {
				List<String> ids = new ArrayList<String>();
				if ("delete".equals(type)) {// 删除
					for (String dataid : dataValue.keySet()) {
						ids.add(dataid);
					}
					deleteBlob(table, ids);
				} else {// 修改或者添加
					List<SysBlobStore> list = new ArrayList<SysBlobStore>();
					for (String dataId : dataValue.keySet()) {
						ids.add(dataId);
						// 先删除后添加

						Map<String, String> dValue = dataValue.get(dataId);
						for (String k : dValue.keySet()) {
							String value = dValue.get(k);
							List<String> strList = splictBylen(value, SPLICT_COUNT);// 按照字符长度进行分割
							if (StringUtils.isNotEmpty(strList)) {
								int n = 0;
								for (String s : strList) {
									n++;
									SysBlobStore bean = new SysBlobStore();
									bean.setId(TMUID.getUID());
									bean.setBlobData(s);
									bean.setColName(k.toUpperCase());
									bean.setDataId(dataId);
									bean.setTableName(table.toUpperCase());
									bean.setTmSort(n);
									list.add(bean);
								}
							}
						}

					}
					if (deleteBlob(table, ids)) {
						if (list.size() > 0) {
							SpringUtils.getBean(EntityService.class).insertBatch(list, 500);
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("", e);
			return false;
		}
		return true;
	}

	/**
	 * 获取大字符数据
	 * 
	 * @param table      表名（实体对象）
	 * @param colName    （列名）
	 * @param dataIdList 数据id
	 * @return
	 */
//	private static String getBlobString(String table, String colName, String dataId) {
//		List<String> dataIdList = new ArrayList<String>();
//		dataIdList.add(dataId);
//		Map<String, StringBuffer> map = getBlobString(table, colName, dataIdList);
//		if (map != null && map.containsKey(dataId)) {
//			return map.get(dataId).toString();
//		}
//		return null;
//	}

	/**
	 * 获取大字符数据
	 * 
	 * @param table      表名（实体对象）
	 * @param colName    （列名）
	 * @param dataIdList 数据id
	 * @return
	 */
	private static Map<String, StringBuffer> getBlobString(String table, String colName, List<String> dataIdList) {
		Map<String, StringBuffer> map = new HashMap<String, StringBuffer>();
		try {
			if (StringUtils.isNotEmpty(dataIdList)) {
				Where where = Where.create();
				where.eq(SysBlobStore::getTableName, table.toUpperCase());
				if (dataIdList.size() == 1) {
					where.eq(SysBlobStore::getDataId, dataIdList.get(0));
				} else {
					where.in(SysBlobStore::getDataId, dataIdList.toArray());
				}
				where.eq(SysBlobStore::getColName, colName.toUpperCase());
				Order order = Order.create();
				order.orderByAsc(SysBlobStore::getDataId);
				order.orderByAsc(SysBlobStore::getTmSort);
				List<SysBlobStore> dataList = SpringUtils.getBean(EntityService.class).queryData(SysBlobStore.class,
						where, order, null);
				if (StringUtils.isNotEmpty(dataList)) {
					for (SysBlobStore e : dataList) {
						String dataStr = e.getBlobData();
						if (dataStr != null) {
							String dataid = e.getDataId();
							if (map.containsKey(dataid)) {
								map.put(dataid, map.get(dataid).append(dataStr));
							} else {
								StringBuffer sb = new StringBuffer();
								sb.append(dataStr);
								map.put(dataid, sb);
							}
						}
					}
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return map;
	}

	/**
	 * 通过字符长度进行分割
	 * 
	 * @param str
	 * @param length
	 * @return
	 */
	private static List<String> splictBylen(String str, int length) {
		List<String> list = new ArrayList<String>();
		if (StringUtils.isNotEmpty(str)) {
			if (str.length() <= length) {
				list.add(str);
				return list;
			}
			for (int i = 0; i < str.length(); i += length) {
				int endIndex = Math.min(i + length, str.length());
				String subStr = str.substring(i, endIndex);
				list.add(subStr);
			}
		}
		return list;

	}

	private static <T> Map<String, Field> getFieldsMap(Class<T> clazz) {
		Map<String, Field> map = new HashMap<String, Field>();
		List<Field> fields = ObjUtils.reflectFieldList(clazz);
		for (Field field : fields) {
			if (!EntityUtils.isTransientField(field)) {
				map.put(field.getName().toUpperCase(), field);
				// columns.add(field);
			}
		}
		return map;

	}

	/**
	 * 获取blob字段值，进行赋值
	 * 
	 * @param <T>
	 * @param resultClass
	 * @param entity
	 */
	public static <T> void setBlobData(Class<T> resultClass, T entity) {
		if (enable && entity != null) {
			List<T> list = new ArrayList<T>();
			list.add(entity);
			setBlobData(resultClass, list);
		}
	}

	/**
	 * 获取blob字段值，进行赋值
	 * 
	 * @param <R>
	 * @param resultClass
	 * @param list
	 */
	public static <R> void setBlobData(Class<R> resultClass, List<R> list) {
		if (enable) {
			String table = EntityUtils.tableName(resultClass).toUpperCase();// 表名称
			if (!containsBlob(table)) {// 不含有 clob字段
				return;
			}
			if (StringUtils.isNotEmpty(list)) {
				try {
					List<String> idList = new ArrayList<String>();
					Map<String, Integer> idxMap = new HashMap<String, Integer>();
					int n = 0;
					for (R r : list) {
						Object id = EntityUtils.getValue(ID, r);
						if (id != null) {
							idList.add(id.toString());
							idxMap.put(id.toString(), n);
						}
						n++;
					}
					if (idList.size() > 0) {
						List<String> cols = clobMap.get(table);
						for (String col : cols) {
							Map<String, StringBuffer> dataMap = getBlobString(table, col, idList);
							if (StringUtils.isNotEmpty(dataMap)) {
								Map<String, Field> fMap = getFieldsMap(resultClass);
								Field field = fMap.get(col);
								if (field != null) {
									for (String key : dataMap.keySet()) {
										String value = dataMap.get(key).toString();
										Integer index = idxMap.get(key);
										EntityUtils.setValue(field, list.get(index), value);
									}
								}
							}
						}
					}
				} catch (Exception e) {
					log.error("", e);
				}
			}
		}
	}

//	public static boolean testSaveBlob() {
//		List<SysBlobStoreTest> list = new ArrayList<SysBlobStoreTest>();
//		SysBlobStoreTest bean = new SysBlobStoreTest();
//		bean.setId(TMUID.getUID());
//		bean.setBlobData("1234567890abcdefgh");
//
//		bean.setTableName("SYS_BLOB_STORE_TEST");
//		bean.setTmSort(1);
//		list.add(bean);
//		SpringUtils.getBean(EntityService.class).insertBatch(list, 500);
//		return true;
//	}
//
//	public static List<SysBlobStoreTest> testGetSaveBlob() {
//		Where where = Where.create();
//		where.eq(SysBlobStoreTest::getTmSort, 1);
//		return SpringUtils.getBean(EntityService.class).queryData(SysBlobStoreTest.class, where, null, null);
//	}

	/**
	 * 获取系统blob插入语句
	 * 
	 * @param tableName
	 * @param blobCol
	 * @param where
	 * @param splictCount
	 * @return
	 */
	public static String getSysBlobStoreSQL(String tableName, String blobCol, String where, Integer splictCount) {
		StringBuffer sb = new StringBuffer();
		String sql = "select ID as ID, " + blobCol + " as BLOBCOL from " + tableName;
		if (StringUtils.isNotEmpty(where)) {
			sql += " where " + where;
		}
		log.info("****************************");
		log.info("Start-Blob转换语句：" + sql);
		SqlRowSet rs = SpringUtils.getBean(EntityService.class).rawQuery(sql, null);
		while (rs.next()) {
			String dataId = rs.getString("ID");
			Object d = rs.getObject("BLOBCOL");
			if (d != null) {
				Class<? extends Object> srcClazz = d.getClass();
				String clzName = srcClazz.getName();
				if ("javax.sql.rowset.serial.SerialClob".equalsIgnoreCase(clzName)
						|| "java.lang.String".equalsIgnoreCase(clzName)) {
					try {
						String value = null;
						if ("javax.sql.rowset.serial.SerialClob".equalsIgnoreCase(clzName)) {
							Clob clob = (Clob) d;
							if (clob.length() > 0) {
								value = clob.getSubString(1, (int) clob.length());
							}
						} else {
							value = d.toString();
						}
						if (value != null) {
							List<String> strList = splictBylen(value, splictCount == null ? SPLICT_COUNT : splictCount);// 按照字符长度进行分割
							if (StringUtils.isNotEmpty(strList)) {
								int n = 0;
								for (String s : strList) {
									n++;
									SysBlobStore bean = new SysBlobStore();
									bean.setId(TMUID.getUID());
									bean.setBlobData(s);
									bean.setColName(blobCol.toUpperCase());
									bean.setDataId(dataId);
									bean.setTableName(tableName.toUpperCase());
									bean.setTmSort(n);
									sb.append(getInsertBlobStoreSQL(bean));// 生成sql语句
									// list.add(bean);
								}
							}
						}
					} catch (Exception e) {
						log.error("格式转换错误", d);
					}
				}
			}
		}
		log.info("End-Blob转换语句.");
		return sb.toString();
	}

	private static String getInsertBlobStoreSQL(SysBlobStore bean) {
		StringBuffer sb = new StringBuffer();
		String data = bean.getBlobData();
		if (StringUtils.isNotEmpty(data)) {
			data = data.replaceAll("'", "''");
			sb.append("insert into SYS_BLOB_STORE(ID,BLOB_DATA,COL_NAME,DATAID,TABLE_NAME,TMSORT) ");
			sb.append("values('" + bean.getId() + "','" + data + "','" + bean.getColName() + "','" + bean.getDataId()
					+ "','" + bean.getTableName() + "'," + bean.getTmSort() + ");");
		}
		// System.out.println(sb);
		log.info(sb);
		return sb.toString();
	}

//	public static void main(String[] args) {
//		ClobColumnUtils.initClobMap();
//
//	}

}