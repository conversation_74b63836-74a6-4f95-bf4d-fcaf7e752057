package com.yunhesoft.joblist.operCard.service.impl;

import java.io.ByteArrayOutputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.Coms;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.common.utils.Tree;
import com.yunhesoft.core.common.utils.TreeNode;
import com.yunhesoft.core.utils.HtmlUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.operCard.entity.dto.OpercardInfoDto;
import com.yunhesoft.joblist.operCard.entity.po.OpercardCatalog;
import com.yunhesoft.joblist.operCard.entity.po.OpercardInfo;
import com.yunhesoft.joblist.operCard.entity.po.OpercardInfoVersion;
import com.yunhesoft.joblist.operCard.entity.po.OpercardOperstep;
import com.yunhesoft.joblist.operCard.entity.vo.OperWordCatalogVo;
import com.yunhesoft.joblist.operCard.entity.vo.OperWordRowVo;
import com.yunhesoft.joblist.operCard.service.IParseWordInfoService;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.impl.EntityServiceImpl;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tools.files.service.IFilesInfoService;
import com.yunhesoft.system.tools.files.tools.ImageManagerImpl;

import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLConverter;
import fr.opensagres.poi.xwpf.converter.xhtml.XHTMLOptions;
import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class ParseWordInfoServiceImpl implements IParseWordInfoService {
    @Autowired
    private EntityService entityService;
    @Autowired
    private EntityServiceImpl entservice;
    @Autowired
    private IFilesInfoService fileInfoSrv;
    @Autowired
    private IParseWordInfoService infoServ;
    @Override
    public String parseWord(String cardId, MultipartFile file) throws Exception {
    	String result = "导入失败";
//        String cardId = opercardInfo.getId();
//        String classid = opercardInfo.getClassid();
//        String orgCode = opercardInfo.getOrgCode();
//        SysUser user = SysUserHolder.getCurrentUser();
        boolean isUpdate =false;
    	OpercardInfoVersion opercardInfoVersion = JSON.parseObject(cardId, OpercardInfoVersion.class);
        if (ObjUtils.isEmpty(opercardInfoVersion.getCardId())) {
            //如果操作卡id是空则创建新的操作卡
            cardId = TMUID.getUID();
        } else {
            cardId = opercardInfoVersion.getCardId();
            isUpdate=true;
        }

        List<OpercardCatalog> updateCatalogs = new ArrayList<OpercardCatalog>();
        //导入操作卡时清除原有卡信息,并返回上次操作卡目录别名信息，用于后续的别名对照
        boolean clear = deleteOldData(cardId,updateCatalogs);
        if(clear) {//删除原有数据成功        
	        Map<String,OpercardCatalog> catalogMap = null;
	    	if(StringUtils.isNotEmpty(updateCatalogs)) {//有历史目录
	    		catalogMap =  updateCatalogs.stream()
						.collect(Collectors.toMap(OpercardCatalog::getName, obj -> obj, (key1, key2) -> key2));// 将list转换为map// 重复键值时，第一个key被第二个key覆盖;					
	    	}
	    	if(catalogMap==null) {
	    		catalogMap = new HashMap<String,OpercardCatalog>();
	    	}
//    	  String fileName = file.getOriginalFilename();
//        try {
            //判断文件导入格式
//            String fileFormat = "";
//            if (!ObjUtils.isEmpty(fileName)) {
//                int lastDotIndex = fileName.lastIndexOf('.');
//                // 如果找到了点，则返回点之后的子字符串；否则返回原字符串
//                if (lastDotIndex != -1) {
//                    fileFormat = fileName.substring(lastDotIndex + 1).trim();
//                }
//            }
            //判断当前文件格式
                //docx格式
                String html = "";
                XWPFDocument doc = null;
                try {		
                	 doc = new XWPFDocument(file.getInputStream());
                }catch(Exception e) {
                	log.error(e);
                	return "文档中含有错误格式，无法解析！";
                }
                XHTMLOptions options = XHTMLOptions.create();
                // 3.将word中图片保存到指定目录
                options.setImageManager(new ImageManagerImpl(fileInfoSrv));
                options.setIgnoreStylesIfUnused(false);
                options.setFragment(true);
//                System.out.println("字符串图片：" + options);
                //输入文件改为输出为字节数组
                ByteArrayOutputStream out = new ByteArrayOutputStream();//new FileOutputStream(templatePath + htmlName);
                // 将XWPFDocument转换成XHTML
                XHTMLConverter.getInstance().convert(doc, out, options);
                byte[] obarr = out.toByteArray();
                Charset cs = Charset.defaultCharset();
                html = new String(obarr, cs);//StandardCharsets.UTF_8
                html = html.replace("\"#\"_Toc", "\"#_Toc");
                HashMap<String,StringBuffer> tocMap = new HashMap<String,StringBuffer>();//目录名称map
            	String tocRegex = "\\<a\\s+href\\=\"#(_Toc\\d+)\".+?</a>";//目录解析（懒惰匹配.+?）	
				Pattern tocPattern = Pattern.compile(tocRegex);
				Matcher tocM = tocPattern.matcher(html);//解析目录
 				while (tocM.find()) {
 					String text = HtmlUtils.clearAllHtml(tocM.group()).trim();
 					if(StringUtils.isNotEmpty(text)) {//解析出了目录内容
 						String tocNumber=tocM.group(1);
 						StringBuffer sb = tocMap.get(tocNumber);
 						if(sb==null) {
 							sb=new StringBuffer();
 							tocMap.put(tocNumber, sb);
 						}
 						sb.append(text);
 					}
 				}
 				String tocTargetRegex = "\"(_Toc\\d+)\"";//目标目录解析	
 				Pattern tocTPattern = Pattern.compile(tocTargetRegex);
				String operateRegex = "(&lt;|[\\[［【\\(（＜])\\s*([IiPpMm]|[IiPp][/\\\\\\|]+[IiPp])\\s*(&gt;|[\\]］】\\)）＞])\\s*";//操作标识解析			
				Pattern pOperate = Pattern.compile(operateRegex);
				String operateSplitRegex="(?=("+operateRegex+"))";//正则为零宽断言，断言操作符([p],<M>,(I))的前面0个字符，用于分割多个操作符
				String level1Regex = "^[一二三四五六七八九十]+([\\.、\\s]|&nbsp;)";//一级目录一、二、
				Pattern pLeve1 = Pattern.compile(level1Regex);
				String level2Regex = "^[(（][一二三四五六七八九十]+[)）]";//二级目录(一)、(二)、
				Pattern pLeve2 = Pattern.compile(level2Regex);
				List<TreeNode<OperWordCatalogVo>> treeList = new ArrayList<TreeNode<OperWordCatalogVo>>();
                TreeNode<OperWordCatalogVo> root = new TreeNode<OperWordCatalogVo>("root",null,null);//根节点
                treeList.add(root); 
                OperWordCatalogVo face = createWordCatalog("封面","");
                TreeNode<OperWordCatalogVo> faceNode = new TreeNode<OperWordCatalogVo>(face.getId(),"root",face);//封面节点
                treeList.add(faceNode);
                Tree<OperWordCatalogVo> tree = new Tree<OperWordCatalogVo>(root);
                tree.buildTree(treeList);// 构建树形
                TreeNode<OperWordCatalogVo> currentNode = faceNode;//当前正在解析的目录
                OperWordRowVo lastRow = face.getStepList().get(0);//这里必有，不判断空了。默认为编辑当前目录的第一个步骤节点
                int currentLevel = 1;//当前正在读取的目录级别

                List<String> divList = Coms.getBl(html, "<div[^>]*>[\\s\\S]*?<\\/div>");//分割DIV
                for (String divstr : divList) {
                	String inhtml = divstr.replace("</div>", "").replaceAll("\\<div[^>]*\\>", ""); //去除外层div标签
                	//获取每个区域行数  匹配标签和自闭和标签
                	List<String> plist = Coms.getBl(inhtml, "<([^>\\s]+)(?:[^>]*?)>([\\s\\S]*?)<\\/\\1>|<([^>\\s]+)(?:[^>]*?)\\/>");
                  	for(String temp:plist) {
                  		String[] dataArr = temp.split(operateSplitRegex);//切割指令，防止一行文字有多个指令（正则为零宽断言，断言操作符([p],<M>,(I))的前面0个字符，用于分割多个操作符）
                  		for(String tempData:dataArr) {
                  			String text = HtmlUtils.clearAllHtml(tempData).trim();
                  			if(StringUtils.isNotEmpty(text) || temp.indexOf("<img")>=0) {//解析出了内容 
                  				if(isCatalogText(tempData)) {//文档目录
                  					lastRow.setOpHtml(lastRow.getOpHtml()+tempData);//直接追加到上面的记录中
                  				}else {
                  					boolean isToc = false;//是否为目录
                  					String tocName = null;//目录名称
                  					int tocLevel = 1;//目录级别(默认为1级目录)
                  					Matcher mToc = tocTPattern.matcher(tempData);//查找目录的目标点
	                  				if(mToc.find()){//查找到了,代表输入数据正确有效
	                  					StringBuffer clogName = tocMap.get(mToc.group(1));
	                  					if(clogName!=null) {
	                  						if(isNoToc(clogName.toString())) {
	                  							//防止将A级纲要、B级操作、C级说明作为目录
	                  						}else{
	                  							isToc = true;
	                  							tocName=clogName.toString().trim();
	                  						}
	                  					}
	                  				}
	                  				Matcher mLevel1 = pLeve1.matcher(text);
                  					if(mLevel1.find() && !isNoToc(text)){//目录查找       
                  						tocLevel=1;//记录目录级别
                  						if(!isToc) {
                  							isToc = true;
                  							tocName=text;
                  						}
                  					}else {
                  						Matcher mLevel2 = pLeve2.matcher(text);
                          				if(mLevel2.find() && !isNoToc(text)){//目录查找
                          					tocLevel=2;//记录目录级别
                      						if(!isToc) {
                      							isToc = true;
                      							tocName=text;
                      						}
                          				}
                  					}
	                  				if(isToc) {//是目录
	                  					OperWordCatalogVo clogBean = createWordCatalog(tocName,tempData);
                  	                    TreeNode<OperWordCatalogVo> clogNode = new TreeNode<OperWordCatalogVo>(clogBean.getId(),"",clogBean);//目录节点
                  	                    if(currentLevel>tocLevel) {//解析目录为当前目录为下级
                  	                    	currentNode.appendChild(clogNode);
                  	                    }else if(currentLevel<tocLevel) {//解析目录为当前目录为上级
                  	                    	if(currentNode.parentNode.parentNode!=null) {
                  								currentNode.parentNode.parentNode.appendChild(clogNode);
                  							}else {
                  								currentNode.parentNode.appendChild(clogNode);//如果级别错误，则直接添加到同级（这里正常走不到，容错）
                  							}
                  	                    }else {//同级
                  	                    	currentNode.parentNode.appendChild(clogNode);
                  	                    }
                  						currentNode=clogNode;
                  						lastRow = currentNode.bean.getStepList().get(0);
	                  					
	                  				}else {
  		                        		OperWordRowVo bean = new OperWordRowVo();
  			            				Matcher m = pOperate.matcher(text);
  			            				if(m.find()){//查找到了,代表输入数据正确有效
  			            					String leftSign = m.group(1).replace("（", "(").replace("【","[").replace("［","[").replace("＜", "&lt;").replace("<", "&lt;");//左操作符
  			            					String postSign = m.group(2).toUpperCase().replaceAll("[^IiPpMm]+", "\\|");//操作岗位
  			            					String rightSign = m.group(3).replace("）", ")").replace("】","]").replace("］","]").replace("＞", "&gt;").replace(">", "&gt;");//右操作符
  			            					StringBuffer sb = new StringBuffer();
  			            					m.appendReplacement(sb,"");//替换已经查找到的数据为""
  			            					m.appendTail(sb);//将剩下未匹配的尾部字符添加进StringBuffer
  			            					bean.setOpType(3);//操作步骤
  			            					bean.setOpSign(leftSign+postSign+rightSign);
  			            					bean.setOpSignType(leftSign.replace("&lt;", "<")+rightSign.replace("&gt;", ">"));
  			            					bean.setOpSignPost(postSign);
  			            				//	bean.setOpText(sb.toString().trim().replaceAll("\\s","_"));//操作内容文本
  			            					bean.setOpText(sb.toString());//操作内容文本
  			            					bean.setOpHtml(bean.getOpSign()+bean.getOpText());//显示的操作内容
  			            					currentNode.bean.getStepList().add(bean);
  				            				lastRow=bean;
  			            				}else {
  			            					if(text.indexOf("提示卡")>=0) {
  			            						bean.setOpType(2);//警示
  			            						bean.setOpHtml(tempData);//显示的操作内容
  			            						currentNode.bean.getStepList().add(bean);
  			    	            				lastRow=bean;
  			            					}else {
  			            						if(lastRow!=null && lastRow.getOpType()==3) {//上一步是操作步骤
  				            						if(tempData.indexOf("<table")>=0 || tempData.indexOf("<img")>=0 || (tempData.indexOf("C级")>=0 && tempData.indexOf("说明")>=0)) {//有table或img的，单独成行,C级说明单独成行
  				            							bean.setOpType(1);//文本
  				            							bean.setOpHtml(tempData);//显示的操作内容
  				            							currentNode.bean.getStepList().add(bean);
  					    	            				lastRow=bean;
  				            						}else {//没有特殊的，直接追加到上面的记录中
//  				            							lastRow.setOpHtml(lastRow.getOpHtml()+text.trim().replaceAll("\\s","_"));
  				            							lastRow.setOpHtml(lastRow.getOpHtml()+text);
  				            						}
  			            						}else {
  			            							if(lastRow!=null && lastRow.getOpHtml()!=null && !(lastRow.getOpHtml().indexOf("<table")>=0 || tempData.indexOf("<img")>=0)) {
  			            								lastRow.setOpHtml(lastRow.getOpHtml()+tempData);//直接追加到上面的记录中
  			            							}else {
  			            								bean.setOpType(1);//文本
  				            							bean.setOpHtml(tempData);//显示的操作内容
  				            							currentNode.bean.getStepList().add(bean);
  					    	            				lastRow=bean;
  			            							}
  			            						}
  			            					}
  			            				}
	                  				}
	                        	}
                  			}
                  		}
                  	}
                }
              //目录
              List<OpercardCatalog> catalogList = new ArrayList<OpercardCatalog>();
              //操作步骤
              List<OpercardOperstep> stepList = new ArrayList<OpercardOperstep>();
              //创建操作卡
              createOperCard(root, catalogMap, catalogList, stepList, cardId);
        	  boolean isSave = false;
              try {       

            	  boolean hasStep =false;//是否有操作步骤
                  entservice.begin();// 事务开始    
                  if (StringUtils.isNotEmpty(catalogList)) {
                	  entservice.insertBatch(catalogList, 1000);
    	          }
    	          if (StringUtils.isNotEmpty(stepList)) {
    	        	  hasStep=true;
    	        	  entservice.insertBatch(stepList, 1000);
    	          }
                  entservice.commit();// 事务提交
                  if(hasStep) {
                	  result = "true";//导入成功
                  }else {
                	  result = "noStep";//导入成功，但是没有步骤
                  }
                  isSave=true;
              } catch (Exception e) {
            	  result = "操作卡文件导入异常！";
            	  log.error("操作卡文件导入异常", e);
            	  entservice.rollback();// 事务回滚
              }
              if(isSave && isUpdate) {
            	  infoServ.updateCardStatus(cardId);
              }
        }else {
        	result = "旧数据删除失败，无法导入新数据！";
        }
        return result;
    }
    /**
         * 判断一下是否不是目录
     * @category 
     * <AUTHOR> 
     * @param text
     * @return
     */
    private boolean isNoToc(String text) {
    	boolean result = false;
    	if((text.indexOf("A级")>=0 && text.indexOf("纲要")>=0) 
				|| (text.indexOf("B级")>=0  && text.indexOf("操作")>=0)
				|| (text.indexOf("C级")>=0 && text.indexOf("说明")>=0)) {
			//防止将A级纲要、B级操作、C级说明作为目录
    		result = true;
		}
    	return result;
    }
    /**
     * 创建操作卡
     * @category 创建操作卡
     * <AUTHOR> 
     * @param node
     * @param catalogMap
     * @param catalogList
     * @param stepList
     */
    private void createOperCard(TreeNode<OperWordCatalogVo> node,Map<String,OpercardCatalog> catalogMap,List<OpercardCatalog> catalogList,List<OpercardOperstep> stepList,String cardId) {
		if(StringUtils.isNotEmpty(node.childNodes)){	
			int catalogSort = 1;
			for(TreeNode<OperWordCatalogVo> temp:node.childNodes) {
				if(temp.bean!=null) {
					StringBuffer html = new StringBuffer();
					boolean hasStep=false;//是否有操作步骤
					if(temp.bean.getStepList()!=null) {
						for(OperWordRowVo tempStep:temp.bean.getStepList()) {
							if(tempStep.getOpType()!=null && tempStep.getOpType().intValue()==3) {//有操作步骤
								hasStep=true;
								temp.bean.setCatalogType(2);//可执行操作卡
								break;
							}
						}
						OpercardCatalog cataLog = createCatalog(temp, catalogMap, cardId, catalogSort);//创建目录
						catalogList.add(cataLog);
						catalogSort++;
						int stepSort=1;
						for(OperWordRowVo tempStep:temp.bean.getStepList()) {				
							if(hasStep) {
								OpercardOperstep step = createOperstep(cataLog, tempStep, stepSort);
								stepList.add(step);
								stepSort++;
								html.append(step.getOpContent());//这里要用整理好的数据，不能用原始数据
							}else {
								html.append(tempStep.getOpHtml());
							}
						}
						cataLog.setCardContent(html.toString());//设置显示内容
					}
				}
				createOperCard(temp, catalogMap, catalogList, stepList,cardId);
			}
		}
    }
    /**
     * 生成操作卡目录
     * @category 生成操作卡目录
     * <AUTHOR> 
     * @param bean
     * @param catalogMap
     * @return
     */
    private OpercardCatalog createCatalog(TreeNode<OperWordCatalogVo> node,Map<String,OpercardCatalog> catalogMap,String cardId,int sort) {
    	OpercardCatalog reuslt = new OpercardCatalog();
    	String catalogAlias = null;
    	int catalogType = 1;//目录类型 1:目录 2:可执行子操作卡
    	if(node.bean.getCatalogType()!=null && node.bean.getCatalogType().intValue()==2) {//可执行操作卡
    		OpercardCatalog oldLog = catalogMap.get(node.bean.getName());
    		if(oldLog!=null) {
    			catalogAlias = oldLog.getCatalogAlias();//使用原有的别名
    		}
    		catalogType = 2;
    	}
    	if(catalogAlias==null) {
    		catalogAlias=TMUID.getUID();
    	}
    	reuslt.setCatalogAlias(catalogAlias);//操作卡别名
    	reuslt.setId(node.bean.getId());
    	reuslt.setPid(node.parentId);
    	reuslt.setCardId(cardId);//操作卡id
    	reuslt.setCatalogType(catalogType);//目录类型 1:目录 2:可执行子操作卡
    	if(node.bean.getName()!=null) {
    		reuslt.setName(node.bean.getName().replace("&nbsp;", "").replace("&lt;", "<").replace("&gt;", ">").replaceAll("\\s", ""));//目录名称(清除标签和空白)
    	}
    	reuslt.setTmUsed(1);//是否使用 1：使用；0：不使用
    	reuslt.setTmSort(sort);//排序
	    return reuslt;
    }
    /**
     * 生成操作步骤
     * @category 生成操作步骤
     * <AUTHOR> 
     * @param catalog
     * @param bean
     * @return
     */
    private OpercardOperstep createOperstep(OpercardCatalog catalog,OperWordRowVo bean,int sort) {
        OpercardOperstep opercardOperstep = new OpercardOperstep();

        opercardOperstep.setId(TMUID.getUID());
        //操作卡id
        opercardOperstep.setCardId(catalog.getCardId());
        //操作卡目录别名
        opercardOperstep.setCatalogAlias(catalog.getCatalogAlias());
        //步骤名称
        //opercardOperstep.setName(name);
        //是否使用//1：使用；0：不使用
        opercardOperstep.setTmUsed(1);
        //排序
        opercardOperstep.setTmSort(sort);
        //注释
        //opercardOperstep.setMemo();
        if(bean.getOpType()==null) {
        	bean.setOpType(1);//默认为文本
        }
        //步骤类型 1：文本；2：警示；3：操作步骤
        opercardOperstep.setOpType(bean.getOpType());
        //操作内容
        //String cataName = getRowTitle(rowstr);
        opercardOperstep.setOpContent(bean.getOpHtml());

        if(opercardOperstep.getOpType()!=null && opercardOperstep.getOpType().intValue()==3) {
        	 // 必填步骤//1：必填；0：非必填 默认1
            opercardOperstep.setRequiredStep(1);
            // 确认方式//1手动确认 2身份证 3人脸识别
            opercardOperstep.setConfirmType(1);
            opercardOperstep.setOpSign(bean.getOpSign());//操作标识
            opercardOperstep.setOpSignType(bean.getOpSignType());//操作类型
            opercardOperstep.setOpSignPost(bean.getOpSignPost());//操作岗位
            opercardOperstep.setOpContent("<p>"+opercardOperstep.getOpContent()+"</p>");//追加标签
            
        }else {
        	opercardOperstep.setRequiredStep(0);
        	opercardOperstep.setConfirmType(0);
        }
        //段落ID
        opercardOperstep.setGroupId(catalog.getId());

        return opercardOperstep;
    }
    /**
     * 删除旧数据
     * @category 
     * <AUTHOR> 
     * @param cardId
     * @param oldDataList
     * @return
     */
    private boolean deleteOldData(String cardId,List<OpercardCatalog> oldDataList) {
    	boolean result = false;
    	if(oldDataList!=null) {
	    	List<OpercardCatalog> deleteList = new ArrayList<OpercardCatalog>();
//	    	List<OpercardCatalog> updateList = new ArrayList<OpercardCatalog>();
	        //如果存在使用状态为-1上次的目录信息则删除      //1：使用；0：不使用  -1 上次的目录信息
	        Where where = Where.create();
	        where.eq(OpercardCatalog::getCardId, cardId);
	        where.isNull(OpercardCatalog::getCardVerId);
	        List<OpercardCatalog> catalogList = entityService.queryList(OpercardCatalog.class, where, null, null);
	        if(StringUtils.isNotEmpty(catalogList)) {
	        	for(OpercardCatalog temp:catalogList) {
	        		if(temp.getTmUsed()!=null && temp.getTmUsed().intValue()==1) {
	        			temp.setTmUsed(-1);
	        			oldDataList.add(temp);
	        		}else {
	        			deleteList.add(temp);
	        		}
	        	}
	        }
	        Where where1 = Where.create();
	        where1.eq(OpercardOperstep::getCardId, cardId);
	        where1.isNull(OpercardOperstep::getCardVerId);
	        List<OpercardOperstep> stepList = entityService.queryList(OpercardOperstep.class, where, null, null);
	        if(StringUtils.isNotEmpty(deleteList) || StringUtils.isNotEmpty(oldDataList) || StringUtils.isNotEmpty(stepList)) {
		        try {       
				      entservice.begin();// 事务开始 
				      if(StringUtils.isNotEmpty(deleteList)) {
				    	  entservice.deleteByIdBatch(deleteList, 1000);
				      }
				      if(StringUtils.isNotEmpty(oldDataList)) {
				    	  entservice.updateBatch(oldDataList,1000);
				      }
				      if (StringUtils.isNotEmpty(stepList)) {
				    	  entservice.deleteByIdBatch(stepList, 1000);
				      }
				      entservice.commit();// 事务提交
				      result=true;
				} catch (Exception e) {
				    log.error("操作卡删除异常", e);
				    entservice.rollback();// 事务回滚
				}
	        }else {
	        	result=true;
	        }
    	}
        return result;
    }

    @Override
    public List<OpercardInfo> queryParseWord(Pagination<?> page, OpercardInfoDto opercardInfoDto) {
        Where where = Where.create();
        if (StringUtils.isNotEmpty(opercardInfoDto.getName())) {
            where.like(OpercardInfo::getName, "%" + opercardInfoDto.getName() + "%");
        }
        if (StringUtils.isNotEmpty(opercardInfoDto.getClassid())) {
            where.eq(OpercardInfo::getClassid, opercardInfoDto.getClassid());
        }
        if (StringUtils.isNotEmpty(opercardInfoDto.getOrgCode())) {
            where.eq(OpercardInfo::getOrgCode, opercardInfoDto.getOrgCode());
        }
        where.eq(OpercardInfo::getTmUsed, 1);

        Order order = Order.create();
        order.orderByDesc(OpercardInfo::getCreateTime);
        List<OpercardInfo> opercardInfos = entityService.queryData(OpercardInfo.class, where, order, page);
        return opercardInfos;
    }

    @Override
    public int insertOperCard(List<OpercardInfo> param) {
        List<OpercardInfo> returnList = new ArrayList<>();
        int sort = 1;
        for (OpercardInfo opercardInfo : param) {
            //如果id是空则表示该数据为新增
            if (ObjUtils.isEmpty(opercardInfo.getId())) {
                SysUser user = SysUserHolder.getCurrentUser();
                String opercardInfoID = TMUID.getUID();
                opercardInfo.setId(opercardInfoID);
                //发布人
                opercardInfo.setPublishPerson(user.getRealName());
                /** 排序 */
                opercardInfo.setTmSort(sort);
                /** 操作卡级别 */
                //数据字典：company:公司级；department:部门级;shiftteam:班组级
                opercardInfo.getCardLevel();
                /** 状态 */
                //1：已发布；0：未发布；2:审核中
                opercardInfo.setCardStatus(0);
                /** 审核状态 */
                //1：审核通过；0:未提交；2：审核中；-1：否决
                opercardInfo.setAuditStatus(0);
                /** 是否为使用中 */
                //1：使用中；0：未使用
                opercardInfo.setCardUsing(1);
                opercardInfo.setTmUsed(1);
                /** 操作卡最新版本 */
                //opercardInfo.getCardVer();
                /** 发布人 */
                //opercardInfo.setPublishPerson(user.getRealName());
                /** 发布人id */
                //opercardInfo.setPublishPersonId(user.getId());
                /** 操作卡当前生效版本ID */
                opercardInfo.getCurrentVerId();
                entityService.insert(opercardInfo);
            } else {
                //如果修改操作则，修改发布状态为未发布
                opercardInfo.setCardStatus(0);
                entityService.update(opercardInfo);
            }
            sort++;
        }
        return 0;
    }


    private void creatOpercardInfo(String cardId, String classid, String orgCode, SysUser user) {
        //如果操作卡id为空则需要创建当前操作卡
        OpercardInfo opercardInfo = new OpercardInfo();
        opercardInfo.setId(cardId);
        opercardInfo.setName("导入操作卡");
        opercardInfo.setClassid(classid);
        opercardInfo.setOrgCode(orgCode);
        opercardInfo.setTmUsed(1);
        /** 排序 */
        opercardInfo.setTmSort(0);
        //操作卡编号
        opercardInfo.setCardno("导入编号");
        /** 操作卡级别 */
        //数据字典：company:公司级；department:部门级;shiftteam:班组级
        opercardInfo.setCardLevel("导入操作级别");
        /** 状态 */
        //1：已发布；0：未发布；2:审核中
        opercardInfo.setCardStatus(0);
        /** 审核状态 */
        //1：审核通过；0:未提交；2：审核中；-1：否决
        opercardInfo.setAuditStatus(0);
        /** 是否为使用中 */
        //1：使用中；0：未使用
        opercardInfo.setCardUsing(1);
        opercardInfo.setPublishDt(new Date());
        /** 发布人 */
        opercardInfo.setPublishPerson(user.getRealName());
        /** 发布人id */
        opercardInfo.setPublishPersonId(user.getId());
        entityService.insert(opercardInfo);
    }


    @Override
    public int deleteOperCard(List<OpercardInfo> ids) {
        List<OpercardInfo> opercardInfolist = new ArrayList<>();
        for (OpercardInfo opercardInfo : ids) {
            opercardInfo.setTmUsed(0);
            opercardInfolist.add(opercardInfo);
        }
        entityService.updateBatch(opercardInfolist);
        return 0;
    }

    @Override
    public int updateOperCard(OpercardInfo opercardInfo) {
        entityService.update(opercardInfo);
        return 0;
    }


    //切割字符串每个5000
    private List<String> splitStringBuffer(StringBuffer source, int chunkSize) {
        List<String> chunks = new ArrayList<>();
        if (source == null || chunkSize <= 0) {
            return chunks;
        }

        int totalLength = source.length();
        for (int i = 0; i < totalLength; i += chunkSize) {
            int endIndex = Math.min(i + chunkSize, totalLength);
            chunks.add(source.substring(i, endIndex));
        }
        return chunks;
    }

    //判断字符串是否包含 [P]和(I)以及(M)
    private Boolean isComprise(String regex, String rowstr) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(rowstr);
        return matcher.find();
    }

    private int getInRows(String rowstr) {
        if (rowstr.indexOf("<table") != -1) {
            List<String> list = Coms.getBl(rowstr, "</tr>");
            return list.size();
        }
        return 1;
    }

    private OpercardOperstep opercardOperstep(String cardId, SysUser user, String opContent, int sort, String groupId, int opType, String catalogAlias) {
        OpercardOperstep opercardOperstep = new OpercardOperstep();
        String id = TMUID.getUID();
        opercardOperstep.setId(id);
        //操作卡id
        opercardOperstep.setCardId(cardId);
        //操作卡目录别名
        opercardOperstep.setCatalogAlias(catalogAlias);
        //步骤名称
        //opercardOperstep.setName(name);
        //是否使用//1：使用；0：不使用
        opercardOperstep.setTmUsed(1);
        //排序
        opercardOperstep.setTmSort(sort);
        //注释
        //opercardOperstep.setMemo();
        //步骤类型 1：文本；2：警示；3：操作步骤
        opercardOperstep.setOpType(opType);
        //操作内容
        //String cataName = getRowTitle(rowstr);
        opercardOperstep.setOpContent(opContent);

        if (opType == 1) {
            // 必填步骤//1：必填；0：非必填 默认1
            opercardOperstep.setRequiredStep(0);
            // 确认方式//1手动确认 2身份证 3人脸识别
//            opercardOperstep.setConfirmType();
        }else{
            // 必填步骤//1：必填；0：非必填 默认1
            opercardOperstep.setRequiredStep(1);
            // 确认方式//1手动确认 2身份证 3人脸识别
            opercardOperstep.setConfirmType(1);
        }


        //如果该行数据为操作步骤，则判断具体匹配值
        if (opType == 3) {
            //String regex = "\\[P\\]|\\【P\\】|\\［P\\］|\\[I\\]|\\【I\\】|\\［I\\］|\\[M\\]|\\【M\\】|\\［M\\］";
            String content = getRowTitle(opContent);
            String name = "";
            //操作标识   完整的标识 [P] [I] (M)
            if (content.length() >= 3) {
                name = content.substring(0, 3);
            }
            if (isComprise("^[\\[【［]", name)) {
                //不使用解析的数据，怕格式匹配不上
                if (isComprise("[P]", name)) {
                    //操作标识岗位   //I:内操 P:外操 M班长
                    opercardOperstep.setOpSign("[P]");
                    opercardOperstep.setOpSignPost("P");
                } else if (isComprise("[I]", name)) {
                    opercardOperstep.setOpSign("[I]");
                    opercardOperstep.setOpSignPost("I");
                } else if (isComprise("[M]", name)) {
                    opercardOperstep.setOpSign("[M]");
                    opercardOperstep.setOpSignPost("M");
                } else {
                    opercardOperstep.setOpSign(name);
                    opercardOperstep.setOpSignPost(name);
                }
                //操作标识类型  []:操作 ():确认 <>:安全
                opercardOperstep.setOpSignType("[]");
            } else if (isComprise("^[(（]", name)) {
                if (isComprise("[P]", name)) {
                    //操作标识岗位   //I:内操 P:外操 M班长
                    opercardOperstep.setOpSign("(P)");
                    opercardOperstep.setOpSignPost("P");
                } else if (isComprise("[I]", name)) {
                    opercardOperstep.setOpSign("(I)");
                    opercardOperstep.setOpSignPost("I");
                } else if (isComprise("[M]", name)) {
                    opercardOperstep.setOpSign("(M)");
                    opercardOperstep.setOpSignPost("M");
                } else {
                    opercardOperstep.setOpSign(name);
                    opercardOperstep.setOpSignPost(name);
                }
                opercardOperstep.setOpSignType("()");
            } else {
                if (isComprise("[P]", name)) {
                    //操作标识岗位   //I:内操 P:外操 M班长
                    opercardOperstep.setOpSign("<P>");
                    opercardOperstep.setOpSignPost("P");
                } else if (isComprise("[I]", name)) {
                    opercardOperstep.setOpSign("<I>");
                    opercardOperstep.setOpSignPost("I");
                } else if (isComprise("[M]", name)) {
                    opercardOperstep.setOpSign("<M>");
                    opercardOperstep.setOpSignPost("M");
                } else {
                    opercardOperstep.setOpSign(name);
                    opercardOperstep.setOpSignPost(name);
                }
                opercardOperstep.setOpSignType("<>");
            }
        }

        //段落ID
        opercardOperstep.setGroupId(groupId);

        return opercardOperstep;
    }

    private OpercardCatalog createNode(String cardId, SysUser user, String cataName, int sort, String pid, List<OpercardCatalog> updateCatalogs) {
        //根据更新的内容，匹配节点名称
        String id = TMUID.getUID();
        List<OpercardCatalog> copeCatalogs = new ArrayList<>();
        OpercardCatalog opercardCatalog = new OpercardCatalog();
        //节点创建时默认赋值随机别名id
        opercardCatalog.setCatalogAlias(TMUID.getUID());
        //updateCatalogs   上次导入的信息
        for (OpercardCatalog catalog : updateCatalogs) {
            if (cataName.equals(catalog.getName())) {
                //名称匹配后，赋值上次操作目录id
                opercardCatalog.setCatalogAlias(catalog.getId());
            }
        }

        opercardCatalog.setId(id);
        //操作卡id
        opercardCatalog.setCardId(cardId);
        //父目录  根节点父id不赋值
        opercardCatalog.setPid(pid);
        //目录别名
        //opercardCatalog.setCatalogAlias();
        //目录类型 //1:目录 2:可执行子操作卡
        //opercardCatalog.setCatalogType(catalogType);
        //目录名称
        opercardCatalog.setName(cataName);
        //是否使用 1：使用；0：不使用
        opercardCatalog.setTmUsed(1);
        //排序
        opercardCatalog.setTmSort(sort);
        // 注释
        //opercardCatalog.setMemo();
        //操作卡内容
        //opercardCatalog.setCardContent(cardContent);

        return opercardCatalog;
    }

    private String getRowTitle(String rowstr) {
        //去除格式，获取文字内容，并提取
        String title = rowstr.replace("\r", "").replace("\n", "");
        title = title.replaceAll("</?p[^>]*>", "").replaceAll("<br[^>]*>", "").replaceAll("<BR[^>]*>", "");
        //表格特殊处理
        if (title.startsWith("<table")) {//取第一行内容
            List<String> trlist = Coms.getBl(title, "<tr[^>]*>.*?</tr>");
            if (trlist.size() > 0) {
                title = trlist.get(0).replaceAll("</?[^>]*>", "");
            } else {
                title = "";
            }
        } else {
            title = title.replaceAll("</?[^>]*>", "");
        }
        if (StringUtils.isEmpty(title)) {
            title = "节点";
        }
        return title;
    }

    private OpercardCatalog insertRootNode(String cardId, SysUser user, String fileName) {
        OpercardCatalog opercardCatalog = new OpercardCatalog();
        opercardCatalog.setId(TMUID.getUID());
        //操作卡id
        opercardCatalog.setCardId(cardId);
        //父目录  根节点父id不赋值
        opercardCatalog.setPid("root");
        //目录别名
        //opercardCatalog.setCatalogAlias();
        //目录类型
        opercardCatalog.setCatalogType(1);
        //目录名称
        opercardCatalog.setName(fileName);
        //是否使用 1：使用；0：不使用
        opercardCatalog.setTmUsed(1);
        //排序
        opercardCatalog.setTmSort(1);
        // 注释
        //opercardCatalog.setMemo();
        //操作卡内容
        //opercardCatalog.setCardContent();

        entityService.insert(opercardCatalog);
        return opercardCatalog;
    }

    private OpercardInfoVersion insertVersion(String opercardId, SysUser user) {
        OpercardInfoVersion opercardInfoVersion = new OpercardInfoVersion();
        opercardInfoVersion.setId(TMUID.getUID());
        //操作卡ID
        opercardInfoVersion.setCardId(opercardId);
        //操作卡版本
        //opercardInfoVersion.setCardVer();
        //发布时间
        opercardInfoVersion.setPublishDt(new Date());
        // 发布人
        opercardInfoVersion.setPublishPerson(user.getRealName());
        //发布人id
        opercardInfoVersion.setPublishPersonId(user.getId());
        // 当前生效状态 0不生效 1生效 全部版本中仅一个版本生效，其他都为不生效
        opercardInfoVersion.setCurrentStatus(0);
        entityService.insert(opercardInfoVersion);
        return opercardInfoVersion;
    }
    ///////////////////////////////////////////////////////////////////
	/**
	 * 截断字符串，保证字符串的长度不超出byteLength
	 * 
	 * @param Str        (String) 要截断的字符串
	 * @param byteLength (int) 要保留的字符串byte长度(英文1长度 中文2长度)
	 * @return String 截断后的字符串
	 */
    private static String subStringByte(String Str, int byteLength) {

		String result = Str;

		if (Str != null && Str.length() != 0 && byteLength > 0) {// 传入数据有效

			byte[] original = Str.getBytes();// 获得原始字符串的byte数组

			if (original.length > byteLength) {// 字符byte数组长度超出最大长度了,需要进行字符截取

				byte[] copy = new byte[byteLength + 1];// 生成长度加1的byte数组

				for (int i = 0; i < copy.length; i++) {

					copy[i] = original[i];

				}

				String copyStr = new String(copy);// 根据截断的字符byte数组生成字符串

				result = copyStr.substring(0, copyStr.length() - 1);// 去掉最后一个字符以保证截取的长度(保证最后一个字符不是被截断的字符)

			}

		}

		return result;
	}
    /**
         * 创建目录
     * @category 创建目录
     * <AUTHOR> 
     * @param text
     * @param html
     * @return
     */
    private OperWordCatalogVo createWordCatalog(String text,String html) {
		OperWordCatalogVo clog = new OperWordCatalogVo();
		clog.setId(TMUID.getUID());
		clog.setCatalogType(1);//普通目录
		clog.setName(subStringByte(text,500));
		
		OperWordRowVo firstRow = new OperWordRowVo();//上一个行记录
		firstRow.setOpType(1);
		firstRow.setOpHtml(html);//初始化第一个记录
		clog.getStepList().add(firstRow);
    	
		return clog;
    }
    /**
     * 判断是否为文档目录
     * @category 判断是否为文档目录
     * <AUTHOR> 
     * @param html
     * @return
     */
    private boolean isCatalogText(String html) {
    	boolean result = false;
    	if(html!=null && html.indexOf("<a href=\"#_Toc")>=0) {//找到的是个文档目录
    		result = true;
    	}
    	return result;
    }
    /**
     * 查询所有操作卡
     * TODO
     * @see com.yunhesoft.joblist.operCard.service.IParseWordInfoService#queryAllParseWord(java.lang.String)
     */
	@Override
	public List<OpercardInfo> queryAllParseWord(String orgCode) {
		// TODO Auto-generated method stub
		Where where = Where.create();
		where.eq(OpercardInfo::getOrgCode,orgCode);
        List<OpercardInfo> opercardInfos = entityService.queryList(OpercardInfo.class, where);
        return opercardInfos;
	}
	/**
	 * 更新操作卡为未发布状态
	 * TODO
	 * @see com.yunhesoft.joblist.operCard.service.IParseWordInfoService#updateCardStatus(java.lang.String)
	 */
	@Override
	public void updateCardStatus(String cardId) {
		// TODO Auto-generated method stub
        OpercardInfo opercardInfo = entityService.queryObjectById(OpercardInfo.class,cardId);
        if(opercardInfo!=null) {
        	if(opercardInfo.getCardStatus()==null || opercardInfo.getCardStatus().intValue()!=0) {//非未发布状态
        		opercardInfo.setCardStatus(0);//修改为未发布状态
        		entityService.update(opercardInfo);
        	}
        }
	}
	 /**
     * 根ID查询操作卡
     * @category 根ID查询操作卡
     * <AUTHOR> 
     * @param cardId 
     * @return OpercardInfo
     */
	@Override
	public OpercardInfo getOpercardInfo(String cardId) {
		// TODO Auto-generated method stub
		return entityService.queryObjectById(OpercardInfo.class,cardId);
	}
}
