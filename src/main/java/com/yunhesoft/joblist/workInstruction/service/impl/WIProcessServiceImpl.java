package com.yunhesoft.joblist.workInstruction.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.gmsg.client.GWebSocketClient;
import com.yunhesoft.joblist.config.JoblistModuleConfig;
import com.yunhesoft.joblist.entity.dto.JobListModuleConfigDto;
import com.yunhesoft.joblist.operCard.entity.dto.OperCardExecInitDto;
import com.yunhesoft.joblist.operCard.entity.vo.OperCardInitResultVo;
import com.yunhesoft.joblist.operCard.service.IOpercardExecService;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataDto;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIDataQueryDto;
import com.yunhesoft.joblist.workInstruction.entity.dto.WIProcessDto;
import com.yunhesoft.joblist.workInstruction.entity.po.*;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIProcessDataStepVo;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIProcessSettingVo;
import com.yunhesoft.joblist.workInstruction.service.IWIProcessConfigService;
import com.yunhesoft.joblist.workInstruction.service.IWIProcessVarService;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.shift.shift.service.IShiftService;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.IEmployeeBasicOperationService;
import com.yunhesoft.system.employee.service.ISysEmployeeChangeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.employee.service.impl.EmployeeBasicOperationImpl;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Update;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.service.ISysOrgService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
public class WIProcessServiceImpl {

    @Autowired
    private EntityService entityService;

    @Autowired
    private EmployeeBasicOperationImpl empSrv;

    @Autowired
    private ISysOrgService orgSrv;

    @Autowired
    private ISysEmployeeInfoService orgInfoSrv;

    @Autowired
    private IEmployeeBasicOperationService empBasicSrv;

    @Autowired
    private ISysEmployeeChangeInfoService empChangeInfoSrv;

    @Autowired
    private IWIProcessVarService processVarSrv;

    // 在服务类中添加事务管理器注入
    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private IOpercardExecService opercardExecService;

    @Autowired
    private IWIProcessConfigService processConfigSrv;

    @Autowired
    private IShiftService shiftSrv;

    @Autowired
    private WIDataUtils dataUtils;

    @Autowired
    private Environment env;

    @Autowired
    private WIDataSendMsgService msgSrv;

    @Autowired
    private GWebSocketClient gWebSocketClient;

    private JobListModuleConfigDto moduleConfig = null;

    /**
     * 查询配置的超期接收模式expire_select_input_mode
     *
     * @return
     */
    public int getExpireSelectInputMode() {
        if (this.moduleConfig == null) {
            JoblistModuleConfig moduleConfig = SpringUtils.getBean(JoblistModuleConfig.class);
            List<JobListModuleConfigDto> modules = moduleConfig == null ? null : moduleConfig.getModules();
            this.moduleConfig = modules == null ? null : modules.stream().filter(item -> "workInstruction".equals(item.getAlias())).findFirst().orElse(null);
        }
        if (this.moduleConfig == null) {
            return 1;
        }
        String json = this.moduleConfig.getConfig();
        if (StringUtils.isEmpty(json)) {
            return 1;
        }

        try {
            JSONObject parse = JSON.parseObject(json);
            Integer expire_select_input_mode = parse.getInteger("expire_select_input_mode");
            return expire_select_input_mode == null ? 1 : expire_select_input_mode;
        } catch (Exception e){}
        return 1;
    }

    public List<WIProcess> queryProcessList(WIProcessDto dto) {
        List<WIProcess> list = entityService.queryData(WIProcess.class, Where.create(), Order.create().orderByAsc(WIProcess::getTmsort), null);
        return list;
    }

    public List<WIProcess> queryCanAddProcessList (String userId, String postId, String orgCode) {
        //查找第一步
        Where where = Where.create();
        where.eq(WIProcessStep::getStepNo, 1);
        Order order = Order.create();
        List<WIProcessStep> firstStepList = entityService.queryData(WIProcessStep.class, where, order, null);
        if (StringUtils.isEmpty(firstStepList)) {
            return null;
        }

        //补充接收对象和抄送对象信息
        processConfigSrv.setStepListAccData(firstStepList);

        Set<String> processIdSet = new HashSet<>();
        Set<String> currentUserVars = processVarSrv.getCurrentUserVars();
        SysOrg myWorkshop = orgSrv.getParentOrgByOrgType(orgCode, ISysOrgService.OrgWorkshopOrDepartment);
        String queryOrgCode = myWorkshop == null ? orgCode : myWorkshop.getOrgcode();
        for (WIProcessStep step : firstStepList) {
            int accType = step.getAccType();
            String accCode = step.getAccCode();
            if (StringUtils.isEmpty(accCode)) {
                continue;
            }
            String[] accCodeArray = accCode.split(",");
            boolean canAdd = false;
            for (String code : accCodeArray) {
                if (accType == 1 && code.equals(userId)) {
                    //人员
                    canAdd = true;
                } else if (accType == 2 && (code.equals(postId) || code.equals(orgCode+"_"+postId))) {
                    //岗位
                    canAdd = true;
                } else if (accType == 3 && code.equals(orgCode)) {
                    //机构
                    canAdd = true;
                } else if (accType == 100 && currentUserVars != null && currentUserVars.contains(code)) {
                    //自定义变量
                    canAdd = true;
                }
                if (canAdd) {
                    break;
                }
            }

            if (canAdd) {
                processIdSet.add(step.getProcessId());
            }
        }

        if (StringUtils.isNotEmpty(processIdSet)) {
            return entityService.queryData(WIProcess.class, Where.create().in(WIProcess::getId, processIdSet.toArray()), Order.create().orderByAsc(WIProcess::getTmsort), null);
        }
        return null;
    }

    /**
     * 根据id获取流程设置主表
     * @param processId
     * @return
     */
    public WIProcess getProcessSettingById (String processId) {
        return entityService.queryObjectById(WIProcess.class, processId);
    }

    /**
     * 根据id获取流程步骤
     * @param processId
     * @return
     */
    public List<WIProcessStep> getProcessStepList (String processId) {
//        Where where = Where.create();
//        where.eq(WIProcessStep::getProcessId, processId);
//        Order order = Order.create();
//        order.orderByAsc(WIProcessStep::getStepNo);
//        return entityService.queryData(WIProcessStep.class, where, order, null);

        WIProcessDto dto = new WIProcessDto();
        dto.setProcessId(processId);
        return processConfigSrv.queryProcessStepList(dto);
    }

    /**
     * 获取流程设置及步骤
     * @param processId
     * @return
     */
    public WIProcessSettingVo getProcessSettingWithStep (String processId) {
        if (StringUtils.isEmpty(processId)) {
            return null;
        }
        WIProcess process = this.getProcessSettingById(processId);
        if (process == null) {
            return null;
        }
        WIProcessSettingVo vo = ObjUtils.copyTo(process, WIProcessSettingVo.class);
        List<WIProcessStep> processStepList = this.getProcessStepList(processId);
        vo.setStepList(processStepList);
        return vo;
    }

    /**
     * 获取流程设置及步骤
     * @param processIdList
     * @return
     */
    public List<WIProcessSettingVo> getProcessListWithStep (List<String> processIdList) {
        if (StringUtils.isEmpty(processIdList)) {
            return null;
        }
        List<WIProcess> procList = entityService.queryData(WIProcess.class, Where.create().in(WIProcess::getId, processIdList.toArray()), Order.create().orderByAsc(WIProcess::getTmsort), null);
        if (StringUtils.isEmpty(procList)) {
            return null;
        }
        Map<String, List<WIProcessStep>> stepGroup = null;
        List<WIProcessStep> stepList = entityService.queryData(WIProcessStep.class, Where.create().in(WIProcessStep::getProcessId, processIdList.toArray()), Order.create().orderByAsc(WIProcessStep::getStepNo), null);
        if (StringUtils.isNotEmpty(stepList)) {
            processConfigSrv.setStepListAccData(stepList);
            stepGroup = stepList.stream().collect(Collectors.groupingBy(WIProcessStep::getProcessId));
        }

        List<WIProcessSettingVo> result = new ArrayList<>();
        for (WIProcess proc : procList) {
            WIProcessSettingVo vo = ObjUtils.copyTo(proc, WIProcessSettingVo.class);
            if (StringUtils.isNotEmpty(stepGroup) && stepGroup.containsKey(vo.getId())) {
                vo.setStepList(stepGroup.get(vo.getId()));
            }
            result.add(vo);
        }

        return result;
    }


    /**
     * 发起流程
     * @param dto
     * @return
     */
    public String startProcess (WIDataDto param) {
        if (param == null) {
            return "传入参数为空";
        }
        WIData data = entityService.queryObjectById(WIData.class, param.getId());
        if (data == null) {
            return "找不到记录";
        }
        if (data.getDataStatus() > 0) {
            return "该记录已经下达，不能重复提交";
        }
        String processId = StringUtils.isNotEmpty(data.getProcessId()) ? data.getProcessId() : param.getProcessId();
        if (StringUtils.isEmpty(processId)) {
            return "流程为空";
        }
//        String processName = param.getProcessName();
        WIProcessSettingVo processVo = this.getProcessSettingWithStep(processId);
        if (processVo == null) {
            return "找不到对应的流程";
        }
        List<WIProcessStep> stepList = processVo.getStepList();
        if (stepList == null || stepList.isEmpty()) {
            return "没有对应流程，请检查";
        }

        processConfigSrv.setStepListAccData(stepList);

        String auditUserId = param.getAuditUserId();
        String auditUserName = param.getAuditUserName();

        //流程实例主数据
        WIProcessData processData = new WIProcessData();
        processData.setId(TMUID.getUID());
        processData.setDataId(data.getId());
        processData.setProcessId(processId);
//        processData.setProcessName(processName);
        processData.setProcessStatus(0);
        processData.setTmused(1);

        SysUser user = SysUserUtil.getCurrentUser();
        String currentOrgCode = user.getOrgId();
        SysOrg myWorkshop = StringUtils.isEmpty(currentOrgCode) ? null : orgSrv.getParentOrgByOrgType(currentOrgCode, ISysOrgService.OrgWorkshopOrDepartment);
        String currentWorkshopCode = myWorkshop == null ? currentOrgCode : myWorkshop.getOrgcode();
        String currentWorkshopName = myWorkshop == null ? null : myWorkshop.getOrgname();

        List<WIProcessDataStep> insertDataStepList = new ArrayList<>();
        List<WIProcessDataStepAcc> accptOrgTempList = new ArrayList<>();
        List<WIProcessDataStepAcc> insertStepAccList = new ArrayList<>();
        List<WIDataOrgInfo> insertOrgInfoList = new ArrayList<>();

//        List<WIProcessDataStep> sendMsgAuditStep = new ArrayList<>();
//        List<WIProcessDataStep> sendMsgAcceptStep = new ArrayList<>();
        List<WIProcessDataStep> nextStepList = new ArrayList<>();

        String nowdt = DateTimeUtils.getNowDateTimeStr();

        int stepNo = 1;

        //发起人
        WIProcessStep firstStepConfig = stepList.get(0);
        WIProcessDataStep firstStep = ObjUtils.copyTo(firstStepConfig, WIProcessDataStep.class);
        firstStep.setTmused(1);
        firstStep.setId(TMUID.getUID());
        firstStep.setDataId(data.getId());
        firstStep.setProcessDataId(processData.getId());
        firstStep.setProcessId(processId);
        firstStep.setProcessStepId(firstStepConfig.getId());
        firstStep.setAccOrgCode(currentWorkshopCode);
        firstStep.setAccOrgName(currentWorkshopName);
        firstStep.setStepNo(stepNo++);
        firstStep.setStepHandle(0);
        firstStep.setHandleUserOrgCode(user.getOrgId());
        firstStep.setHandleUserId(user.getId());
        firstStep.setHandleUserName(user.getRealName());
        firstStep.setHandleResult(1);
        firstStep.setHandleTime(nowdt);
        if (firstStep.getSelectInputUser() == 1) {
            //如果第一步就选择执行人，说明是班组内部执行，记录一下当前班组代码
            firstStep.setCurrentOrgCode(currentOrgCode);
        }
        insertDataStepList.add(firstStep);
        List<WIProcessDataStepAcc> firstAccList = this.getDataStepAccListByStep(firstStep, 1, user.getId(), user.getRealName());
        if (StringUtils.isNotEmpty(firstAccList)) {
            insertStepAccList.addAll(firstAccList);
            firstStep.setAccList(firstAccList);
        }

        //内部流程，接收单位是自己车间
        if (firstStep.getInAcceptOrg() == 1) {
            param.setAcceptOrgCode(currentWorkshopCode);
            param.setAcceptOrgName(currentWorkshopName);
        }

        if (StringUtils.isNotEmpty(param.getAcceptOrgCode())) {
            String acceptOrgCode = param.getAcceptOrgCode();
            String acceptOrgName = param.getAcceptOrgName();

            String[] codeSplit = acceptOrgCode.split(",");
            String[] nameSplit = acceptOrgName.split(",");
            for (int i = 0; i < codeSplit.length; i++) {
                String eachCode = codeSplit[i];
                String eachName = nameSplit[i];

//                if (firstStep.getInAcceptOrg() == 1) {
                    WIProcessDataStepAcc accOrg = new WIProcessDataStepAcc();
                    accOrg.setId(TMUID.getUID());
                    accOrg.setDataId(data.getId());
                    accOrg.setProcessDataId(processData.getId());
                    accOrg.setProcessDataStepId("acceptOrg");
                    accOrg.setObjType(0);
                    accOrg.setAccType(3);
                    accOrg.setAccCode(eachCode);
                    accOrg.setAccText(eachName);
                    accOrg.setTmsort(i+1);
                    accptOrgTempList.add(accOrg);

//                }

                if (firstStep.getInAcceptOrg() == 1 || firstStep.getSelectAcceptOrg() == 1) {
                    //内部指令或者下达时选执行单位，需在下达时添加机构信息
                    WIDataOrgInfo orgInfo = new WIDataOrgInfo();
                    orgInfo.setId(TMUID.getUID());
                    orgInfo.setDataId(data.getId());
                    orgInfo.setAccOrgCode(eachCode);
                    orgInfo.setAccOrgName(eachName);
                    orgInfo.setCardId(param.getCardId());
                    orgInfo.setCardName(param.getCardName());
                    orgInfo.setCatalogAlias(param.getCatalogAlias());
                    orgInfo.setCardExecType(param.getCardExecType());
                    insertOrgInfoList.add(orgInfo);
                }

//                if (StringUtils.isEmpty(auditUserId)) {
                //没有审核人时，创建机构信息
//                }
            }
        }


        //审核人
        if (StringUtils.isNotEmpty(auditUserId)) {
            //有审核人时，把审核人节点放到第二步
            WIProcessDataStep auditStep = new WIProcessDataStep();
            auditStep.setId(TMUID.getUID());
            auditStep.setTmused(1);
            auditStep.setDataId(data.getId());
            auditStep.setProcessDataId(processData.getId());
            auditStep.setProcessId(processId);
//            auditStep.setHandleResult(-100);
            auditStep.setHandleResult(0);
            auditStep.setCurrentStatus(1);
            auditStep.setStepName("审核");
            auditStep.setStepDesc("审核");
            auditStep.setCurrentOrgCode(firstStep.getCurrentOrgCode());
            auditStep.setAccOrgCode(currentWorkshopCode);
            auditStep.setAccOrgName(currentWorkshopName);
            auditStep.setInAcceptOrg(firstStep.getInAcceptOrg());
            auditStep.setStepNo(stepNo++);
            auditStep.setStepHandle(1);
//            auditStep.setSelectAcceptOrg(firstStep.getSelectAcceptOrg());
            auditStep.setSelectAcceptOrg(0);
            auditStep.setSelectInputUser(firstStep.getSelectInputUser());
            auditStep.setSelectCard(firstStep.getSelectCard());
            auditStep.setInputStartId(firstStep.getSelectInputUser() == 1 ? firstStep.getId() : null);
            auditStep.setInputFromId(firstStep.getSelectInputUser() == 1 ? firstStep.getId() : null);
            insertDataStepList.add(auditStep);

            List<WIProcessDataStepAcc> auditAccList = this.getDataStepAccListByStep(auditStep, 1, auditUserId, auditUserName);
            if (StringUtils.isNotEmpty(auditAccList)) {
                auditStep.setAccList(auditAccList);
                insertStepAccList.addAll(auditAccList);
            }

            //给审核人发消息
//            sendMsgAuditStep.add(auditStep);
            nextStepList.add(auditStep);
        }


        List<String> orgCodeArray = new ArrayList<>();
        List<String> orgNameArray = new ArrayList<>();
        if (firstStep.getSelectAcceptOrg() == 1 && StringUtils.isEmpty(auditUserId)) {
            orgCodeArray = new ArrayList<>(Arrays.asList(param.getAcceptOrgCode().split(",")));
            orgNameArray = new ArrayList<>(Arrays.asList(param.getAcceptOrgName().split(",")));
        } else {
            orgCodeArray.add(currentWorkshopCode);
            orgNameArray.add(currentWorkshopName);
        }

        //其余节点
        for (int i=1; i<stepList.size(); i++) {
            WIProcessStep step = stepList.get(i);
            List<WIProcessStepAcc> stepAccList = step.getAccList();
            List<WIProcessStepAcc> stepCcList = step.getCcList();
            for (int k = 0; k < orgCodeArray.size(); k++) {
                String eachCode = orgCodeArray.get(k);
                String eachName = orgNameArray.get(k);
                WIProcessDataStep dataStep = ObjUtils.copyTo(step, WIProcessDataStep.class);
                dataStep.setTmused(1);
                dataStep.setInAcceptOrg(dataStep.getInAcceptOrg());
                dataStep.setId(TMUID.getUID());
                dataStep.setDataId(data.getId());
                dataStep.setProcessDataId(processData.getId());
                dataStep.setProcessId(processId);
                dataStep.setProcessStepId(step.getId());
                dataStep.setStepHandle(2);
                dataStep.setAccOrgCode(eachCode);
                dataStep.setAccOrgName(eachName);
                dataStep.setStepNo(stepNo);
                if (StringUtils.isEmpty(auditUserId) && i==1) {
                    //没有审核人，第二步待接收
//                    dataStep.setHandleResult(-100);
                    dataStep.setCurrentStatus(1);
                    //记录要发消息的节点
//                    sendMsgAcceptStep.add(dataStep);
                    nextStepList.add(dataStep);
                }

                //接收
                List<WIProcessDataStepAcc> accList = this.getDataStepAccListByStep(dataStep, step.getAccType(), step.getAccCode(), step.getAccText());
                if (StringUtils.isNotEmpty(accList)) {
                    dataStep.setAccList(accList);
                    insertStepAccList.addAll(accList);
                }
                insertDataStepList.add(dataStep);

                if (StringUtils.isNotEmpty(stepCcList)) {
                    //后面步骤需要存抄送人设置
                    for (WIProcessStepAcc acc : stepCcList) {
                        WIProcessDataStepAcc newAcc = new WIProcessDataStepAcc();
                        newAcc.setId(TMUID.getUID());
                        newAcc.setDataId(data.getId());
                        newAcc.setProcessDataId(dataStep.getProcessDataId());
                        newAcc.setProcessDataStepId(dataStep.getId());
                        newAcc.setObjType(2);
                        newAcc.setAccType(acc.getAccType());
                        newAcc.setAccCode(acc.getAccCode());
                        newAcc.setAccText(acc.getAccText());
                        newAcc.setTmsort(acc.getTmsort());
                        insertStepAccList.add(newAcc);
                    }
                }
            }
            stepNo++;
        }

        boolean inputflag = false;

        WIProcessDataStep insertInputStep = null;
        List<WIProcessDataStepAcc> inputStepAccList = null;
        List<WIDataOrgOpCardInfo> opCardInfoList = new ArrayList<>();
        if (firstStep.getSelectInputUser() == 1 && StringUtils.isEmpty(auditUserId) && firstStep.getInAcceptOrg() == 1 && insertOrgInfoList.size() == 1) {
            //第一步选择执行人，并且没有审核人，需要生成操作卡实例
            WIDataOrgInfo orgInfo = insertOrgInfoList.get(0);
            JSONObject inputRes = this.generateInputData(param, firstStep, orgInfo, param.getInputUserId(), param.getInputUserName());
            if (inputRes.containsKey("error")) {
                return inputRes.getString("error");
            }
            data.setDataStatus(3);

            Object inputStepAccListObj = inputRes.get("inputStepAccList");
            if (inputStepAccListObj != null) {
                inputStepAccList = (List<WIProcessDataStepAcc>) inputStepAccListObj;
                insertStepAccList.addAll(inputStepAccList);
            }
            Object insertInputStepObj = inputRes.get("insertInputStep");
            if (insertInputStepObj != null) {
                insertInputStep = (WIProcessDataStep) insertInputStepObj;
                insertDataStepList.add(insertInputStep);
                insertInputStep.setAccList(inputStepAccList);
            }
            inputflag = true;

            if (StringUtils.isNotEmpty(orgInfo.getCardId())) {
                WIDataOrgOpCardInfo opCardInfo = getOpCardInfo(orgInfo);
                opCardInfoList.add(opCardInfo);
            }

            if (insertInputStep != null) {
                nextStepList.add(insertInputStep);
            }
        }

        data.setDataStatus(StringUtils.isNotEmpty(auditUserId) ? 1 : (inputflag ? 3 : 2));
        data.setSubmitDt(nowdt);
        data.setSubmitUserId(user.getId());
        data.setSubmitUserName(user.getRealName());
        data.setSubmitOrgCode(currentWorkshopCode);
        data.setInAcceptOrg(firstStep.getInAcceptOrg());

        //接收机构数量
        if (StringUtils.isNotEmpty(param.getAcceptOrgCode())) {
            data.setAcceptOrgCount(param.getAcceptOrgCode().split(",").length);
        }

        //更新流程数据状态
        entityService.rawUpdate(WIProcessData.class, Update.create(WIProcessData::getTmused, 0), Where.create().eq(WIProcessData::getDataId, data.getId()).eq(WIProcessData::getTmused, 1));
        processData.setProcessStatus(data.getDataStatus());
        entityService.insert(processData);
        entityService.rawDeleteByWhere(WIProcessDataStep.class, Where.create().eq(WIProcessDataStep::getDataId, data.getId()));
        entityService.insertBatch(insertDataStepList, 1000);
//        Where deleteAccWhere = Where.create();
//        deleteAccWhere.eq(WIProcessDataStepAcc::getDataId, data.getId());
//        deleteAccWhere.in(WIProcessDataStepAcc::getProcessDataStepId, Arrays.asList("carbonCopy", "auditUser", "acceptOrg").toArray());
        if (StringUtils.isNotEmpty(insertStepAccList)) {
            entityService.insertBatch(insertStepAccList, 1000);
//            entityService.deleteAndInsert(WIProcessDataStepAcc.class, deleteAccWhere, insertStepAccList);
        }
        if (StringUtils.isNotEmpty(accptOrgTempList)) {
            entityService.deleteAndInsert(WIProcessDataStepAcc.class, Where.create().eq(WIProcessDataStepAcc::getDataId, data.getId()).eq(WIProcessDataStepAcc::getProcessDataStepId, "acceptOrg"), accptOrgTempList);
        }

        entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(data)), "dataStatus", "submitDt", "submitUserId", "submitUserName", "submitOrgCode", "acceptOrgCount", "inAcceptOrg");

        if (StringUtils.isNotEmpty(insertOrgInfoList)) {
            entityService.deleteAndInsert(WIDataOrgInfo.class, Where.create().eq(WIDataOrgInfo::getDataId, data.getId()), insertOrgInfoList);
        } else {
            //把缓存的操作卡信息清除
            entityService.rawDeleteByWhere(WIDataOrgInfo.class, Where.create().eq(WIDataOrgInfo::getDataId, data.getId()));
        }

        //操作卡信息
        if (StringUtils.isNotEmpty(opCardInfoList)) {
            entityService.insertBatch(opCardInfoList, 1000);
        }

//        //给审核节点发消息
//        this.sendMsgToNextStep(data, sendMsgAuditStep);
//        //给接收节点发消息
//        this.sendMsgToNextStep(data, sendMsgAcceptStep);

        String ccCode = null;
        if (firstStep.getCarbonCopy() == 1 && StringUtils.isNotEmpty(param.getCarbonCopyCode())) {
            //不需要审核时，判断是否有抄送人
//            this.sendMsgToCc(data, firstStep, firstStep.getCarbonCopyType(), firstStep.getCarbonCopyCode(), firstStep.getAccOrgCode());
            ccCode = param.getCarbonCopyCode();
        }

        msgSrv.sendTodoMsgAndCcMsg(data, firstStep, nextStepList, 1, ccCode);

        return null;
    }

    /**
     * 获取操作卡信息
     * @param orgInfo
     * @return
     */
    private WIDataOrgOpCardInfo getOpCardInfo(WIDataOrgInfo orgInfo) {
        WIDataOrgOpCardInfo opCardInfo = new WIDataOrgOpCardInfo();
        opCardInfo.setId(TMUID.getUID());
        opCardInfo.setDataId(orgInfo.getDataId());
        opCardInfo.setAccOrgCode(orgInfo.getAccOrgCode());
        opCardInfo.setAccOrgName(orgInfo.getAccOrgName());
        opCardInfo.setCardId(orgInfo.getCardId());
        opCardInfo.setCardName(orgInfo.getCardName());
        opCardInfo.setCatalogAlias(orgInfo.getCatalogAlias());
        opCardInfo.setCardExecType(orgInfo.getCardExecType());
        opCardInfo.setCardInstanceId(orgInfo.getCardInstanceId());
        opCardInfo.setFixedStatus(orgInfo.getFixedStatus());
        return opCardInfo;
    }

    /**
     * 获取待接收的指令map
     * @return
     */
    public Map<String, WIProcessDataStep> getToAcceptDataStepMap(String queryDate, Set<String> dataIdSet) {
        SysUser user = SysUserUtil.getCurrentUser();
        String currentOrgCode = user.getOrgId();
        String currentUserId = user.getId();
        String currentPostId = user.getPostId();
        SysOrg currentWorkshop = StringUtils.isEmpty(currentOrgCode) ? null : orgSrv.getParentOrgByOrgType(currentOrgCode, ISysOrgService.OrgWorkshopOrDepartment);
        String currentWorkshopCode = currentWorkshop == null ? null : currentWorkshop.getOrgcode();
        Set<String> currentUserVars = processVarSrv.getCurrentUserVars();
        Map<String, WIProcessDataStep> result = new HashMap<>();
        Where where = Where.create();
        where.eq(WIProcessDataStep::getTmused, 1);
        where.gt(WIProcessDataStep::getStepNo, 1);
        if (StringUtils.isNotEmpty(dataIdSet)) {
            where.in(WIProcessDataStep::getDataId, dataIdSet.toArray());
        }
        where.and();
        where.lb();
//        where.lb();
        where.eq(WIProcessDataStep::getHandleResult, -100);
        where.or();
        where.eq(WIProcessDataStep::getHandleUserId, currentUserId);
//        where.rb();
        where.rb();
        List<WIProcessDataStep> stepList = entityService.queryData(WIProcessDataStep.class, where, Order.create().orderByAsc(WIProcessDataStep::getStepNo), null);
        if (StringUtils.isNotEmpty(stepList)) {
            for (WIProcessDataStep dataStep : stepList) {
                String handleUserId = dataStep.getHandleUserId();
//                if (dataStep.getHandleResult() != 0 && dataStep.getHandleResult() != -100) {
                    if (currentUserId.equals(handleUserId)) {
                        //处理人是当前人
                        result.put(dataStep.getDataId(), dataStep);
                        continue;
                    }
//                }
//                int accType = dataStep.getAccType();
//                String accCode = dataStep.getAccCode();
                int accType = 0;
                String accCode = null;
                String accOrgCode = dataStep.getAccOrgCode();
                if (StringUtils.isEmpty(accCode)) {
                    continue;
                }

                if (accType == 1 && (","+accCode+",").contains(","+currentUserId+",")) {
                    //人员
                } else if (accType == 2 && ((","+accCode+",").contains(","+currentPostId+",") || (","+accCode+",").contains(","+currentOrgCode+"_"+currentPostId+","))) {
                    //岗位
                } else if (accType == 3 && (","+accCode+",").contains(","+currentOrgCode+",")) {
                    //机构
                } else if (accType == 100 && StringUtils.isNotEmpty(accOrgCode) && accOrgCode.equals(currentWorkshopCode) && StringUtils.isNotEmpty(currentUserVars)) {
                    //自定义变量
                    boolean flag = false;
                    for (String eachAcc : accCode.split(",")) {
                        if (currentUserVars.contains(eachAcc)) {
                            //符合
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        continue;
                    }
                } else {
                    continue;
                }


                result.put(dataStep.getDataId(), dataStep);
            }
        }
        return result;
    }


    public Map<Integer, Map<String, WIProcessDataStepVo>> getToAcceptDataStepMap(List<WIData> dataList, ShiftForeignVo currentUserShift, WIDataQueryDto dto) {
        Map<Integer, Map<String, WIProcessDataStepVo>> result = new HashMap<>();
//        if (StringUtils.isEmpty(dataList)) {
//            return result;
//        }
//        String currentUserShiftOrgCode = currentUserShift1 == null ? null : currentUserShift1.getOrgCode();
//        Map<String, WIData> dataMap = new HashMap<>();
//        for (WIData data : dataList) {
//            dataMap.put(data.getId(), data);
//        }
        SysUser user = SysUserUtil.getCurrentUser();
        String userOrgCode = user.getOrgId();
        String currentUserId = user.getId();
        String currentPostId = user.getPostId();
        SysOrg currentWorkshop = StringUtils.isEmpty(userOrgCode) ? null : orgSrv.getParentOrgByOrgType(userOrgCode, ISysOrgService.OrgWorkshopOrDepartment);
        String currentWorkshopCode = currentWorkshop == null ? null : currentWorkshop.getOrgcode();
        Set<String> currentUserVars = processVarSrv.getCurrentUserVars();
        Map<String, WIProcessDataStepVo> acceptMap = new HashMap<>();
        Map<String, WIProcessDataStepVo> inputMap = new HashMap<>();
        Map<String, WIProcessDataStepVo> baseMap = new HashMap<>();

        StringBuilder dataWhere = new StringBuilder();
        dataWhere.append(" data.TMUSED=1 ");
        if (StringUtils.isNotEmpty(dto.getCategory())) {
            dataWhere.append(" and data.category='").append(dto.getCategory()).append("'");
        }
        String queryDate = dto.getQueryDate();
        if (StringUtils.isNotEmpty(queryDate)) {
            dataWhere.append(" and data.START_DT<='").append(queryDate).append(" 23:59:59").append("'");
            dataWhere.append(" and ");
            dataWhere.append("(");
            dataWhere.append(" data.END_DT is null or data.END_DT='' or data.END_DT>='").append(queryDate).append(" 00:00:00").append("' or (data.END_DT<'").append(queryDate).append(" 00:00:00").append("' and data.DATA_STATUS>0 and data.DATA_STATUS<10)");
            dataWhere.append(")");
        }


        String varWhereStr = "";
        if (StringUtils.isNotEmpty(currentUserVars)) {
            varWhereStr = " or (acc.ACC_TYPE=100 and acc.ACC_ORG_CODE=? and acc.ACC_CODE in ("+StringUtils.join(new ArrayList<>(Collections.nCopies(currentUserVars.size(), "?")), ",")+"))";
        }
        String sql = "select step.ID, step.ACC_ORG_CODE, step.STEP_HANDLE, step.IN_ACCEPT_ORG, " +
            "step.DATA_ID, acc.HANDLE_USER_ID, acc.HANDLE_RESULT, acc.ID as ACC_ID, " +
            "step.SELECT_INPUT_USER, step.SELECT_ACCEPT_ORG, step.SELECT_CARD, " +
            "step.CURRENT_STATUS as STEP_STATUS, acc.CURRENT_STATUS as ACC_STATUS, " +
            "step.EXPIRE_ASSIGN_ORG_CODE, step.CURRENT_ORG_CODE "+
            ", step.CURRENT_SHIFT_DATE, step.CURRENT_SHIFT_CODE " +
            ", data.DATA_NAME, data.DATA_CONTENT, data.START_DT, data.END_DT, data.DATA_TYPE " +
            ", data.DATA_EXPIRE, data.DATA_CHANGED, data.CATEGORY, data.DATA_STATUS " +
            ", data.CREATE_BY, data.CREATE_TIME " +
            ", acc.PART_TIME_POST, acc.PART_TIME_POST_ORG_CODE, acc.PART_TIME_POST_ID " +
//            ", orgInfo.COMPLETE_STATUS "+
            " from WI_PROCESS_DATA_STEP_ACC acc join WI_PROCESS_DATA_STEP step " +
            " on acc.PROCESS_DATA_STEP_ID=step.ID " +
            " join WI_DATA data on " + dataWhere + " and step.DATA_ID=data.id " +
//            " left join WI_DATA_ORG_INFO orgInfo on step.DATA_ID=orgInfo.DATA_ID and step.ACC_ORG_CODE=orgInfo.ACC_ORG_CODE " +
//            " where acc.DATA_ID in ("+StringUtils.join(new ArrayList<>(Collections.nCopies(dataMap.size(), "?")), ",")+") and " +
            " where " +
            " ((acc.HANDLE_USER_ID=? and (acc.OBJ_TYPE=1 or acc.OBJ_TYPE=10)) or " +
            "  (acc.OBJ_TYPE=1 and acc.CURRENT_STATUS=1 and " +
            "   ((acc.ACC_TYPE=1 and acc.ACC_CODE=?) or (acc.ACC_TYPE=2 and acc.ACC_ORG_CODE=? and acc.ACC_CODE=?) or (acc.ACC_TYPE=3 and acc.ACC_CODE=?) "+varWhereStr+") " +
            "  )" +
            " ) order by step.ID";
        List<Object> values = new ArrayList<>();
//        values.addAll(dataMap.keySet());
        values.add(currentUserId);
        values.add(currentUserId);
        values.add(currentWorkshopCode);
        values.add(currentPostId);
        values.add(userOrgCode);
        if (StringUtils.isNotEmpty(currentUserVars)) {
            values.add(currentWorkshopCode);
            values.addAll(currentUserVars);
        }
        List<LinkedHashMap<String, Object>> list = entityService.query(sql, values);

//        List<WIProcessDataStepAcc> stepAccList = entityService.queryData(WIProcessDataStepAcc.class, where, Order.create().orderByAsc(WIProcessDataStepAcc::getId), null);
        if (StringUtils.isNotEmpty(list)) {
            Map<String, ShiftForeignVo> shiftMap = new HashMap<>();
            if (currentUserShift != null && StringUtils.isNotEmpty(currentUserShift.getOrgCode())) {
                shiftMap.put(currentUserShift.getOrgCode(), currentUserShift);
            }
            String nowdt = DateTimeUtils.getNowDateTimeStr();
            int expireSelectInputMode = this.getExpireSelectInputMode();
//            ShiftForeignVo currentShift = dataUtils.getCurrentUserShiftInfo();
//            String currentShiftOrgCode = currentShift == null ? null : currentShift.getOrgCode();
            for (LinkedHashMap<String, Object> map : list) {
                String stepId = (String) map.get("ID");
                String ACC_ID = (String) map.get("ACC_ID");
                String DATA_ID = (String) map.get("DATA_ID");
                Integer IN_ACCEPT_ORG = (Integer) map.get("IN_ACCEPT_ORG");
                IN_ACCEPT_ORG = IN_ACCEPT_ORG == null ? 0 : IN_ACCEPT_ORG;
                String ACC_ORG_CODE = (String) map.get("ACC_ORG_CODE");
                Integer STEP_HANDLE = (Integer) map.get("STEP_HANDLE");
                STEP_HANDLE = STEP_HANDLE == null ? 0 : STEP_HANDLE;
                String HANDLE_USER_ID = (String) map.get("HANDLE_USER_ID");
                String EXPIRE_ASSIGN_ORG_CODE = (String) map.get("EXPIRE_ASSIGN_ORG_CODE");
                String CURRENT_ORG_CODE = (String) map.get("CURRENT_ORG_CODE");
                String CURRENT_SHIFT_DATE = (String) map.get("CURRENT_SHIFT_DATE");
                String CURRENT_SHIFT_CODE = (String) map.get("CURRENT_SHIFT_CODE");
                Integer HANDLE_RESULT = (Integer) map.get("HANDLE_RESULT");
                Integer STEP_STATUS = (Integer) map.get("STEP_STATUS");
                Integer ACC_STATUS = (Integer) map.get("ACC_STATUS");
                ACC_STATUS = ACC_STATUS == null ? 0 : ACC_STATUS;
                Integer SELECT_INPUT_USER = (Integer) map.get("SELECT_INPUT_USER");
                Integer SELECT_ACCEPT_ORG = (Integer) map.get("SELECT_ACCEPT_ORG");
                Integer SELECT_CARD = (Integer) map.get("SELECT_CARD");

                String CATEGORY = (String) map.get("CATEGORY");
                String DATA_NAME = (String) map.get("DATA_NAME");
                String DATA_CONTENT = (String) map.get("DATA_CONTENT");
                String START_DT = (String) map.get("START_DT");
                String END_DT = (String) map.get("END_DT");
                String DATA_TYPE = (String) map.get("DATA_TYPE");
                Integer DATA_EXPIRE = (Integer) map.get("DATA_EXPIRE");
                Integer DATA_CHANGED = (Integer) map.get("DATA_CHANGED");
                Integer DATA_STATUS = (Integer) map.get("DATA_STATUS");
                DATA_STATUS = DATA_STATUS == null ? 0 : DATA_STATUS;
                String CREATE_BY = (String) map.get("CREATE_BY");
                Date CREATE_TIME = (Date) map.get("CREATE_TIME");
                Integer PART_TIME_POST = (Integer) map.get("PART_TIME_POST");
                PART_TIME_POST = PART_TIME_POST == null ? 0 : PART_TIME_POST;
                String PART_TIME_POST_ID = (String) map.get("PART_TIME_POST_ID");
                String PART_TIME_POST_ORG_CODE = (String) map.get("PART_TIME_POST_ORG_CODE");

//                WIData data = dataMap.get(DATA_ID);
//                if (data == null) {
//                    continue;
//                }
//                String endDt = data.getEndDt();
                String endDt = END_DT;
                if (StringUtils.isNotEmpty(endDt) && StringUtils.isNotEmpty(queryDate) && queryDate.substring(0, 10).compareTo(endDt.substring(0, 10)) > 0 && DATA_STATUS > 0) {
                    //超期未完成记录，只显示接收待办和反馈待办
                    if (DATA_STATUS < 10 || (DATA_STATUS == 10 && STEP_HANDLE == 4)) {
                        if (ACC_STATUS != 1) {
                            continue;
                        }
                    }
                }

                boolean inputStep = (STEP_HANDLE == 3 || STEP_HANDLE == 6);
                boolean selectInputUserStep = SELECT_INPUT_USER != null && SELECT_INPUT_USER == 1;

                if (StringUtils.isNotEmpty(START_DT) && nowdt.compareTo(START_DT) < 0) {
                    //没到开始时间的待办不显示
                    if (ACC_STATUS == 1 && IN_ACCEPT_ORG == 1 && (inputStep || selectInputUserStep)) {
                        //只有车间内部的值班长或者填报人不能提前处理待办
                        continue;
                    }
                }

                if (ACC_STATUS == 1 && (inputStep || selectInputUserStep)) {
                    //填报或者选择执行人的步骤时，待办需要过滤
                    ShiftForeignVo eachShiftInfo = currentUserShift;
                    if (StringUtils.isNotEmpty(PART_TIME_POST_ORG_CODE)) {
                        ShiftForeignVo shiftInfo = null;
                        if (shiftMap.containsKey(PART_TIME_POST_ORG_CODE)) {
                            shiftInfo = shiftMap.get(PART_TIME_POST_ORG_CODE);
                        } else {
                            shiftInfo = dataUtils.getOrgNowShiftInfo(PART_TIME_POST_ORG_CODE);
                            shiftMap.put(PART_TIME_POST_ORG_CODE, shiftInfo);
                        }
                        eachShiftInfo = shiftInfo;
                    }
                    String eachShiftOrgCode = eachShiftInfo == null ? null : eachShiftInfo.getOrgCode();

                    if (StringUtils.isEmpty(eachShiftOrgCode)) {
                        //不当班，收不到待办
                        continue;
                    }
                    //没超期时，只有当班的能收到待办
                    if (STEP_HANDLE == 5) {
                        //结转待接收
//                        if (StringUtils.isNotEmpty(EXPIRE_ASSIGN_ORG_CODE) && !EXPIRE_ASSIGN_ORG_CODE.equals(userOrgCode)) {
                        if (StringUtils.isNotEmpty(EXPIRE_ASSIGN_ORG_CODE) && !(","+EXPIRE_ASSIGN_ORG_CODE+",").contains(","+userOrgCode+",")) {
                            //不是指定机构，跳过
                            continue;
                        }
                    } else if (STEP_HANDLE == 6) {
                        //结转填写
//                        if (StringUtils.isEmpty(currentUserShiftOrgCode)) {
//                            //不当班，收不到待办
//                            continue;
//                        }
                    } else if (StringUtils.isEmpty(endDt) || nowdt.compareTo(endDt) <= 0) {
                        //正常选执行人接收或者填报
//                        if (StringUtils.isEmpty(currentUserShiftOrgCode)) {
//                            //不当班，收不到待办
//                            continue;
//                        }
                        if (inputStep) {
                            String tbrq = eachShiftInfo.getTbrq();
                            String shiftClassCode = eachShiftInfo.getShiftClassCode();
                            //填报人，只有同班次的才能收到待办
                            if (StringUtils.isNoneEmpty(CURRENT_SHIFT_DATE, CURRENT_SHIFT_CODE) && (!CURRENT_SHIFT_DATE.equals(tbrq) || !CURRENT_SHIFT_CODE.equals(shiftClassCode))) {
                                continue;
                            }
                        }
                    } else if (StringUtils.isNotEmpty(endDt) && nowdt.compareTo(endDt) > 0) {
                        //超期时
                        //模式1时，超期了所有当班的仍然能接收
                        //模式2时，只有指定了超期接收单位，且匹配时才能接收
                        if (expireSelectInputMode == 2) {
                            //模式2
                            //待办时，如果超时了，有指定单位，不是的跳过
                            if (StringUtils.isEmpty(EXPIRE_ASSIGN_ORG_CODE) || (StringUtils.isNoneEmpty(EXPIRE_ASSIGN_ORG_CODE, CURRENT_ORG_CODE) && !(","+EXPIRE_ASSIGN_ORG_CODE+",").contains(","+CURRENT_ORG_CODE+","))) {
                                //没指定超期接收单位
                                continue;
                            } else if (inputStep) {
                                //填写时，没有当前机构，跳过
//                                if (StringUtils.isEmpty(CURRENT_ORG_CODE) || !CURRENT_ORG_CODE.equals(EXPIRE_ASSIGN_ORG_CODE)) {
                                if (StringUtils.isEmpty(CURRENT_ORG_CODE) || !(","+EXPIRE_ASSIGN_ORG_CODE+",").contains(","+CURRENT_ORG_CODE+",")) {
                                    //没有当前机构，或者当前机构不是超期接收单位，跳过
                                    continue;
                                }
                            } else if (selectInputUserStep) {
                                //选择执行人的接收节点（值班长）
                                if (!(","+EXPIRE_ASSIGN_ORG_CODE+",").contains(","+userOrgCode+",")) {
                                    //不是超期指定单位，跳过
                                    continue;
                                }
                            }
                        } else {
                            //模式1
                            if (StringUtils.isEmpty(eachShiftOrgCode)) {
                                //不当班，收不到待办
                                continue;
                            }
                        }

                    }

                }
                WIProcessDataStepVo step = new WIProcessDataStepVo();
                step.setStepId(stepId);
                step.setInAcceptOrg(IN_ACCEPT_ORG == null ? 0 : IN_ACCEPT_ORG);
                step.setDataId(DATA_ID);
                step.setAccOrgCode(ACC_ORG_CODE);
                step.setStepHandle(STEP_HANDLE);
                step.setHandleUserId(HANDLE_USER_ID);
                step.setHandleResult(HANDLE_RESULT == null ? 0 : HANDLE_RESULT);
                step.setCurrentStatus(ACC_STATUS);
                step.setAccId(ACC_ID);
                step.setSelectInputUser(SELECT_INPUT_USER == null ? 0 : SELECT_INPUT_USER);
                step.setSelectAcceptOrg(SELECT_ACCEPT_ORG == null ? 0 : SELECT_ACCEPT_ORG);
                step.setSelectCard(SELECT_CARD == null ? 0 : SELECT_CARD);
                step.setCurrentShiftCode(CURRENT_SHIFT_CODE);
                step.setCurrentOrgCode(CURRENT_ORG_CODE);
                step.setCurrentShiftDate(CURRENT_SHIFT_DATE);
                step.setExpireAssignOrgCode(EXPIRE_ASSIGN_ORG_CODE);

                step.setDataName(DATA_NAME);
                step.setDataContent(DATA_CONTENT);
                step.setDataStatus(DATA_STATUS == null ? 0 : DATA_STATUS);
                step.setStartDt(START_DT);
                step.setEndDt(END_DT);
                step.setDataExpire(DATA_EXPIRE == null ? 0 : DATA_EXPIRE);
                step.setDataChanged(DATA_CHANGED == null ? 0 : DATA_CHANGED);
                step.setCategory(CATEGORY);
                step.setDataType(DATA_TYPE);
                step.setCreateBy(CREATE_BY);
                step.setCreateTime(CREATE_TIME);
                step.setPartTimePost(PART_TIME_POST);
                step.setPartTimePostId(PART_TIME_POST_ID);
                step.setPartTimePostOrgCode(PART_TIME_POST_ORG_CODE);

                if (step.getStepHandle() == 3 || step.getStepHandle() == 6) {
                    //填写
                    WIProcessDataStepVo dataStep = inputMap.get(step.getDataId());
                    if (dataStep == null || step.getCurrentStatus() == 1) {
                        inputMap.put(step.getDataId(), step);
                    }
                } else {
                    //接收
                    WIProcessDataStepVo dataStep = acceptMap.get(step.getDataId());
                    if (dataStep == null || step.getCurrentStatus() == 1) {
                        acceptMap.put(step.getDataId(), step);
                    }
                }

                baseMap.put(step.getDataId(), step);
            }
        }

        result.put(2, acceptMap);
        result.put(3, inputMap);
        result.put(0, baseMap);
        return result;
    }



    public JSONObject generateInputData(WIDataDto data, WIProcessDataStep currentStep, WIDataOrgInfo currentOrgInfo, String inputUserId, String inputUserName) {
        String cardInstanceId = null;
        String cardId = null;
        String catalogAlias = null;
        Integer fixedStatus = null;
        int execType = 1;
        if (currentOrgInfo != null) {
            cardInstanceId = currentOrgInfo.getCardInstanceId();
            cardId = currentOrgInfo.getCardId();
            catalogAlias = currentOrgInfo.getCatalogAlias();
            execType = currentOrgInfo.getCardExecType();
            fixedStatus = currentOrgInfo.getFixedStatus();
        }

        JSONObject result = new JSONObject();
        if (StringUtils.isNotEmpty(cardId) && StringUtils.isEmpty(cardInstanceId)) {
            //绑定了操作卡，且没启动，需要启动生成实例
            OperCardExecInitDto parm = new OperCardExecInitDto();
            parm.setCardId(cardId);
            parm.setCatalogAlias(catalogAlias);
            parm.setExecType(execType);
            parm.setWorkId(data.getId()+"__"+currentStep.getAccOrgCode());
            parm.setWorkType(1);
            parm.setWorkContent(data.getDataContent());
            OperCardInitResultVo opVo = opercardExecService.initOpercard(parm);
            boolean opResult = opVo.isResult();
            if (!opResult) {
                result.put("error", StringUtils.isNotEmpty(opVo.getErrorInfo()) ? opVo.getErrorInfo() : "操作失败");
                return result;
            }
            fixedStatus = opVo.getFixedStatus();
//            fixedStatus = 1;
            cardInstanceId = opVo.getExecId();
            if (StringUtils.isEmpty(cardInstanceId)) {
//                return "操作卡启动失败";
                result.put("error", "操作卡启动失败");
                return result;
            }
        } else {
            //没绑定
        }

        if (currentOrgInfo != null) {
            currentOrgInfo.setCardInstanceId(cardInstanceId);
            currentOrgInfo.setFixedStatus(fixedStatus);
        }

        //有操作卡但是没有执行人，自动设置跟操作卡关联岗位的人
        if (StringUtils.isNoneEmpty(cardId, catalogAlias) && StringUtils.isEmpty(inputUserId)) {
            String userOrgCode = SysUserUtil.getCurrentUser().getOrgId();
            List<EmployeeVo> empList = StringUtils.isEmpty(userOrgCode) ? null : empSrv.getEmployeeByOrgcode(userOrgCode);
            List<String> operCardPost = StringUtils.isEmpty(empList) ? null : opercardExecService.getOperCardPost(cardId, catalogAlias);
            if (StringUtils.isNotEmpty(empList) && StringUtils.isNotEmpty(operCardPost)) {
                List<String> userIdList = new ArrayList<>();
                List<String> userNameList = new ArrayList<>();
                Set<String> filterPostSet = new HashSet<>(operCardPost);
                for (EmployeeVo employeeVo : empList) {
                    String postTmuid = employeeVo.getPostTmuid();
                    if (StringUtils.isEmpty(postTmuid) && !filterPostSet.contains(postTmuid)) {
                        continue;
                    }
                    userIdList.add(employeeVo.getEmpTmuid());
                    userNameList.add(employeeVo.getEmpname());
                }
                data.setInputUserId(StringUtils.join(userIdList, ","));
                data.setInputUserName(StringUtils.join(userNameList, ","));
            }
        }

        List<WIProcessDataStep> inputStepList = null;
        if (currentStep.getStepHandle() != 1) {
            inputStepList = entityService.queryData(WIProcessDataStep.class, Where.create().eq(WIProcessDataStep::getInputFromId, StringUtils.isNotEmpty(currentStep.getInputFromId()) ? currentStep.getInputFromId() : currentStep.getId()), null, null);
        }

        boolean insertFlag = false;
        WIProcessDataStep inputStep = StringUtils.isNotEmpty(inputStepList) ? inputStepList.get(0) : null;
        if (inputStep == null) {
            insertFlag = true;
            inputStep = new WIProcessDataStep();
            inputStep.setInputStartId(StringUtils.isNotEmpty(currentStep.getInputStartId()) ? currentStep.getInputStartId() : currentStep.getId());
            inputStep.setInputFromId(StringUtils.isNotEmpty(currentStep.getInputFromId()) ? currentStep.getInputFromId() : currentStep.getId());
            inputStep.setId(TMUID.getUID());
            inputStep.setDataId(currentStep.getDataId());
            inputStep.setStepName("执行");
            inputStep.setStepDesc("执行");
            inputStep.setProcessDataId(currentStep.getProcessDataId());
            inputStep.setProcessId(currentStep.getProcessId());
            inputStep.setInAcceptOrg(currentStep.getInAcceptOrg());
            inputStep.setAccOrgCode(currentStep.getAccOrgCode());
            inputStep.setAccOrgName(currentStep.getAccOrgName());
            inputStep.setCurrentOrgCode(currentStep.getCurrentOrgCode());
            inputStep.setCurrentShiftCode(currentStep.getCurrentShiftCode());
            inputStep.setCurrentShiftDate(currentStep.getCurrentShiftDate());
//            inputStep.setHandleResult(-100);
            inputStep.setHandleResult(0);
            inputStep.setCurrentStatus(1);
            inputStep.setStepHandle(currentStep.getStepHandle()== 5 ? 6 : 3);
            inputStep.setTmused(1);
            inputStep.setStepNo(currentStep.getStepNo()+1);
        } else {
            inputStep.setCurrentOrgCode(currentStep.getCurrentOrgCode());
            inputStep.setCurrentShiftCode(currentStep.getCurrentShiftCode());
            inputStep.setCurrentShiftDate(currentStep.getCurrentShiftDate());
        }
        inputStep.setExpireAssignOrgCode(currentStep.getExpireAssignOrgCode());

        List<WIProcessDataStepAcc> accList = new ArrayList<>();
        if (StringUtils.isNotEmpty(inputUserId)) {
            String currentOrgCode = currentStep.getCurrentOrgCode();
            String[] userIdSplit = inputUserId.split(",");
            String[] userNameSplit = inputUserName.split(",");
            for (int i = 0; i < userIdSplit.length; i++) {
                String userId = userIdSplit[i];
                String userName = userNameSplit[i];
                WIProcessDataStepAcc acc = new WIProcessDataStepAcc();
                acc.setId(TMUID.getUID());
                acc.setDataId(currentStep.getDataId());
                acc.setProcessDataId(currentStep.getProcessDataId());
                acc.setProcessDataStepId(inputStep.getId());
                acc.setObjType(1);
                acc.setAccOrgCode(currentStep.getAccOrgCode());
                acc.setAccOrgName(currentStep.getAccOrgName());
                acc.setAccType(1);
                acc.setAccCode(userId);
                acc.setAccText(userName);
                acc.setTmsort(i+1);
//                acc.setHandleResult(-100);
                acc.setCurrentStatus(1);

                EmployeeVo emp = empBasicSrv.getEmployeeFromReids(userId);
                if (emp.getStaffType() != null && emp.getStaffType() == 4) {
                    //外委人员
                    List<EmployeeVo> userPartTimePost = orgInfoSrv.getUserPartTimePost(userId);
                    if (StringUtils.isNotEmpty(userPartTimePost)) {
                        EmployeeVo partTimePost = userPartTimePost.stream().filter(item -> item.getOrgcode().equals(currentOrgCode)).findFirst().orElse(null);
                        if (partTimePost != null) {
                            acc.setPartTimePost(1);
                            acc.setPartTimePostOrgCode(partTimePost.getOrgcode());
                            acc.setPartTimePostId(partTimePost.getPostid());
                        }
                    }
                }

                accList.add(acc);
            }
        }
//        entityService.insert(newNextStep);
        result.put("cardInstanceId", cardInstanceId);
        if (insertFlag) {
            result.put("insertInputStep", inputStep);
        } else {
            result.put("updateInputStep", inputStep);
        }
        result.put("inputStepId", inputStep.getId());
        result.put("inputStepAccList", accList);
        return result;
    }

    public String checkAndUpdateDataStatusReject(WIData dto) {
        if (dto == null) {
            return null;
        }

        List<WIDataOrgInfo> orgInfoList = dataUtils.getOrgInfoList(dto.getId());
//        String acceptOrgCode = data == null ? null : data.getAcceptOrgCode();
        if (StringUtils.isEmpty(orgInfoList)) {
            return null;
        }
        List<WIProcessDataStep> stepList = entityService.queryData(WIProcessDataStep.class, Where.create().eq(WIProcessDataStep::getDataId, dto.getId()).eq(WIProcessDataStep::getInAcceptOrg, 1).eq(WIProcessDataStep::getHandleResult, -1), null, null);
        if (StringUtils.isEmpty(stepList)) {
            return null;
        }
        Set<String> orgCodeSet = stepList.stream().map(WIProcessDataStep::getAccOrgCode).collect(Collectors.toSet());
        boolean flag = true;
        for (WIDataOrgInfo eachInfo : orgInfoList) {
            if (!orgCodeSet.contains(eachInfo.getAccOrgCode())) {
                //完成机构中没有，说明尚未全部完成
                flag = false;
                break;
            }
        }

        if (flag) {
            WIData data = entityService.queryObjectById(WIData.class, dto.getId());
            data.setDataStatus(-2);
            entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(data)), "DATA_STATUS");

            //更新流程数据状态
            Where proUpdateWhere = Where.create();
            proUpdateWhere.eq(WIProcessData::getDataId, data.getId());
            proUpdateWhere.eq(WIProcessData::getTmused, 1);
            Update proUpdate = Update.create(WIProcessData::getProcessStatus, -2);
            entityService.rawUpdate(WIProcessData.class, proUpdate, proUpdateWhere);
        }
        return "";
    }

    /**
     * 修改填写人
     * @param param
     * @param currentStep
     * @param currentAcc
     * @return
     */
    private String selectInputUser(WIDataDto param, WIProcessDataStep currentStep, WIProcessDataStepAcc currentAcc, List<WIProcessDataStepAcc> currentStepAllAcc) {

        List<WIProcessDataStep> insertStepList = new ArrayList<>();
        List<WIProcessDataStep> updateStepList = new ArrayList<>();
        List<WIProcessDataStepAcc> insertInputAccList = new ArrayList<>();
        String nowdt = DateTimeUtils.getNowDateTimeStr();
        String currentUserId = SysUserUtil.getCurrentUser().getId();
        String currentUserName = SysUserUtil.getCurrentUser().getRealName();
        String currentUserOrgCode = SysUserUtil.getCurrentUser().getOrgId();

        WIData data = entityService.queryObjectById(WIData.class, param.getId());

        //记录当前步骤

        if (currentStep.getStepHandle() != 1) {
            //不是审核步骤时，一般为值班长，记录当前班次
            ShiftForeignVo currentUserShiftInfo = dataUtils.getCurrentUserShiftInfo();
            if (currentUserShiftInfo != null) {
                currentStep.setCurrentOrgCode(currentUserShiftInfo.getOrgCode());
                currentStep.setCurrentShiftDate(currentUserShiftInfo.getTbrq());
                currentStep.setCurrentShiftCode(currentUserShiftInfo.getShiftClassCode());
            }
        }
        //如果是审核步骤，则该步直接待办通过，如果是普通选择执行人步骤，该步仍是待办状态，可修改
        currentStep.setCurrentStatus(currentStep.getStepHandle() == 1 ? 0 : 1);
        currentStep.setHandleResult(1);
        currentStep.setHandleTime(nowdt);
        currentStep.setHandleUserId(currentUserId);
        currentStep.setHandleUserName(currentUserName);
        currentStep.setHandleUserOrgCode(currentUserOrgCode);

        //记录当前接收
        boolean alreadyTodo = StringUtils.isEmpty(currentUserId);
//        int maxsort = 0;
        List<WIProcessDataStepAcc> updateCurrentStepAccList = new ArrayList<>();
        for (WIProcessDataStepAcc acc : currentStepAllAcc) {
            if (currentStep.getStepHandle() == 1) {
                //审核时清除待办
                acc.setCurrentStatus(0);
            }
            if (acc.getId().equals(currentAcc.getId())) {
                //当前节点
                acc.setHandleResult(1);
                acc.setHandleUserId(currentUserId);
                acc.setHandleUserName(currentUserName);
                acc.setHandleTime(nowdt);
            }
            if (acc.getObjType() == 1) {
                //只修改接收
                updateCurrentStepAccList.add(acc);
//                maxsort = Math.max(acc.getTmsort(), maxsort);
            }
            if (!alreadyTodo && acc.getObjType() == 10 && currentUserId.equals(acc.getHandleUserId())) {
                //已办
                alreadyTodo = true;
            }
        }
        if (!alreadyTodo) {
            WIProcessDataStepAcc alreadyAcc = new WIProcessDataStepAcc();
            alreadyAcc.setId(TMUID.getUID());
            alreadyAcc.setDataId(currentAcc.getDataId());
            alreadyAcc.setProcessDataId(currentAcc.getProcessDataId());
            alreadyAcc.setProcessDataStepId(currentAcc.getProcessDataStepId());
            alreadyAcc.setAccOrgCode(currentAcc.getAccOrgCode());
            alreadyAcc.setAccOrgName(currentAcc.getAccOrgName());
            alreadyAcc.setTmsort(-1);
            alreadyAcc.setObjType(10);
            alreadyAcc.setAccText("");
            alreadyAcc.setAccCode("already");
            alreadyAcc.setAccType(0);
            alreadyAcc.setHandleResult(1);
            alreadyAcc.setHandleUserId(currentUserId);
            alreadyAcc.setHandleUserName(currentUserName);
            alreadyAcc.setHandleTime(nowdt);
            insertInputAccList.add(alreadyAcc);
        }

        List<WIDataOrgInfo> orgInfoList = entityService.queryData(WIDataOrgInfo.class, Where.create().eq(WIDataOrgInfo::getDataId, param.getId()).eq(WIDataOrgInfo::getAccOrgCode, currentStep.getAccOrgCode()), null, null);
        WIDataOrgInfo currentOrgInfo = StringUtils.isEmpty(orgInfoList) ? null : orgInfoList.get(0);


        if (currentOrgInfo != null) {
            //赋值操作卡
            if (currentStep.getSelectCard() == 1) {
                currentOrgInfo.setCardId(param.getCardId());
                currentOrgInfo.setCatalogAlias(param.getCatalogAlias());
                currentOrgInfo.setCardName(param.getCardName());
                currentOrgInfo.setCardExecType(param.getCardExecType());
            }
            if (StringUtils.isEmpty(currentOrgInfo.getAccUserId())) {
                currentOrgInfo.setAccUserId(currentUserId);
                currentOrgInfo.setAccUserName(currentUserName);
                currentOrgInfo.setAccDt(nowdt);
            }
        }

        JSONObject inputRes = this.generateInputData(param, currentStep, currentOrgInfo, param.getInputUserId(), param.getInputUserName());
        if (inputRes.containsKey("error")) {
            return inputRes.getString("error");
        }
        Object inputStepAccListObj = inputRes.get("inputStepAccList");
        if (inputStepAccListObj != null) {
            List<WIProcessDataStepAcc> inputStepAccList = (List<WIProcessDataStepAcc>) inputStepAccListObj;
            insertInputAccList.addAll(inputStepAccList);
        }
        Object insertInputStepObj = inputRes.get("insertInputStep");
        if (insertInputStepObj != null) {
            WIProcessDataStep insertInputStep = (WIProcessDataStep) insertInputStepObj;
            insertStepList.add(insertInputStep);
        }
        Object updateInputStepObj = inputRes.get("updateInputStep");
        if (updateInputStepObj != null) {
            WIProcessDataStep updateInputStep = (WIProcessDataStep) updateInputStepObj;
            updateStepList.add(updateInputStep);
        }
        String inputStepId = (String) inputRes.get("inputStepId");

        Update update = Update.create(WIData::getDataStatus, 3);
        Where where = Where.create();
        where.eq(WIData::getId, param.getId());
        entityService.rawUpdate(WIData.class, update, where);

        updateStepList.add(currentStep);
        entityService.updateByIdBatch(updateStepList, "currentStatus", "currentOrgCode", "currentShiftDate", "currentShiftCode", "handleResult", "handleTime", "handleUserId", "handleUserName", "expireAssignOrgCode", "handleUserOrgCode");
        if (StringUtils.isNotEmpty(updateCurrentStepAccList)) {
            entityService.updateByIdBatch(updateCurrentStepAccList, "currentStatus", "handleResult", "handleTime", "handleUserId", "handleUserName", "handleUserOrgCode");
        }
        if (StringUtils.isNotEmpty(insertStepList)) {
            entityService.insertBatch(insertStepList, 1000);
        }
        Where deleteWhere = Where.create().eq(WIProcessDataStepAcc::getProcessDataStepId, inputStepId).eq(WIProcessDataStepAcc::getObjType, 1);
        if (StringUtils.isEmpty(insertInputAccList)) {
            //没有填写人，只清空
            entityService.rawDeleteByWhere(WIProcessDataStepAcc.class, deleteWhere);
        } else {
            //有填写人，先清除再插入
            entityService.deleteAndInsert(WIProcessDataStepAcc.class, deleteWhere, insertInputAccList);
        }

        //更新操作卡信息
        if (currentOrgInfo != null) {
            entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentOrgInfo)), "cardId", "catalogAlias", "cardName", "cardExecType", "cardInstanceId", "fixedStatus", "accUserId", "accUserName", "accDt");

            Where opWhere = Where.create().eq(WIDataOrgOpCardInfo::getDataId, currentOrgInfo.getDataId()).eq(WIDataOrgOpCardInfo::getAccOrgCode, currentOrgInfo.getAccOrgCode());
            if (StringUtils.isNotEmpty(currentOrgInfo.getCardId())) {
                WIDataOrgOpCardInfo opCardInfo = this.getOpCardInfo(currentOrgInfo);
                entityService.deleteAndInsert(WIDataOrgOpCardInfo.class, opWhere, new ArrayList<>(Collections.singletonList(opCardInfo)));
            } else {
                entityService.rawDeleteByWhere(WIDataOrgOpCardInfo.class, opWhere);
            }
        }

        //发消息
        WIProcessDataStep inputStep = entityService.queryObjectById(WIProcessDataStep.class, inputStepId);
        List<WIProcessDataStepAcc> accList = entityService.queryData(WIProcessDataStepAcc.class, Where.create().eq(WIProcessDataStepAcc::getDataId, inputStep.getDataId()).eq(WIProcessDataStepAcc::getProcessDataStepId, inputStep.getId()), Order.create().orderByAsc(WIProcessDataStepAcc::getTmsort), null);
        inputStep.setAccList(accList);
        String carbonCopyCode = param.getCarbonCopyCode();
        msgSrv.sendTodoMsgAndCcMsg(data, currentStep, new ArrayList<>(Collections.singletonList(inputStep)), 1, carbonCopyCode);

        //记录接收日志
        WIProcessDataStepLog log = new WIProcessDataStepLog();
        log.setId(TMUID.getUID());
        log.setProcessDataId(currentStep.getProcessDataId());
        log.setProcessDataStepId(currentStep.getId());
        log.setHandleResult(1);
        log.setHandleUserId(currentUserId);
        log.setHandleUserName(currentUserName);
        log.setHandleTime(DateTimeUtils.getNowDateTimeStr());

        if (StringUtils.isNotEmpty(param.getInputUserId())) {
            List<WIProcessDataStepLogAcc> logAccList = new ArrayList<>();
            String[] idSplit = param.getInputUserId().split(",");
            String[] nameSplit = param.getInputUserName().split(",");
            for (int i = 0; i < idSplit.length; i++) {
                WIProcessDataStepLogAcc logAcc = new WIProcessDataStepLogAcc();
                logAcc.setId(TMUID.getUID());
                logAcc.setProcessDataId(log.getProcessDataId());
                logAcc.setProcessDataStepId(log.getProcessDataStepId());
                logAcc.setProcessDataStepLogId(log.getId());
                logAcc.setObjType(1);
                logAcc.setObjCode(idSplit[i]);
                logAcc.setObjText(nameSplit[i]);
                logAcc.setTmsort(i+1);
            }
            entityService.insertBatch(logAccList, 1000);
        }
        entityService.insert(log);

        return null;
    }


    private String handleReject(WIDataDto param, WIProcessDataStep currentStep, WIProcessDataStepAcc currentAcc, List<WIProcessDataStepAcc> currentStepAllAcc) {
        if (param == null) {
            return "未找到当前记录";
        }
        if (currentStep == null || currentAcc == null) {
            return "未找到当前节点";
        }
        if (currentAcc.getCurrentStatus() != 1) {
            return "当前节点不是待处理节点";
        }
        String nowdt = DateTimeUtils.getNowDateTimeStr();
        String currentUserId = SysUserUtil.getCurrentUser().getId();
        String currentUserName = SysUserUtil.getCurrentUser().getRealName();
        WIData data = entityService.queryObjectById(WIData.class, param.getId());
        //拒收或否决
        if (StringUtils.isNotEmpty(currentStepAllAcc)) {
            for (WIProcessDataStepAcc acc : currentStepAllAcc) {
                acc.setCurrentStatus(0);
                if (acc.getId().equals(currentAcc.getId())) {
                    //当前节点
                    acc.setHandleResult(-1);
                    acc.setHandleTime(nowdt);
                    acc.setHandleUserId(currentUserId);
                    acc.setHandleUserName(currentUserName);
                    acc.setHandleMessage(param.getHandleMessage());
                } else {
                    //其余同步骤节点,标记无效即可
                    acc.setHandleResult(0);
                }
            }
        }
        currentStep.setHandleResult(-1);
        currentStep.setHandleTime(nowdt);
        currentStep.setHandleUserId(currentUserId);
        currentStep.setHandleUserName(currentUserName);
        currentStep.setHandleMessage(param.getHandleMessage());
        entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentStep)), "handleResult", "handleTime", "currentStatus", "handleUserId", "handleUserName", "handleMessage");
        if (StringUtils.isNotEmpty(currentStepAllAcc)) {
            entityService.updateByIdBatch(currentStepAllAcc, "handleResult", "handleTime", "handleUserId", "handleUserName", "handleMessage", "currentStatus");
        }

        //修改主记录状态
        if (currentStep.getInAcceptOrg() == 0 || currentStep.getStepHandle() == 1) {
            //上级机构节点或者是审核节点，否决时整个记录都为否决状态
            data.setDataStatus(currentStep.getStepHandle()==1?-1:-2);
            entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(data)), "dataStatus");
            //更新流程数据状态
            Where proUpdateWhere = Where.create();
            proUpdateWhere.eq(WIProcessData::getDataId, data.getId());
            proUpdateWhere.eq(WIProcessData::getTmused, 1);
            Update proUpdate = Update.create(WIProcessData::getProcessStatus, currentStep.getStepHandle()==1?-1:-2);
//                proUpdate.update(WIProcessData::getTmused, 0);
            entityService.rawUpdate(WIProcessData.class, proUpdate, proUpdateWhere);
        } else {
            //其余节点否决，需判断是否全部机构都为拒收，是则修改整个记录状态
            this.checkAndUpdateDataStatusReject(data);
        }

        //TODO 发消息给前面接收人
        this.getHandleUserIdSetByOrgCode(data, currentStep.getAccOrgCode());
        return null;
    }

    /**
     * 处理流程节点
     * @param param
     * @return
     */
    public String handleStep(WIDataDto param) {
        String stepParams = Optional.ofNullable(param).map(WIDataDto::getDataStepId).orElse(null);
        if (StringUtils.isEmpty(stepParams)) {
            return "传入参数有误";
        }
        String currentStepId = null;
        String currentStepAccId = null;

        WIProcessDataStepAcc currentAcc = null;
        List<WIProcessDataStepAcc> currentStepAllAcc = null;
        if (StringUtils.isNotEmpty(stepParams)) {
            JSONObject jsonObject = JSON.parseObject(stepParams);
            currentStepId = jsonObject.containsKey("acceptStepId") ? jsonObject.getString("acceptStepId") : "";
            currentStepAccId = jsonObject.containsKey("acceptStepAccId") ? jsonObject.getString("acceptStepAccId") : "";
            currentStepAllAcc = StringUtils.isEmpty(currentStepId) ? null : entityService.queryData(WIProcessDataStepAcc.class, Where.create().eq(WIProcessDataStepAcc::getProcessDataStepId, currentStepId), null, null);
            String filterAccId = currentStepAccId;
            currentAcc = StringUtils.isEmpty(filterAccId) || StringUtils.isEmpty(currentStepAllAcc) ? null
                    : currentStepAllAcc.stream().filter(acc -> acc.getId().equals(filterAccId)).findFirst().orElse(null);
        }
        if (StringUtils.isEmpty(currentStepId)) {
            return "传入参数有误";
        }

        List<WIProcessDataStep> allStepList = entityService.queryData(WIProcessDataStep.class, Where.create().eq(WIProcessDataStep::getDataId, param.getId()).eq(WIProcessDataStep::getTmused, 1), Order.create().orderByAsc(WIProcessDataStep::getStepNo), null);
        String filterStepId = currentStepId;
        WIProcessDataStep currentStep = StringUtils.isEmpty(allStepList) ? null : allStepList.stream().filter(step -> step.getId().equals(filterStepId)).findFirst().orElse(null);
        if (currentStep == null || currentAcc == null) {
            return "未找到当前节点";
        }
//        if (currentAcc.getHandleResult() != -100) {
        if (currentAcc.getCurrentStatus() != 1) {
            return "当前节点不是待处理节点";
        }

        if (param.getHandleResult() == -1) {
            //拒收或否决
            return this.handleReject(param, currentStep, currentAcc, currentStepAllAcc);
        } else if (currentStep.getSelectInputUser() == 1) {
            //选择执行人，已经处理的，走修改逻辑
            return this.selectInputUser(param, currentStep, currentAcc, currentStepAllAcc);
        }

        if (currentStep.getSelectAcceptOrg() == 1 && StringUtils.isEmpty(param.getAcceptOrgCode())) {
            //校验执行单位必填
            return "请选择执行单位";
        }

        String handleMessage = param.getHandleMessage();
        if (StringUtils.isNotEmpty(handleMessage) && handleMessage.length() > 500) {
            handleMessage = handleMessage.substring(0, 500);
        }
        WIData data = entityService.queryObjectById(WIData.class, param.getId());
        String nowdt = DateTimeUtils.getNowDateTimeStr();
        String currentUserId = SysUserUtil.getCurrentUser().getId();
        String currentUserName = SysUserUtil.getCurrentUser().getRealName();
        String currentUserOrgCode = SysUserUtil.getCurrentUser().getOrgId();

        List<WIDataOrgInfo> insertOrgInfoList = new ArrayList<>();
        List<WIDataOrgInfo> updateOrgInfoList = new ArrayList<>();

        int currentStepNo = currentStep.getStepNo();

        currentStep.setCurrentStatus(0);
        currentStep.setHandleResult(1);
        currentStep.setHandleTime(nowdt);
        currentStep.setHandleUserId(currentUserId);
        currentStep.setHandleUserName(currentUserName);
        currentStep.setHandleUserOrgCode(currentUserOrgCode);
//        currentStep.setHandleMessage(handleMessage);

        List<WIProcessDataStepAcc> insertAccList = new ArrayList<>();
        List<WIProcessDataStepAcc> insertInputAccList = new ArrayList<>();
        List<WIProcessDataStepAcc> deleteAccList = new ArrayList<>();
        List<WIProcessDataStepAcc> updateAccList = new ArrayList<>(currentStepAllAcc);

        Set<String> dataUpdateCols = new HashSet<>();
        int currentInAcceptOrg = currentStep.getInAcceptOrg();

        if (currentStep.getSelectAcceptOrg() == 1 && StringUtils.isNotEmpty(param.getAcceptOrgCode())) {
            //选择执行单位，创建各单位的记录
            List<WIProcessDataStepAcc> accOrgAccList = new ArrayList<>();

            String[] orgCodeSplit = param.getAcceptOrgCode().split(",");
            String[] orgNameSplit = param.getAcceptOrgName().split(",");
            for (int i = 0; i < orgCodeSplit.length; i++) {
                String eachOrgCode = orgCodeSplit[i];
                String eachOrgName = orgNameSplit[i];

                WIDataOrgInfo orgInfo = new WIDataOrgInfo();
                orgInfo.setId(TMUID.getUID());
                orgInfo.setDataId(data.getId());
                orgInfo.setAccOrgCode(eachOrgCode);
                orgInfo.setAccOrgName(eachOrgName);
                insertOrgInfoList.add(orgInfo);

                WIProcessDataStepAcc accOrg = new WIProcessDataStepAcc();
                accOrg.setId(TMUID.getUID());
                accOrg.setDataId(param.getId());
                accOrg.setProcessDataStepId("acceptOrg");
                accOrg.setObjType(1);
                accOrg.setAccType(3);
                accOrg.setAccCode(eachOrgCode);
                accOrg.setAccText(eachOrgName);
                accOrg.setTmsort(i+1);
                accOrgAccList.add(accOrg);
            }

            entityService.deleteAndInsert(WIProcessDataStepAcc.class, Where.create().eq(WIProcessDataStepAcc::getDataId, param.getId()).eq(WIProcessDataStepAcc::getProcessDataStepId, "acceptOrg"), accOrgAccList);

            int count = StringUtils.isNotEmpty(param.getAcceptOrgCode()) ? param.getAcceptOrgCode().split(",").length : 0;
            data.setAcceptOrgCount(count);
            dataUpdateCols.add("acceptOrgCount");
        }

        List<WIDataOrgInfo> orgInfoList = entityService.queryData(WIDataOrgInfo.class, Where.create().eq(WIDataOrgInfo::getDataId, data.getId()).eq(WIDataOrgInfo::getAccOrgCode, currentStep.getAccOrgCode()), null, null);
        WIDataOrgInfo currentOrgInfo = StringUtils.isEmpty(orgInfoList) ? null : orgInfoList.get(0);

        //赋值操作卡
        if (currentStep.getSelectCard() == 1) {
            if (currentOrgInfo != null) {
                //修改
                updateOrgInfoList.add(currentOrgInfo);
            } else {
                //新增
                currentOrgInfo = insertOrgInfoList.stream().filter(item -> item.getAccOrgCode().equals(currentStep.getAccOrgCode())).findFirst().orElse(null);
            }

            if (currentOrgInfo != null) {
                currentOrgInfo.setCardId(param.getCardId());
                currentOrgInfo.setCatalogAlias(param.getCatalogAlias());
                currentOrgInfo.setCardName(param.getCardName());
                currentOrgInfo.setCardExecType(param.getCardExecType());
            }
        }

        WIProcessDataStep nextStep = null;
        if (currentStep.getInAcceptOrg() == 0) {
            //不是分支节点，直接找下一个节点
            nextStep = allStepList.stream().filter(step -> step.getStepNo() == (currentStepNo + 1)).findFirst().orElse(null);
        } else {
            //是分支节点，找相同分支的下一步节点
            nextStep = allStepList.stream().filter(step -> step.getStepNo() == (currentStepNo + 1) && currentStep.getAccOrgCode().equals(step.getAccOrgCode())).findFirst().orElse(null);
        }

        List<WIProcessDataStep> nextStepList = new ArrayList<>();

        List<WIProcessDataStep> insertStepList = new ArrayList<>();
        List<WIProcessDataStep> updateStepList = new ArrayList<>();
        List<WIProcessDataStep> updateEffectiveStepList = new ArrayList<>();

        for (WIProcessDataStepAcc acc : currentStepAllAcc) {
            acc.setCurrentStatus(0);
            if (currentAcc.getId().equals(acc.getId())) {
                //当前节点
                acc.setHandleResult(1);
                acc.setHandleTime(nowdt);
                acc.setHandleUserId(currentUserId);
                acc.setHandleUserName(currentUserName);
                acc.setHandleMessage(handleMessage);
                acc.setHandleUserOrgCode(currentUserOrgCode);
            }
        }

        if (nextStep != null){
            //往下进行的节点

            if (currentStep.getStepHandle() == 1 && nextStep.getStepHandle() == 2) {
                //审核节点通过后，下一步为接收，状态需变为待接收
                dataUpdateCols.add("dataStatus");
                data.setDataStatus(2);
            }

            //下一节点是否是子流程
//            int nextInAccepetOrg = nextStep.getInAcceptOrg();
            String acceptOrgCode = param.getAcceptOrgCode();
            if (currentStep.getSelectAcceptOrg() == 1) {
                //选择执行单位节点，说明当前为分叉节点，后续节点创建分支流程
                String[] split = acceptOrgCode.split(",");
                Set<String> orgSet = new HashSet<>(Arrays.asList(split));

                //查出后面步骤的模板
                List<String> nextStepIdList = new ArrayList<>();
                List<WIProcessDataStep> nextStepTplList = new ArrayList<>();
                for (WIProcessDataStep step : allStepList) {
                    if (step.getStepNo() > currentStepNo) {
                        nextStepIdList.add(step.getId());
                        nextStepTplList.add(step);
                    }
                }

                Map<String, List<WIProcessDataStepAcc>> accGroup = new HashMap<>();
                Map<String, List<WIProcessDataStepAcc>> ccGroup = new HashMap<>();
                List<WIProcessDataStepAcc> nextAccList = entityService.queryData(WIProcessDataStepAcc.class, Where.create().eq(WIProcessDataStepAcc::getDataId, data.getId()).in(WIProcessDataStepAcc::getProcessDataStepId, nextStepIdList.toArray()), Order.create().orderByAsc(WIProcessDataStepAcc::getTmsort), null);
                if (StringUtils.isNotEmpty(nextAccList)) {
                    for (WIProcessDataStepAcc acc : nextAccList) {
                        int objType = acc.getObjType();
                        if (objType == 1) {
                            accGroup.computeIfAbsent(acc.getProcessDataStepId(), k -> new ArrayList<>()).add(acc);
                        } else if (objType == 2) {
                            ccGroup.computeIfAbsent(acc.getProcessDataStepId(), k -> new ArrayList<>()).add(acc);
                        }
                    }
                }

                //遍历步骤模板
                for (int i=0; i<nextStepTplList.size(); i++) {
                    WIProcessDataStep step = nextStepTplList.get(i);
                    step.setTmused(0);
                    updateEffectiveStepList.add(step);
                    //后面的步骤都要创建分支
                    for (String eachOrg : orgSet) {
                        WIProcessDataStep newStep = ObjUtils.copyTo(step, WIProcessDataStep.class);
                        newStep.setId(TMUID.getUID());
                        newStep.setTmused(1);
                        newStep.setAccOrgCode(eachOrg);
                        SysOrg org = orgSrv.findOrgById(eachOrg);
                        if (org != null) {
                            newStep.setAccOrgName(org.getOrgname());
                        }
                        if (i == 0) {
                            //下一点变成待处理
//                            newStep.setHandleResult(-100);
                            newStep.setCurrentStatus(1);
                            nextStepList.add(newStep);
                        }

                        List<WIProcessDataStepAcc> accList = accGroup.get(step.getId());
                        List<WIProcessDataStepAcc> ccList = ccGroup.get(step.getId());
                        if (StringUtils.isNotEmpty(accList)) {
                            //更换id重新保存接收人
                            List<WIProcessDataStepAcc> newAccList = new ArrayList<>();
                            for (WIProcessDataStepAcc acc : accList) {
                                WIProcessDataStepAcc newAcc = ObjUtils.copyTo(acc, WIProcessDataStepAcc.class);
                                newAcc.setId(TMUID.getUID());
                                newAcc.setAccOrgCode(newStep.getAccOrgCode());
                                newAcc.setAccOrgName(newStep.getAccOrgName());
                                newAcc.setProcessDataStepId(newStep.getId());
                                newAcc.setHandleResult(newStep.getHandleResult());
                                newAcc.setCurrentStatus(newStep.getCurrentStatus());
                                newAccList.add(newAcc);

                                deleteAccList.add(acc);
                                insertAccList.add(newAcc);
                            }
                            newStep.setAccList(newAccList);
                        }

                        if (StringUtils.isNotEmpty(ccList)) {
                            //更换id重新保存抄送人
                            List<WIProcessDataStepAcc> newCcList = new ArrayList<>();
                            for (WIProcessDataStepAcc acc : ccList) {
                                WIProcessDataStepAcc newAcc = ObjUtils.copyTo(acc, WIProcessDataStepAcc.class);
                                newAcc.setId(TMUID.getUID());
                                newAcc.setAccOrgCode(newStep.getAccOrgCode());
                                newAcc.setAccOrgName(newStep.getAccOrgName());
                                newAcc.setProcessDataStepId(newStep.getId());
//                                newAcc.setHandleResult(newStep.getHandleResult());
                                newCcList.add(newAcc);

                                deleteAccList.add(acc);
                                insertAccList.add(newAcc);
                            }
                            newStep.setCcList(newCcList);
                        }
                        insertStepList.add(newStep);
                    }
                }
            } else {
                //普通接收节点，下一节点变成待办
                nextStep.setAccOrgCode(currentStep.getAccOrgCode());
                nextStep.setAccOrgName(currentStep.getAccOrgName());
                nextStep.setCurrentStatus(1);
                updateStepList.add(nextStep);
                nextStepList.add(nextStep);

                List<WIProcessDataStepAcc> nextAccList = entityService.queryData(WIProcessDataStepAcc.class, Where.create().eq(WIProcessDataStepAcc::getDataId, data.getId()).eq(WIProcessDataStepAcc::getProcessDataStepId, nextStep.getId()), Order.create().orderByAsc(WIProcessDataStepAcc::getTmsort), null);
                if (StringUtils.isNotEmpty(nextAccList)) {
                    nextStep.setAccList(nextAccList);
                    for (WIProcessDataStepAcc acc : nextAccList) {
                        int objType = acc.getObjType();
                        if (objType == 1) {
                            //接收
                            acc.setCurrentStatus(1);
                            updateAccList.add(acc);
                        }
                    }
                }
            }

            //        TransactionTemplate template = new TransactionTemplate(transactionManager);
            //        return template.execute(status -> {
            //            try {
            //                // 原有业务逻辑代码
            //                entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentStep)), updateCols.toArray(new String[0]));
            //                if (StringUtils.isNotEmpty(updateEffectiveStepList)) {
            //                    entityService.updateByIdBatch(updateEffectiveStepList, "effective");
            //                }
            //                if (StringUtils.isNotEmpty(updateStepList)) {
            //                    entityService.updateByIdBatch(updateStepList, "handleResult");
            //                }
            //                if (StringUtils.isNotEmpty(insertStepList)) {
            //                    entityService.insertBatch(insertStepList, 1000);
            //                }
            //                return null;
            //            } catch (Exception e) {
            //                status.setRollbackOnly();
            //                return "流程数据处理失败";
            //            }
            //        });

        }

        if (StringUtils.isNotEmpty(dataUpdateCols)) {
            entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(data)), dataUpdateCols.toArray(new String[0]));
            if (dataUpdateCols.contains("dataStatus")) {
                //更新流程数据状态
                Where proUpdateWhere = Where.create();
                proUpdateWhere.eq(WIProcessData::getDataId, data.getId());
                proUpdateWhere.eq(WIProcessData::getTmused, 1);
                Update proUpdate = Update.create(WIProcessData::getProcessStatus, data.getDataStatus());
                entityService.rawUpdate(WIProcessData.class, proUpdate, proUpdateWhere);
            }
        }
        entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentStep)), "handleResult", "handleTime", "handleUserId", "handleUserName", "handleMessage", "currentStatus", "currentOrgCode", "currentShiftCode", "currentShiftDate");
        if (StringUtils.isNotEmpty(updateEffectiveStepList)) {
            entityService.updateByIdBatch(updateEffectiveStepList, "tmused");
        }
        if (StringUtils.isNotEmpty(updateStepList)) {
            entityService.updateByIdBatch(updateStepList, "accOrgCode", "accOrgName", "handleResult", "currentStatus", "handleUserOrgCode");
        }
        if (StringUtils.isNotEmpty(insertStepList)) {
            entityService.insertBatch(insertStepList, 1000);
        }

        if (StringUtils.isNotEmpty(insertOrgInfoList)) {
            entityService.insertBatch(insertOrgInfoList, 1000);
        }
        if (StringUtils.isNotEmpty(updateOrgInfoList)) {
            entityService.updateByIdBatch(updateOrgInfoList, "cardId", "catalogAlias", "cardName", "cardExecType");
        }
        if (StringUtils.isNotEmpty(insertAccList)) {
            entityService.insertBatch(insertAccList, 1000);
        }
        if (StringUtils.isNotEmpty(insertInputAccList)) {
            WIProcessDataStepAcc firstAcc = insertInputAccList.get(0);
            entityService.deleteAndInsert(WIProcessDataStepAcc.class, Where.create().eq(WIProcessDataStepAcc::getProcessDataStepId, firstAcc.getProcessDataStepId()), insertInputAccList);
        }
        if (StringUtils.isNotEmpty(deleteAccList)) {
            entityService.deleteByIdBatch(deleteAccList, 1000);
        }
        if (StringUtils.isNotEmpty(updateAccList)) {
            entityService.updateByIdBatch(updateAccList, "handleResult", "handleTime", "handleUserId", "handleUserName", "handleMessage", "currentStatus", "handleUserOrgCode");
        }

        if (currentStep.getSelectCard() == 1 && currentOrgInfo != null) {
            //选卡步骤时
            Where opWhere = Where.create().eq(WIDataOrgOpCardInfo::getDataId, currentOrgInfo.getDataId()).eq(WIDataOrgOpCardInfo::getAccOrgCode, currentOrgInfo.getAccOrgCode());
            if (StringUtils.isNotEmpty(currentOrgInfo.getCardId())) {
                WIDataOrgOpCardInfo opCardInfo = this.getOpCardInfo(currentOrgInfo);
                entityService.deleteAndInsert(WIDataOrgOpCardInfo.class, opWhere, new ArrayList<>(Collections.singletonList(opCardInfo)));
            } else {
                entityService.rawDeleteByWhere(WIDataOrgOpCardInfo.class, opWhere);
            }
        }

        String ccCode = null;
        //给抄送人发消息
        if (currentStep.getCarbonCopy() == 1) {
            //需要抄送
            String carbonCopyCode = param.getCarbonCopyCode();
            String carbonCopyText = param.getCarbonCopyText();
            if (StringUtils.isNotEmpty(carbonCopyCode)) {
                //前台传入的抄送人
                ccCode = carbonCopyCode;
//                this.sendMsgToCc(data, currentStep, 1, carbonCopyCode, currentStep.getAccOrgCode());
            }
//            else if (StringUtils.isNotEmpty(currentStep.getCarbonCopyCode())){
//                //前台没传入，用步骤中的配置解析
//                this.sendMsgToCc(data, currentStep, currentStep.getCarbonCopyType(), currentStep.getCarbonCopyCode(), currentStep.getAccOrgCode());
//            }
        }

        //给下一步接收人发消息
//        if (StringUtils.isNotEmpty(nextStepList)) {
//            this.sendMsgToNextStep(data, nextStepList);
//        }
        msgSrv.sendTodoMsgAndCcMsg(data, currentStep, nextStepList, 1, ccCode);
        return null;
    }


    public Set<String> getHandleUserIdSetByOrgCode (WIData data, String orgCode) {
        Set<String> result = new HashSet<>();
        //接收人以及审核
        List<WIProcessDataStep> stepList = entityService.queryData(WIProcessDataStep.class, Where.create().eq(WIProcessDataStep::getDataId, data.getId()).eq(WIProcessDataStep::getTmused, 1).ne(WIProcessDataStep::getHandleResult, 0), Order.create(WIProcessDataStep::getStepNo), null);
        if (StringUtils.isNotEmpty(stepList)) {
            for (WIProcessDataStep eachStep : stepList) {
                int handleResult = eachStep.getHandleResult();
                if (handleResult != 0 && handleResult != -100 && StringUtils.isNotEmpty(eachStep.getHandleUserId())) {
                    //处理过的人员
                    if (StringUtils.isEmpty(orgCode)) {
                        result.add(eachStep.getHandleUserId());
                    } else if (eachStep.getInAcceptOrg() == 0 || (eachStep.getInAcceptOrg()==1 && orgCode.equals(eachStep.getAccOrgCode()))) {
                        //上级单位以及自己单位
                        result.add(eachStep.getHandleUserId());
                    }
                }
            }
        }
        return result;
    }


    public Set<String> getHandleUserIdSet (WIData data) {
        return getHandleUserIdSetByOrgCode(data, null);
    }

    /**
     * 给抄送人发消息
     * @param data
     * @param carbonCopyType
     * @param carbonCopyCode
     * @param carbonOrgCode
     * @return
     */
    public String sendMsgToCc (WIData data, WIProcessDataStep currentStep, int carbonCopyType, String carbonCopyCode, String carbonOrgCode) {
        List<EmployeeVo> empList = this.getUserByAccCode(data, carbonCopyType, carbonCopyCode, carbonOrgCode);
        //TODO 发消息
        System.out.println("发送消息给抄送人：" + empList);
        return null;
    }

    public String sendMsgToNextStep (WIData data, List<WIProcessDataStep> dataStepList) {
        for (WIProcessDataStep eachStep : dataStepList) {
            int stepHandle = eachStep.getStepHandle();
            String handleText = stepHandle == 1 ? "待审核" : "待接收";
//            Set<String> userIdSet = this.getUserIdSetByAccCode(data, eachStep.getAccType(), eachStep.getAccCode(), eachStep.getAccOrgCode());
            List<EmployeeVo> empList = this.getUserByAccCode(data, 0, "eachStep.getAccCode()", "eachStep.getAccOrgCode()");
            if (StringUtils.isNotEmpty(empList)) {
                //TODO 发消息
                System.out.println("发送消息给下一级审核人：" + empList);
            }
        }
        return null;
    }

    public List<EmployeeVo> getUserByAccCode (WIData data, int accType, String accCode, String accOrgCode) {
//        int auditStep = dataStep.getAuditStep();
//        String handleText = auditStep == 1 ? "待审核" : "待接收";
//        int accType = dataStep.getAccType();
//        String accCode = dataStep.getAccCode();
//        String accOrgCode = dataStep.getAccOrgCode();
        if (StringUtils.isEmpty(accCode)) {
            return null;
        }
        List<EmployeeVo> result = new ArrayList<>();
        if (accType == 1) {
            //人员
            result = empSrv.getEmployee(new ArrayList<>(Arrays.asList(accCode.split(","))));
//            result.addAll(Arrays.asList(accCode.split(",")));
        } else if (accType == 2) {
            //岗位
//            result = StringUtils.isEmpty(accOrgCode) ? null : empSrv.getEmployeeByOrgcode(accOrgCode);
            List<EmployeeVo> empList = StringUtils.isEmpty(accOrgCode) ? null : empChangeInfoSrv.getOrgUserChange(DateTimeUtils.getNowDateStr(), accOrgCode, true, true);
            if (StringUtils.isNotEmpty(empList)) {
                result.addAll(empList);
            }

//            if (StringUtils.isNotEmpty(accOrgUsers)) {
//                result.addAll(accOrgUsers.stream().map(EmployeeVo::getEmpTmuid).collect(Collectors.toList()));
//            }
        } else if (accType == 3) {
            //机构
            for (String eachOrg : accCode.split(",")) {
                List<EmployeeVo> eachList = empChangeInfoSrv.getOrgUserChange(DateTimeUtils.getNowDateStr(), eachOrg, true, true);
                if (StringUtils.isNotEmpty(eachList)) {
                    result.addAll(eachList);
//                    result.addAll(eachList.stream().map(EmployeeVo::getEmpTmuid).collect(Collectors.toList()));
                }
            }
        } else if (accType == 100 && StringUtils.isNotEmpty(accOrgCode)) {
            //自定义变量
            for (String eachCode : accCode.split(",")) {
                List<EmployeeVo> userList = processVarSrv.getUserByVar(eachCode, accOrgCode);
                if (StringUtils.isNotEmpty(userList)) {
                    result.addAll(userList);
                }
            }
        }
        return result;
    }


    public List<WIProcessDataStepAcc> getDataStepAccListByStep(WIProcessDataStep step, int type, String code, String text) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        List<WIProcessDataStepAcc> result = new ArrayList<>();
        String[] codeSplit = code.split(",");
        String[] textSplit = text.split(",");
        for (int i = 0; i < codeSplit.length; i++) {
            String eachCode = codeSplit[i];
            String eachText = textSplit[i];
            WIProcessDataStepAcc acc = new WIProcessDataStepAcc();
            acc.setId(TMUID.getUID());
            acc.setDataId(step.getDataId());
            acc.setProcessDataId(step.getProcessDataId());
            acc.setProcessDataStepId(step.getId());
            acc.setObjType(1);
            acc.setAccType(type);
            acc.setAccOrgCode(step.getAccOrgCode());
            acc.setAccOrgName(step.getAccOrgName());
            acc.setAccCode(eachCode);
            acc.setAccText(eachText);
            acc.setHandleResult(step.getHandleResult());
            acc.setCurrentStatus(step.getCurrentStatus());
            acc.setTmsort(i+1);

            result.add(acc);
        }
        return result;
    }

    public String dataTransfer(WIDataDto param) {
        if (param == null) {
            return "没有要结转的记录";
        }
        WIData data = this.queryDataById(param.getId());
        if (data == null) {
            return "未找到记录";
        }
        String stepParams = param.getDataStepId();
        if (StringUtils.isEmpty(stepParams)) {
            return "没找到当前节点";
        }
        WIProcessDataStep currentAcceptStep = null;
        WIProcessDataStepAcc currentAcceptStepAcc = null;
        WIProcessDataStep currentInputStep = null;
        WIProcessDataStepAcc currentInputStepAcc = null;
        if (StringUtils.isNotEmpty(stepParams)) {
            JSONObject jsonObject = JSON.parseObject(stepParams);
            String inputStepId = jsonObject.containsKey("inputStepId") ? jsonObject.getString("inputStepId") : "";
            String inputStepAccId = jsonObject.containsKey("inputStepAccId") ? jsonObject.getString("inputStepAccId") : "";
            if (StringUtils.isNotEmpty(inputStepId)) {
                //接收指令时，查当前节点信息
                currentInputStep = entityService.queryObjectById(WIProcessDataStep.class, inputStepId);
            }
            if (StringUtils.isNotEmpty(inputStepAccId)) {
                //接收指令时，查当前节点信息
                currentInputStepAcc = entityService.queryObjectById(WIProcessDataStepAcc.class, inputStepAccId);
            }
            String acceptStepId = jsonObject.containsKey("acceptStepId") ? jsonObject.getString("acceptStepId") : "";
            String acceptStepAccId = jsonObject.containsKey("acceptStepAccId") ? jsonObject.getString("acceptStepAccId") : "";
            if (StringUtils.isNotEmpty(acceptStepId)) {
                //接收指令时，查当前节点信息
                currentAcceptStep = entityService.queryObjectById(WIProcessDataStep.class, acceptStepId);
            }
            if (StringUtils.isNotEmpty(acceptStepAccId)) {
                //接收指令时，查当前节点信息
                currentAcceptStepAcc = entityService.queryObjectById(WIProcessDataStepAcc.class, acceptStepAccId);
            }
        }
//        WIProcessDataStep currentStep = entityService.queryObjectById(WIProcessDataStep.class, dataStepId);
        if (currentAcceptStep == null || currentAcceptStepAcc == null) {
            return "没找到当前节点";
        }
        if (currentAcceptStep.getSelectInputUser() != 1) {
            return "只有选择执行人员的节点才能结转";
        }
        int currentStatus = currentAcceptStepAcc.getCurrentStatus();
        if (currentStatus != 1) {
            return "当前节点已通过，无法结转";
        }
//        String inputFromId = currentStep.getInputFromId();
//        WIProcessDataStep fromStep = entityService.queryObjectById(WIProcessDataStep.class, inputFromId);
//        if (fromStep == null) {
//            return "未找到对应记录，无法结转";
//        }
        String nextOrgCode = null;
        String nextOrgName = null;
        ShiftForeignVo currentUserShiftInfo = dataUtils.getCurrentUserShiftInfo();
        String xbsj = Optional.ofNullable(currentUserShiftInfo).map(ShiftForeignVo::getXbsj).orElse(null);
        if (StringUtils.isNotEmpty(xbsj)) {
            //查车间内下个班次
            String acceptOrgCode = currentAcceptStep.getAccOrgCode();
            List<SysOrg> childrenOrg = orgSrv.getOrgList(acceptOrgCode);
            List<String> orgCodeList = childrenOrg.stream().map(SysOrg::getOrgcode).collect(Collectors.toList());
            //下班时间加1秒
            String nextTime = DateTimeUtils.formatDateTime(DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(xbsj),1));
//            ShiftForeignVo nextShift = shiftSrv.getShiftByOrgListDateTime(orgCodeList, nextTime);
            List<ShiftForeignVo> nextShiftList = dataUtils.getOrgShiftInfo(acceptOrgCode, nextTime);
            if (StringUtils.isNotEmpty(nextShiftList)) {
                String currentProId = Optional.ofNullable(currentUserShiftInfo.getProgId()).orElse("");
                ShiftForeignVo nextShift = nextShiftList.stream().filter(item -> StringUtils.isNotEmpty(item.getOrgCode()) && currentProId.equals(Optional.ofNullable(item.getProgId()).orElse(""))).findFirst().orElse(null);
                if (nextShift != null) {
                    nextOrgCode = nextShift.getOrgCode();
                    nextOrgName = nextShift.getOrgName();
                }
            }
        }

        if (StringUtils.isEmpty(nextOrgCode)) {
            return "未找到下一个班次，无法结转";
        }

        currentAcceptStep.setHandleResult(1);
        currentAcceptStep.setHandleTime(DateTimeUtils.getNowDateTimeStr());
        currentAcceptStep.setHandleUserId(SysUserUtil.getCurrentUser().getId());
        currentAcceptStep.setHandleUserName(SysUserUtil.getCurrentUser().getRealName());
        currentAcceptStep.setCurrentStatus(0);
        currentAcceptStep.setTransferToOrgCode(nextOrgCode);
        currentAcceptStep.setTransferToOrgName(nextOrgName);

        currentAcceptStepAcc.setHandleResult(1);
        currentAcceptStepAcc.setHandleTime(DateTimeUtils.getNowDateTimeStr());
        currentAcceptStepAcc.setHandleUserId(SysUserUtil.getCurrentUser().getId());
        currentAcceptStepAcc.setHandleUserName(SysUserUtil.getCurrentUser().getRealName());
        currentAcceptStepAcc.setCurrentStatus(0);
        currentAcceptStepAcc.setHandleTransfer(1);

        int maxStepNo = currentAcceptStep.getStepNo();
        List<String> stepIdList = new ArrayList<>();
        stepIdList.add(currentAcceptStep.getId());
        List<WIProcessDataStep> stepList = entityService.queryData(WIProcessDataStep.class, Where.create().eq(WIProcessDataStep::getInputFromId, currentAcceptStep.getId()), null, null);
        if (StringUtils.isNotEmpty(stepList)) {
            for (WIProcessDataStep step : stepList) {
                step.setCurrentStatus(0);
                stepIdList.add(step.getId());
                maxStepNo = Math.max(maxStepNo, step.getStepNo());
            }
        }
        List<WIProcessDataStepAcc> updateAccList = new ArrayList<>();
        List<WIProcessDataStepAcc> currentAccList = new ArrayList<>();
        List<WIProcessDataStepAcc> accList = entityService.queryData(WIProcessDataStepAcc.class, Where.create().in(WIProcessDataStepAcc::getProcessDataStepId, stepIdList.toArray()).eq(WIProcessDataStepAcc::getObjType, 1), Order.create().orderByAsc(WIProcessDataStepAcc::getTmsort), null);
        if (StringUtils.isNotEmpty(accList)) {
            for (WIProcessDataStepAcc acc : accList) {
                if (!acc.getId().equals(currentAcceptStepAcc.getId())) {
                    //不是当前节点接收
                    acc.setCurrentStatus(0);
                    updateAccList.add(acc);
                }
                if (acc.getProcessDataStepId().equals(currentAcceptStep.getId())) {
                    //当前节点的接收
                    currentAccList.add(acc);
                } else {
                    //填写节点
                }
            }
        }

        entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentAcceptStep)), "handleResult", "handleTime", "handleUserId", "handleUserName", "currentStatus", "handleTransfer", "transferToOrgCode", "transferToOrgName");
        entityService.updateByIdBatch(new ArrayList<>(Collections.singletonList(currentAcceptStepAcc)), "handleResult", "handleTime", "handleUserId", "handleUserName", "currentStatus", "handleTransfer");
        if (StringUtils.isNotEmpty(stepList)) {
            entityService.updateByIdBatch(stepList, "currentStatus");
        }
        if (StringUtils.isNotEmpty(updateAccList)) {
            entityService.updateByIdBatch(updateAccList,  "currentStatus");
        }


        WIProcessDataStep newNextStep = new WIProcessDataStep();
        newNextStep.setInputStartId(StringUtils.isNotEmpty(currentAcceptStep.getInputStartId()) ? currentAcceptStep.getInputStartId() : currentAcceptStep.getId());
        newNextStep.setInputFromId(null);
        newNextStep.setId(TMUID.getUID());
        newNextStep.setDataId(currentAcceptStep.getDataId());
        newNextStep.setStepName("结转");
        newNextStep.setStepDesc("结转");
        newNextStep.setProcessDataId(currentAcceptStep.getProcessDataId());
        newNextStep.setProcessId(currentAcceptStep.getProcessId());
        newNextStep.setInAcceptOrg(currentAcceptStep.getInAcceptOrg());
        newNextStep.setAccOrgCode(currentAcceptStep.getAccOrgCode());
        newNextStep.setAccOrgName(currentAcceptStep.getAccOrgName());
        newNextStep.setHandleResult(0);
        newNextStep.setHandleTransfer(0);
        newNextStep.setTransferToOrgCode(null);
        newNextStep.setTransferToOrgName(null);
        newNextStep.setSelectAcceptOrg(0);
        newNextStep.setSelectCard(0);
        newNextStep.setSelectInputUser(1);
        newNextStep.setStepHandle(5);
        newNextStep.setCurrentStatus(1);
        newNextStep.setTmused(1);
        newNextStep.setStepNo(maxStepNo+1);
        if (StringUtils.isNotEmpty(nextOrgCode)) {
            newNextStep.setExpireAssignOrgCode(nextOrgCode);
        }

        List<WIProcessDataStepAcc> insertAccList = new ArrayList<>();
        if (StringUtils.isNotEmpty(currentAccList)) {
            for (WIProcessDataStepAcc acc : currentAccList) {
                WIProcessDataStepAcc newAcc = ObjUtils.copyTo(acc, WIProcessDataStepAcc.class);
                newAcc.setId(TMUID.getUID());
                newAcc.setCurrentStatus(1);
                newAcc.setProcessDataStepId(newNextStep.getId());
                newAcc.setHandleResult(0);
                newAcc.setHandleTime(null);
                newAcc.setHandleUserId(null);
                newAcc.setHandleUserName(null);
                newAcc.setHandleMessage(null);
                newAcc.setHandleTransfer(0);
                insertAccList.add(newAcc);
            }
        }

        entityService.insert(newNextStep);
        if (StringUtils.isNotEmpty(insertAccList)) {
            newNextStep.setAccList(insertAccList);
            entityService.insertBatch(insertAccList, 1000);
        }


        msgSrv.sendTodoMsgAndCcMsg(data, currentAcceptStep, new ArrayList<>(Collections.singletonList(newNextStep)), 0, null);

        return null;
    }

    private WIData queryDataById(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        return entityService.queryObjectById(WIData.class, id);
    }
}
