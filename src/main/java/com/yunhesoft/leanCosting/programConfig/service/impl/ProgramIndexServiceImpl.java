package com.yunhesoft.leanCosting.programConfig.service.impl;


import com.yunhesoft.core.common.script.ScriptManager;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.programConfig.entity.dto.IndexQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.dto.IndexSaveDto;
import com.yunhesoft.leanCosting.programConfig.entity.dto.ProgramLibraryCostQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramContingencyPlan;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramIndexFormula;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramIndicator;
import com.yunhesoft.leanCosting.programConfig.entity.vo.IndexProgramVo;
import com.yunhesoft.leanCosting.programConfig.entity.vo.ProgramContingencyPlanVo;
import com.yunhesoft.leanCosting.programConfig.service.IProgramIndexService;
import com.yunhesoft.leanCosting.programConfig.service.IProgramLibraryCostService;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampledotVo;
import com.yunhesoft.leanCosting.unitConf.service.IUnitInterfaceService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *	指标方案相关接口实现类
 * <AUTHOR>
 * @date 2023-08-18
 */
@Service
public class ProgramIndexServiceImpl implements IProgramIndexService {

	
	@Autowired
	private EntityService entityService;
		
	@Autowired
	private IUnitInterfaceService interfaceService; //通用接口
	
	@Autowired
	private IProgramLibraryCostService costService; //核算接口
	
	private ScriptManager sm = new ScriptManager();
	
	
	/**
	 *	获取指标方案数据
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<IndexProgramVo> getIndexProgramList(IndexQueryDto queryDto) {
		List<IndexProgramVo> result = new ArrayList<IndexProgramVo>();
		List<ProgramIndicator> initList = new ArrayList<ProgramIndicator>();
		List<ProgramIndicator> updList = new ArrayList<ProgramIndicator>();
		if(StringUtils.isNotNull(queryDto)) {
			String pVersion = queryDto.getPVersion();
			String unitcode = queryDto.getUnitcode();
			String pvId = queryDto.getPvId();
			Pagination<?> page = queryDto.getPage();
			String itemName = queryDto.getItemName();
			if(StringUtils.isNotEmpty(pVersion)&&StringUtils.isNotEmpty(unitcode)&&StringUtils.isNotEmpty(pvId)) {
				//调用接口获取采集点数据列表（过滤出采集点数据）
				List<SampledotVo> dotVoList = this.filterSampledotData(interfaceService.getSampledotListByClass(unitcode, pVersion, "1", itemName, page));
				if(StringUtils.isNotEmpty(dotVoList)) {
					
					//获取计算指标合格限值公式
					ProgramIndexFormula formulaObj = this.getIndexFormulaObj();
					
					//接口有数据--->获取设置数据
					IndexQueryDto setDto = new IndexQueryDto();
					setDto.setPvId(pvId);
					Map<String, List<ProgramIndicator>> setMap = new HashMap<String, List<ProgramIndicator>>();
					List<ProgramIndicator> setList = this.getProgramIndicatorList(setDto);
					if(StringUtils.isNotEmpty(setList)) {
						setMap = setList.stream().collect(Collectors.groupingBy(ProgramIndicator::getIname));
					}
					//获取预案数据
					ProgramLibraryCostQueryDto costQueryDto = new ProgramLibraryCostQueryDto();
					costQueryDto.setPvId(pvId);
					Map<String, List<ProgramContingencyPlan>> planMap = new HashMap<String, List<ProgramContingencyPlan>>();
					List<ProgramContingencyPlan> planList = costService.getProgramContingencyPlanList(costQueryDto);
					if(StringUtils.isNotEmpty(planList)) {
						planMap = planList.stream().collect(Collectors.groupingBy(ProgramContingencyPlan::getPId));
					}
					
					//遍历接口查询数据
					String hasClassName = "";
					for (int i = 0; i < dotVoList.size(); i++) {
						SampledotVo vo = dotVoList.get(i);
						String pcName_vo = StringUtils.isEmpty(vo.getParentName())?"无分类名称":vo.getParentName();
						String showClassName = pcName_vo;
						if(hasClassName.equals(showClassName)) {
							showClassName = "";
						}else {
							hasClassName = showClassName;
						}
						String name_vo = vo.getName();
						String saveType = "";
						IndexProgramVo indexVo = new IndexProgramVo();
						if(StringUtils.isNotEmpty(setMap)&&setMap.containsKey(name_vo)) {
							ProgramIndicator setObj = setMap.get(name_vo).get(0);
			    			BeanUtils.copyProperties(setObj, indexVo);
						}else {
							Double indexRangeUpper = vo.getIndexRangeUpper();
							if(indexRangeUpper!=null) {
								String valStr = new BigDecimal(String.valueOf(indexRangeUpper)).setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
								indexRangeUpper = Double.valueOf(valStr);
							}
							Double indexRangeLower = vo.getIndexRangeLower();
							if(indexRangeLower!=null) {
								String valStr = new BigDecimal(String.valueOf(indexRangeLower)).setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
								indexRangeLower = Double.valueOf(valStr);
							}
							Double targetValue = vo.getTargetValue();
							if(targetValue!=null) {
								String valStr = new BigDecimal(String.valueOf(targetValue)).setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
								targetValue = Double.valueOf(valStr);
							}
							saveType = "add";
							indexVo.setId(TMUID.getUID());
							indexVo.setPvId(pvId);
							indexVo.setIname(name_vo);
							indexVo.setTmUsed(1);
							indexVo.setOperateUpLimit(indexRangeUpper); //指标范围上限
							indexVo.setOperateLowLimit(indexRangeLower); //指标范围下限
							indexVo.setTargetValue(targetValue);
						}
						indexVo.setTmSort(i+1);
						indexVo.setShowClassName(showClassName);
						indexVo.setPcName(pcName_vo);
						indexVo.setSdUnit(vo.getSdUnit());
						//计算合格限值
						this.calcKeyLimitByFormula(indexVo, formulaObj, saveType, updList, false);
						//设置其他属性（超限预案和接收人等）
						this.setOtherAttribute(indexVo, planMap);
						result.add(indexVo);
						//新增记录保存到数据库
						if("add".equals(saveType)) {
							ProgramIndicator indexObj = new ProgramIndicator();
			    			BeanUtils.copyProperties(indexVo, indexObj);
			    			initList.add(indexObj);
						}
					}
				}
			}
		}
		if (StringUtils.isNotEmpty(initList)||StringUtils.isNotEmpty(updList)) { //初始化新记录，只保存主表即可（因为没有录入子数据）
			this.saveProgramIndicatorData(initList, updList, null);
        }
		return result;
	}
	
	/**
	 *	根据公式配置计算合格限值
	 * @param vo
	 * @param formulaObj
	 * @param saveType
	 * @param updList
	 * @param isCalFormula
	 */
	private void calcKeyLimitByFormula(IndexProgramVo vo, ProgramIndexFormula formulaObj,
		String saveType, List<ProgramIndicator> updList, boolean isCalFormula) {
		if(vo!=null&&formulaObj!=null&&StringUtils.isNotEmpty(formulaObj.getLastEditTime())) { //有效记录
			boolean isCal = false;
			if(isCalFormula) { //直接计算
				isCal = true;
			}else { //判断是否计算
				Date updDt = vo.getUpdateTime()!=null?vo.getUpdateTime():vo.getCreateTime();
				Date lastEditTime = DateTimeUtils.parseDateTime(formulaObj.getLastEditTime());
				if(updDt!=null) { //修改记录，判断时间是否需要计算
					if(lastEditTime.compareTo(updDt)>=0) {
						isCal = true;
					}
				}else { //新记录，计算
					isCal = true;
				}
			}
			if(isCal) {
				Double operateLowLimit_vo = vo.getOperateLowLimit();
				Double operateUpLimit_vo = vo.getOperateUpLimit();
				if(operateLowLimit_vo!=null&&operateUpLimit_vo!=null) { //有上下限
					//合格限值--下限
					String allLowFormula = formulaObj.getAllLowFormula();
					Double keyLowLimit = this.calcFormulaValue(allLowFormula, vo);
					vo.setKeyLowLimit(keyLowLimit);
					//合格限值--下限
					String allUpFormula = formulaObj.getAllUpFormula();
					Double keyUpLimit = this.calcFormulaValue(allUpFormula, vo);
					vo.setKeyUpLimit(keyUpLimit);
				}else if(operateLowLimit_vo!=null&&operateUpLimit_vo==null) { //仅有下限
					String onlyLowFormula = formulaObj.getOnlyLowFormula();
					Double keyLowLimit = this.calcFormulaValue(onlyLowFormula, vo);
					vo.setKeyLowLimit(keyLowLimit);
					vo.setKeyUpLimit(null);
				}else if(operateLowLimit_vo==null&&operateUpLimit_vo!=null) { //仅有上限
					vo.setKeyLowLimit(null);
					String onlyUpFormula = formulaObj.getOnlyUpFormula();
					Double keyUpLimit = this.calcFormulaValue(onlyUpFormula, vo);
					vo.setKeyUpLimit(keyUpLimit);
				}else { //都没有
					vo.setKeyLowLimit(null);
					vo.setKeyUpLimit(null);
				}
				if(!"add".equals(saveType)&&updList!=null) { //非新增记录，需要存入修改列表
					ProgramIndicator indexObj = new ProgramIndicator();
	    			BeanUtils.copyProperties(vo, indexObj);
	    			updList.add(indexObj);
				}
			}
		}
	}
	
	//解析公式值
	private Double calcFormulaValue(String formula, IndexProgramVo vo) {
		Double result = null;
		if(StringUtils.isNotEmpty(formula)&&vo!=null) {
			Double operateLowLimit_vo = vo.getOperateLowLimit()==null?0d:vo.getOperateLowLimit();
			Double operateUpLimit_vo = vo.getOperateUpLimit()==null?0d:vo.getOperateUpLimit();
			Double targetValue_vo = vo.getTargetValue()==null?0d:vo.getTargetValue();
			
			formula = formula.replaceAll("\\[指标下限\\]", String.valueOf(operateLowLimit_vo));
			formula = formula.replaceAll("\\[指标上限\\]", String.valueOf(operateUpLimit_vo));
			formula = formula.replaceAll("\\[目标值\\]", String.valueOf(targetValue_vo));
			try {
				Object checkRet = sm.eval(formula);
				if(checkRet!=null) {
					String checkRetStr = checkRet.toString();
					checkRetStr = new BigDecimal(checkRetStr).setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
					result = Double.valueOf(checkRetStr);
				}
			}catch(Exception e) {}
		}
		return result;
	}
	
	/**
	 *	解析合格限值公式
	 * @param vo
	 * @return
	 */
	@Override
	public IndexProgramVo getKeyLimitByFormula(IndexProgramVo vo) {
		if(vo!=null) {
			//获取计算指标合格限值公式
			ProgramIndexFormula formulaObj = this.getIndexFormulaObj();
			//计算合格限值
			this.calcKeyLimitByFormula(vo, formulaObj, null, null, true);
		}
		return vo;
	}
	
	/**
	 *	过滤采集点数据
	 * @param list
	 * @return
	 */
	private List<SampledotVo> filterSampledotData(List<SampledotVo> list) {
		List<SampledotVo> result = new ArrayList<SampledotVo>();
		if(StringUtils.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				SampledotVo obj = list.get(i);
				String ctype = obj.getCtype(); //采集类型---刨除“成本仪表”
				if(StringUtils.isNotEmpty(ctype)&&"1".equals(ctype)) {
					continue;
				}
				result.add(obj);
			}
		}
		return result;
	}
	
	
	/**
	 *	获取指标方案设置数据
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<ProgramIndicator> getProgramIndicatorList(IndexQueryDto queryDto) {
		List<ProgramIndicator> result = new ArrayList<ProgramIndicator>();
		try {
			String pvId = ""; //方案版本ID
			List<String> pvIdList = null; //方案版本ID列表
			if(StringUtils.isNotNull(queryDto)) {
				pvId = queryDto.getPvId();
				pvIdList = queryDto.getPvIdList();
			}
			//检索条件
			Where where = Where.create();
			//不判断tmUsed；要显示记录
			if(StringUtils.isNotEmpty(pvId)) {
				where.eq(ProgramIndicator::getPvId, pvId);
			}
			if(StringUtils.isNotEmpty(pvIdList)) {
				where.in(ProgramIndicator::getPvId, pvIdList.toArray());
			}
			//排序
			Order order = Order.create();
			order.orderByAsc(ProgramIndicator::getPvId);
			order.orderByAsc(ProgramIndicator::getIname);
			order.orderByDesc(ProgramIndicator::getId);
			List<ProgramIndicator> list = entityService.queryData(ProgramIndicator.class, where, order, null);
			if(StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	
	/**
	 *	保存成本项目方案数据
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveIndexProgramData(IndexSaveDto saveDto) {
		String result = "";
		List<ProgramIndicator> addList = new ArrayList<ProgramIndicator>();
       	List<ProgramIndicator> updList = new ArrayList<ProgramIndicator>();
       	
       	List<ProgramContingencyPlan> addPlanList = new ArrayList<ProgramContingencyPlan>();
       	List<ProgramContingencyPlan> updPlanList = new ArrayList<ProgramContingencyPlan>();
       	List<ProgramContingencyPlan> delPlanList = new ArrayList<ProgramContingencyPlan>();
       	
		if (saveDto != null) {
			String editType = saveDto.getEditType();
			String pvId = saveDto.getPvId();
			List<IndexProgramVo> saveList = saveDto.getIndexProgramList();
			if (StringUtils.isNotEmpty(editType)&&StringUtils.isNotEmpty(pvId)&&StringUtils.isNotEmpty(saveList)) {

				IndexQueryDto queryDto = new IndexQueryDto();
				queryDto.setPvId(pvId);
				Map<String, List<ProgramIndicator>> setMap = new HashMap<String, List<ProgramIndicator>>();
				List<ProgramIndicator> setList = this.getProgramIndicatorList(queryDto);
				if(StringUtils.isNotEmpty(setList)) {
					setMap = setList.stream().collect(Collectors.groupingBy(ProgramIndicator::getIname));
				}
				//获取预案数据
				ProgramLibraryCostQueryDto costQueryDto = new ProgramLibraryCostQueryDto();
				costQueryDto.setPvId(pvId);
				Map<String, List<ProgramContingencyPlan>> planMap = new HashMap<String, List<ProgramContingencyPlan>>();
				List<ProgramContingencyPlan> planList = costService.getProgramContingencyPlanList(costQueryDto);
				if(StringUtils.isNotEmpty(planList)) {
					planMap = planList.stream().collect(Collectors.groupingBy(ProgramContingencyPlan::getPId));
				}
            	
            	for (int i = 0; i < saveList.size(); i++) {
            		IndexProgramVo saveObj = saveList.get(i);
            		if("save".equals(editType)) { //保存
            			ProgramIndicator indexObj = null;
            			String iname = saveObj.getIname();
        				if(StringUtils.isNotEmpty(setMap)&&setMap.containsKey(iname)) { //修改
        					indexObj = setMap.get(iname).get(0);
        				}
        				if(indexObj!=null) { //修改
        					String id = indexObj.getId();
        					BeanUtils.copyProperties(saveObj, indexObj); //赋予返回对象
        					indexObj.setId(id);
    						updList.add(indexObj);
    					}else { //新增
    						String saveId = saveObj.getId();
    						if(StringUtils.isEmpty(saveId)) {
    							saveObj.setId(TMUID.getUID());
    						}
    						saveObj.setPvId(pvId);
    						indexObj = new ProgramIndicator();
    		    			BeanUtils.copyProperties(saveObj, indexObj); //赋予返回对象
    						addList.add(indexObj);
    					}
    					//获取最新超限预案和接收人数据
        				costService.getSavePlanPersonData(saveObj.getId(),pvId,saveObj.getPlanList(),planMap,addPlanList,updPlanList,delPlanList);
            		}else if("del".equals(editType)) { //删除
            			//暂时没有删除操作
            		}
				}
            }
        }
		if ("".equals(result)) {
			result = this.saveProgramIndicatorData(addList, updList, null);
        }
		if ("".equals(result)) {
			result = costService.savePlanData(addPlanList, updPlanList, delPlanList);
        }
		return result;
	}
	
	
	/**
	 *	按照版本更新核算方案设置数据
	 * @param operType
	 * @param pvId
	 * @param newPvId
	 * @return
	 */
	@Override
	public String renewByVersionIdIndexProgram(String operType,String pvId,String newPvId) {
		String result = "";
		if(StringUtils.isNotEmpty(operType)) {
			List<ProgramIndicator> addList = new ArrayList<ProgramIndicator>();
			List<ProgramIndicator> delList = new ArrayList<ProgramIndicator>();
			List<ProgramContingencyPlan> addPlanList = new ArrayList<ProgramContingencyPlan>();
			List<ProgramContingencyPlan> delPlanList = new ArrayList<ProgramContingencyPlan>();
			if("add".equals(operType)) { //新增方案版本时
				if(StringUtils.isNotEmpty(pvId)&&StringUtils.isNotEmpty(newPvId)) {
					IndexQueryDto dto = new IndexQueryDto();
					dto.setPvId(pvId);
					List<ProgramIndicator> costItemList = this.getProgramIndicatorList(dto);
					if(StringUtils.isNotEmpty(costItemList)) {
						//获取预案数据
						ProgramLibraryCostQueryDto costQueryDto = new ProgramLibraryCostQueryDto();
						costQueryDto.setPvId(pvId);
						Map<String, List<ProgramContingencyPlan>> planMap = new HashMap<String, List<ProgramContingencyPlan>>();
						List<ProgramContingencyPlan> planList = costService.getProgramContingencyPlanList(costQueryDto);
						if(StringUtils.isNotEmpty(planList)) {
							planMap = planList.stream().collect(Collectors.groupingBy(ProgramContingencyPlan::getPId));
						}
						
						for (int i = 0; i < costItemList.size(); i++) {
							ProgramIndicator costItemObj = costItemList.get(i);
							String id = costItemObj.getId();							
							String newid = TMUID.getUID();
							costItemObj.setId(newid);
							costItemObj.setPvId(newPvId);
							addList.add(costItemObj);
							//超限预案
							costService.getPlanPersionDataByAdd(id,newid,newPvId,planMap,addPlanList);
						}
					}
				}
			}else if("del".equals(operType)) { //删除方案版本时
				if(StringUtils.isNotEmpty(pvId)) {
            		String[] pvIdArr = pvId.split(",");
            		List<String> pvIdList = Arrays.asList(pvIdArr);
					IndexQueryDto dto = new IndexQueryDto();
					dto.setPvIdList(pvIdList);
					List<ProgramIndicator> costItemList = this.getProgramIndicatorList(dto);
					if(StringUtils.isNotEmpty(costItemList)) {
						//获取预案数据
						ProgramLibraryCostQueryDto costQueryDto = new ProgramLibraryCostQueryDto();
						costQueryDto.setPvId(pvId);
						HashMap<String, List<ProgramContingencyPlan>> planMap = new HashMap<String, List<ProgramContingencyPlan>>();
						List<ProgramContingencyPlan> planList = costService.getProgramContingencyPlanList(costQueryDto);
						if(StringUtils.isNotEmpty(planList)) {
							planMap = costService.getPlanMapByPvId_PId(planList);
						}
						for (int i = 0; i < costItemList.size(); i++) {
							ProgramIndicator costItemObj = costItemList.get(i);
							delList.add(costItemObj);
							String item_key = costItemObj.getPvId()+"_"+costItemObj.getId();
							//超限预案
							costService.getPlanPersionDataByDel(item_key,planMap,delPlanList);
						}
					}
				}
			}
			if ("".equals(result)) {
				result = this.saveProgramIndicatorData(addList, null, delList);
	        }
			if ("".equals(result)) {
				result = costService.savePlanData(addPlanList, null, delPlanList);
	        }
		}
		return result;		
	}
	
	
	/**
	 *	设置其他属性
	 * @param vo
	 * @param planMap
	 * @param personMap
	 */
	private void setOtherAttribute(IndexProgramVo vo,Map<String, List<ProgramContingencyPlan>> planMap) {
		String id = vo.getId();
		if(StringUtils.isNotEmpty(id)&&StringUtils.isNotEmpty(planMap)&&planMap.containsKey(id)) {
			List<ProgramContingencyPlan> planList = planMap.get(id);
			if(StringUtils.isNotEmpty(planList)) {
				List<ProgramContingencyPlanVo> planVoList = new ArrayList<ProgramContingencyPlanVo>();
				for (int i = 0; i < planList.size(); i++) {
					ProgramContingencyPlan planObj = planList.get(i);
					ProgramContingencyPlanVo planVo = new ProgramContingencyPlanVo();
	    			BeanUtils.copyProperties(planObj, planVo); //赋予返回对象
					planVoList.add(planVo);
				}
				if(StringUtils.isNotEmpty(planVoList)) {
					vo.setPlanList(planVoList);
				}
			}
		}
	}
	
	
	/**
	 *	保存指标方案数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	@Override
	public String saveProgramIndicatorData(List<ProgramIndicator> addList,List<ProgramIndicator> updList,List<ProgramIndicator> delList) {
		String result = "";
		if ("".equals(result)&&StringUtils.isNotEmpty(addList)) {
        	if(entityService.insertBatch(addList, 500)==0) {
        		result = "指标方案添加失败！";
        	}
        }
		if ("".equals(result)&&StringUtils.isNotEmpty(updList)) {
        	if(entityService.updateByIdBatchIncludeNull(updList, 500)==0) {
        		result = "指标方案更新失败！";
        	}
        }
		if ("".equals(result)&&StringUtils.isNotEmpty(delList)) {
        	if(entityService.deleteByIdBatch(delList, 500)==0) {
        		result = "指标方案删除失败！";
        	}
        }
		return result;
	}
	
	
	/**
	 *	修改采集点保存时同步方案指标数据
	 * @param dotList
	 * @return
	 */
	@Override
	public String initProgramIndicatorByAddSampledot(List<Costunitsampledot> dotList) {
		String result = "";
		List<ProgramIndicator> initList = new ArrayList<ProgramIndicator>();
		List<String> pvidList = new ArrayList<String>();
		if(StringUtils.isNotEmpty(dotList)) {
			String unitCode = dotList.get(0).getUnitid();
			if(StringUtils.isNotEmpty(unitCode)) {
				pvidList = costService.getAllMaxProgramVersionList(unitCode);
			}
		}
		
		if(StringUtils.isNotEmpty(pvidList)) {
			//获取设置数据
			HashMap<String, Integer> maxPxMap = new HashMap<String, Integer>();
			IndexQueryDto setDto = new IndexQueryDto();
			setDto.setPvIdList(pvidList);
			Map<String, ProgramIndicator> setMap = new HashMap<String, ProgramIndicator>();
			List<ProgramIndicator> setList = this.getProgramIndicatorList(setDto);
			if(StringUtils.isNotEmpty(setList)) {
				for (int i = 0; i < setList.size(); i++) {
					ProgramIndicator setObj = setList.get(i);
					String pvId = setObj.getPvId();
					String iname = setObj.getIname();
					String key = pvId+"_"+iname;
					if(!setMap.containsKey(key)) {
						setMap.put(key, setObj);
					}
					int tmsort = setObj.getTmSort()==null?0:setObj.getTmSort();
					if(maxPxMap.containsKey(pvId)) {
						int maxPx = maxPxMap.get(pvId);
						if(tmsort>maxPx) {
							maxPxMap.put(pvId, tmsort);
						}
					}else {
						maxPxMap.put(pvId, tmsort);
					}
				}
			}
			
			for (int m = 0; m < pvidList.size(); m++) {
				String pvid = pvidList.get(m);
				for (int i = 0; i < dotList.size(); i++) {
					Costunitsampledot dotObj = dotList.get(i);
					int tmused = dotObj.getTmused()==null?0:dotObj.getTmused();
					if(tmused==1) {
						String name = dotObj.getName();
						String key = pvid+"_"+name;
						if(StringUtils.isEmpty(setMap)||!setMap.containsKey(key)) {
							int maxPx = 0;
							if(maxPxMap.containsKey(pvid)) {
								maxPx = maxPxMap.get(pvid);
							}
							maxPx += 1;
							ProgramIndicator indexObj = new ProgramIndicator();
							indexObj.setId(TMUID.getUID());
							indexObj.setPvId(pvid);
							indexObj.setIname(name);
							indexObj.setTmUsed(1);
							indexObj.setKeyUpLimit(dotObj.getIndexRangeUpper()); //指标范围上限
							indexObj.setKeyLowLimit(dotObj.getIndexRangeLower()); //指标范围下限
							indexObj.setTmSort(maxPx);
			    			initList.add(indexObj);
			    			setMap.put(key, indexObj);
			    			maxPxMap.put(pvid, maxPx);
						}
					}
				}
			}
		}
		if (StringUtils.isNotEmpty(initList)) { //初始化新记录，只保存主表即可（因为没有录入子数据）
			this.saveProgramIndicatorData(initList, null, null);
        }
		return result;
	}
	/**
	 *	获取计算指标合格限值公式（最新公式）
	 * @return
	 */
	@Override
	public ProgramIndexFormula getIndexFormulaObj() {
		ProgramIndexFormula result = new ProgramIndexFormula();
		List<ProgramIndexFormula> formulaList = this.getProgramIndexFormulaList();
		if(StringUtils.isNotEmpty(formulaList)) {
			result = formulaList.get(0);
		}
		return result;
	}
	
	
	/**
	 *	获取计算指标合格限值公式列表
	 * @return
	 */
	private List<ProgramIndexFormula> getProgramIndexFormulaList() {
		List<ProgramIndexFormula> result = new ArrayList<ProgramIndexFormula>();
		try {
			//检索条件
			Where where = Where.create();
			where.eq(ProgramIndexFormula::getTmUsed, 1);
			//排序
			Order order = Order.create();
			order.orderByDesc(ProgramIndexFormula::getLastEditTime);
			List<ProgramIndexFormula> list = entityService.queryData(ProgramIndexFormula.class, where, order, null);
			if(StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	
	/**
	 *	保存计算指标合格限值公式
	 * @param saveObj
	 * @return
	 */
	@Override
	public String saveIndexFormulaObj(ProgramIndexFormula saveObj) {
		String result = "";
		List<ProgramIndexFormula> addList = new ArrayList<ProgramIndexFormula>();
       	List<ProgramIndexFormula> updList = new ArrayList<ProgramIndexFormula>();
       	
		if (saveObj != null) {
			//已存在记录
			Map<String, ProgramIndexFormula> dataMap = new HashMap<String, ProgramIndexFormula>();
			List<ProgramIndexFormula> dataList = this.getProgramIndexFormulaList();
			if(StringUtils.isNotEmpty(dataList)) {
				dataMap = dataList.stream().collect(Collectors.toMap(ProgramIndexFormula::getId,Function.identity()));
			}
			String currDt = DateTimeUtils.getNowDateTimeStr();
			String saveId = saveObj.getId();
			if(StringUtils.isNotEmpty(saveId)&&StringUtils.isNotEmpty(dataMap)&&dataMap.containsKey(saveId)) { //修改
				ProgramIndexFormula dataObj = dataMap.get(saveId);
				BeanUtils.copyProperties(saveObj, dataObj); //赋予返回对象
				dataObj.setLastEditTime(currDt);
				updList.add(dataObj);
			}else { //新增
				ProgramIndexFormula dataObj = new ProgramIndexFormula();
				BeanUtils.copyProperties(saveObj, dataObj); //赋予返回对象
				if(StringUtils.isEmpty(saveId)) {
					dataObj.setId(TMUID.getUID());
				}
				dataObj.setTmUsed(1);
				dataObj.setLastEditTime(currDt);
				addList.add(dataObj);
			}
        }
		if ("".equals(result)) {
			result = this.saveDataProgramIndexFormula(addList, updList, null);
        }
		return result;
	}
	
	/**
	 *	保存计算指标合格限值公式数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	@Override
	public String saveDataProgramIndexFormula(List<ProgramIndexFormula> addList,List<ProgramIndexFormula> updList,List<ProgramIndexFormula> delList) {
		String result = "";
		if ("".equals(result)&&StringUtils.isNotEmpty(addList)) {
        	if(entityService.insertBatch(addList, 500)==0) {
        		result = "指标合格限值公式添加失败！";
        	}
        }
		if ("".equals(result)&&StringUtils.isNotEmpty(updList)) {
        	if(entityService.updateByIdBatchIncludeNull(updList, 500)==0) {
        		result = "指标合格限值公式更新失败！";
        	}
        }
		if ("".equals(result)&&StringUtils.isNotEmpty(delList)) {
        	if(entityService.deleteByIdBatch(delList, 500)==0) {
        		result = "指标合格限值公式删除失败！";
        	}
        }
		return result;
	}
	
}
