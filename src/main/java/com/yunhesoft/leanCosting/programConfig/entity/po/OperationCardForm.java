package com.yunhesoft.leanCosting.programConfig.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 *	方案下的工艺卡
 */
@Entity
@Setter
@Getter
@Table(name = "OPERATIONCARDFORM")
public class OperationCardForm extends BaseEntity {
	
    private static final long serialVersionUID = 1L;
    
    /** 方案版本ID */
    @Column(name="PVID", length=100)
    private String pvId;
    
    /** 表单ID */
    @Column(name="FORMID", length=100)
    private String formId;
        
    /** 是否使用：1、使用；0、不使用 */
    @Column(name="TMUSED")
    private Integer tmUsed;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmSort;
    
}
