package com.yunhesoft.leanCosting.steadyRate.scheduler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.steadyRate.service.ISteadyRateServise;
import com.yunhesoft.leanCosting.steadyRate.service.impl.OperatingConditionsService;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.multiTenant.entity.po.SysMultiTenant;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 工况识别调度
 * 
 * <AUTHOR>
 *
 */
@Component
@Log4j2
public class OperatingConditionsScheduleJobHandler {

	@Autowired
	private EntityService dao;
	@Autowired
	private OperatingConditionsService opcSrv;

	@XxlJob("tm4OperatingConditionsScheduleJobHandler")
	public void tm4OperatingConditionsScheduleJobHandler() throws Exception {
		String param = XxlJobHelper.getJobParam();
		//每几分钟执行一次
		if (MultiTenantUtils.enalbe()) {
			//开启多租户
			List<SysMultiTenant> list = dao.queryListDisableTenant(SysMultiTenant.class, Where.create().eq(SysMultiTenant::getTmused, 1), Order.create().orderByDesc(SysMultiTenant::getCreateTime));
			if (StringUtils.isEmpty(list)) {
				return;
			}
//			for (SysMultiTenant t : list) {
//				//多租户模式
//				opcSrv.operatingConditionsRecognize(t.getTenant_id());
//			}
			opcSrv.operatingConditionsRecognize(null);
		} else {
			opcSrv.operatingConditionsRecognize(null);
		}
	}


}