package com.yunhesoft.leanCosting.calcLogic;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.aliyuncs.utils.StringUtils;
import com.yunhesoft.leanCosting.deliverGoods.entity.po.ItemStockDayLedger;
import com.yunhesoft.leanCosting.deliverGoods.entity.po.ShiftItemEntryQty;
import com.yunhesoft.leanCosting.samplePlan.entity.po.ProdLimsResult;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

@Service
public class GetLimsDataServiceImpl implements IGetLimsDataService {

	@Autowired
	EntityService entityService;

	/**
	 * @category 保存LIMS数据
	 * @param data
	 * @return
	 */
	public String saveLimsData(List<ProdLimsResult> updata, List<ProdLimsResult> indata) {
		String rtn = "";
		if (indata != null && indata.size() > 0) {
			int bs = entityService.insertBatch(indata);
			if (bs > 0) {// 保存成功
				rtn = "";
			} else {
				rtn = "新增LIMS数据采集失败";
			}
		}
		if (updata != null && updata.size() > 0) {
			int bs = entityService.updateByIdBatch(updata);
			if (bs == 1) {
				rtn = rtn + "";
			} else {
				rtn = rtn + "更新LIMS数据采集失败";
			}
		}
		return rtn;
	}

	/**
	 * @category 获取LIMS数据
	 * @param dto
	 * @return
	 */
	public HashMap<String, List<LimsDataVo>> getLimsData(LimsDataDto dto) {
		HashMap<String, List<LimsDataVo>> rtn = new HashMap<String, List<LimsDataVo>>();
		List<LimsDataVo> limsyb = dto.getLimsyb();
		if (limsyb != null && limsyb.size() > 0) {
			String kssj = dto.getKssj();
			String jzsj = dto.getJzsj();
			// 获取给定时间段的LIMS数据：以采样时间为准
			Where where = Where.create();
			where.ge(ProdLimsResult::getSampledDate, kssj);
			where.le(ProdLimsResult::getSampledDate, jzsj);
			// 排序
			Order order = Order.create();
			order.orderByAsc(ProdLimsResult::getSampledDate);
			List<ProdLimsResult> limsdata = this.entityService.queryList(ProdLimsResult.class, where, order, null);
			if (limsdata != null) {
				String key1, key2, dotid, dotmc, dotbm;
				String zzmc, cp, cyd, xm, fx, delbs, sj, val;
				HashMap<String, List<ProdLimsResult>> dm = new HashMap<String, List<ProdLimsResult>>();
				HashMap<String, String> djm = new HashMap<String, String>();
				for (ProdLimsResult d : limsdata) {
					delbs = d.getTmused();
					if (delbs == null) {
						delbs = "f";
					}
					if ("t".equalsIgnoreCase(delbs)) {
						continue;// 已删除的数据不采集
					}
					zzmc = d.getProcessunit();
					if (zzmc == null) {
						zzmc = "";
					}
					cp = d.getProductName();
					if (cp == null) {
						cp = "";
					}
					cyd = d.getSamplingPoint();
					if (cyd == null) {
						cyd = "";
					}
					xm = d.getAnalysisName();
					if (xm == null) {
						xm = "";
					}
					fx = d.getItemName();
					if (fx == null) {
						fx = "";
					}
					key1 = (new StringBuffer(zzmc).append(".").append(cp).append(".").append(cyd).append(".").append(xm)
							.append(".").append(fx)).toString();
					sj = d.getSampledDate();
					key2 = (new StringBuffer(key1).append(".").append(sj)).toString();
					if (!djm.containsKey(key2)) {
						// 同一时间不能有两个结果
						djm.put(key2, "1");
						if (dm.containsKey(key1)) {
							List<ProdLimsResult> m = dm.get(key1);
							m.add(d);
							dm.put(key1, m);
						} else {
							List<ProdLimsResult> m = new ArrayList<ProdLimsResult>();
							m.add(d);
							dm.put(key1, m);
						}
					}
				}
				// 给返回数据赋值
				for (LimsDataVo j : limsyb) {
					dotid = j.getDotId();
					if (StringUtils.isEmpty(dotid)) {
						continue;
					}
					dotmc = j.getInstrumentName();
					if (StringUtils.isEmpty(dotmc)) {
						continue;
					}
					dotbm = j.getTagNumber();
					if (StringUtils.isEmpty(dotbm)) {
						continue;
					}
					zzmc = j.getProcessUnit();
					if (zzmc == null) {
						zzmc = "";
					}
					cp = j.getProductName();
					if (cp == null) {
						cp = "";
					}
					cyd = j.getSamplingPoint();
					if (cyd == null) {
						cyd = "";
					}
					xm = j.getAnalysisName();
					if (xm == null) {
						xm = "";
					}
					fx = j.getItemName();
					if (fx == null) {
						fx = "";
					}
					key1 = (new StringBuffer(zzmc).append(".").append(cp).append(".").append(cyd).append(".").append(xm)
							.append(".").append(fx)).toString();
					if (dm.containsKey(key1)) {
						List<ProdLimsResult> xd = dm.get(key1);
						if (xd != null) {
							for (ProdLimsResult jg : xd) {
								delbs = jg.getTmused();
								if (delbs == null) {
									delbs = "f";
								}
								if ("t".equalsIgnoreCase(delbs)) {
									continue;// 已删除的数据不采集
								}
								sj = jg.getSampledDate();
								val = jg.getFormattedEntry();
								if (val == null) {
									continue;
								}
								LimsDataVo vo = new LimsDataVo();
								vo.setProcessUnit(zzmc);
								vo.setProductName(cp);
								vo.setSamplingPoint(cyd);
								vo.setAnalysisName(xm);
								vo.setItemName(fx);
								vo.setDotId(dotid);
								vo.setInstrumentName(dotmc);
								vo.setTagNumber(dotbm);
								vo.setTime(sj);
								vo.setValue(val);
								if (rtn.containsKey(key1)) {
									List<LimsDataVo> l = rtn.get(key1);
									l.add(vo);
									rtn.put(key1, l);
								} else {
									List<LimsDataVo> l = new ArrayList<LimsDataVo>();
									l.add(vo);
									rtn.put(key1, l);
								}
							}
						}
					}
				}
			}
		}

		return rtn;
	}

	/**
	 * @category 保存仓储库存量同步数据
	 * @param indata
	 * @return
	 */
	@Override
	public String saveItemStockDayLedgerData(List<ItemStockDayLedger> indata) {
		String rtn = "";
		if (indata != null && indata.size() > 0) {
			int bs = entityService.insertBatch(indata);
			if (bs > 0) {// 保存成功
				rtn = "";
			} else {
				rtn = "新增仓储库存量数据失败";
			}
		}
		return rtn;
	}

	/**
	 * @category 保存仓储班次入库量
	 * @param indata
	 * @return
	 */
	@Override
	public String saveShiftItemEntryQtyData(List<ShiftItemEntryQty> indata) {
		String rtn = "";
		if (indata != null && indata.size() > 0) {
			int bs = entityService.insertBatch(indata);
			if (bs > 0) {// 保存成功
				rtn = "";
			} else {
				rtn = "新增仓储班次入库量数据失败";
			}
		}
		return rtn;
	}
}
